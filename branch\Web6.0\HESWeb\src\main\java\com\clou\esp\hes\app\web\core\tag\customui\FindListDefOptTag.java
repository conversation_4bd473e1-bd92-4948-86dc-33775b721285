package com.clou.esp.hes.app.web.core.tag.customui;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.tagext.Tag;
import javax.servlet.jsp.tagext.TagSupport;

/**
 * 文件名：FindListDefOptTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：列表默认操作项标签
 * 修改人：严浪
 * 修改时间：2017年3月20日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FindListDefOptTag extends TagSupport {
    /**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    protected String url;
    protected String title;
    private String exp;//判断链接是否显示的表达式
    private String operationCode;//按钮的操作Code
    private String icon;//样式
    private String langArg;
    
    public int doStartTag() throws JspTagException {
        return EVAL_PAGE;
    }
    public int doEndTag() throws JspTagException {
        Tag t = findAncestorWithClass(this, FindListTag.class);
        FindListTag parent = (FindListTag) t;
        parent.setDefOpt(url, operationCode, title, icon, exp);
        return EVAL_PAGE;
    }
    
    
    public void setUrl(String url) {
        this.url = url;
    }
    public void setTitle(String title) {
        this.title = title;
    }
    public void setExp(String exp) {
        this.exp = exp;
    }
    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }
    public void setIcon(String icon) {
        this.icon = icon;
    }
    public void setLangArg(String langArg) {
        this.langArg = langArg;
    }
    
}
