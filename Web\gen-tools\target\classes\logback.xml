<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n
            </Pattern>
        </layout>
    </appender>

    <logger name="org.springframework" level="debug" >
    </logger>

    <!-- <logger name="com.power7000.service.impl" level="debug" /> -->
   
       <logger name="com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException" level="debug" />

    
    　　<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender"> 
　　　　　　<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"> 
　　　　　　　　   <fileNamePattern>logFile.%d{yyyy-MM-dd}-%i.log</fileNamePattern> 
　　　　
             <maxHistory>30</maxHistory>
             <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
   				 <MaxFileSize>200MB</MaxFileSize>
   			 </TimeBasedFileNamingAndTriggeringPolicy>   
　　　　　　</rollingPolicy> 
　　　　　　<encoder> 
　　　　　　　　　<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-4relative [%thread] %-5level %logger{35} - %msg%n</pattern> 
　　　　　　</encoder> 
　　　</appender> 

　　　<root level="DEBUG"> 
 		 <appender-ref ref="STDOUT"/>
　　　　　　<appender-ref ref="FILE" /> 
　　　</root> 

</configuration>