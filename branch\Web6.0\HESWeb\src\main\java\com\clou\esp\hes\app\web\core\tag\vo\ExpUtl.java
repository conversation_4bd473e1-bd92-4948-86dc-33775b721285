package com.clou.esp.hes.app.web.core.tag.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * 文件名：ExpUtl.java
 * 版权：Copyright by Power7000g Team
 * 描述：
 * 修改人：严浪
 * 修改时间：2017年3月23日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class ExpUtl {
    //判断链接符号
    private List<String> jud=new ArrayList<String>();
    //表达式
    private List<FindListExp> expList=new ArrayList<FindListExp>();
    public List<String> getJud() {
        return jud;
    }
    public List<FindListExp> getExpList() {
        return expList;
    }
    public void setJud(List<String> jud) {
        this.jud = jud;
    }
    public void setExpList(List<FindListExp> expList) {
        this.expList = expList;
    }
    @Override
    public String toString() {
        return "ExpUtl [jud=" + jud + ", expList=" + expList + "]";
    }
    
   
    
    

}
