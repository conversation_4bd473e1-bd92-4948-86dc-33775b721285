package com.clou.esp.hes.app.web.core.shiro.filter;

import java.io.PrintWriter;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.WebUtils;

import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MacAddressUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.system.SysUtility;
import com.clou.esp.hes.app.web.service.system.SysUtilityService;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.encrypt.PasswordUtil;

/**
 * pc码校验
 * 
 * <AUTHOR>
 * 
 */
public class PcCodeOverdueFilter extends AccessControlFilter {

	private static String reason = "";

	private static String readme = "";
	@Resource
    private SysUtilityService 		sysUtilityService;
	
	@Override
	protected boolean isAccessAllowed(ServletRequest request,
			ServletResponse response, Object mappedValue) throws Exception {
		
			if(Globals.isDevelop){
				return Boolean.TRUE; 
			}
			
			if(Globals.pcCodeMap.get("pc-code") != null){
				readme = Globals.pcCodeMap.get("pc-code");
				return Boolean.TRUE;
			}else{
				List<SysUtility> sysUtilityList = sysUtilityService.getAllList();
		        if(sysUtilityList != null && sysUtilityList.size() > 0){
		        	if(StringUtils.isNotBlank(sysUtilityList.get(0).getReadme())){
		        		readme = sysUtilityList.get(0).getReadme();
		        	}else{
		    			reason =  MutiLangUtil.doMutiLang("login.pcCodeUnavailable");
		    			return Boolean.FALSE;
		        	}
		        }else{

	    			reason =  MutiLangUtil.doMutiLang("login.pcCodeUnavailable");
	    			return Boolean.FALSE;
	        	}
			}
	
    		String [] authArrays = readme.split("-");
    		if(authArrays.length == 4){
    			String mac = PasswordUtil.decrypt(authArrays[0], "root",// 解密密钥，root，写死
    					PasswordUtil.getStaticSalt());
    			String startDateStr = PasswordUtil.decrypt(authArrays[1], "root",// 解密密钥，root，写死
    					PasswordUtil.getStaticSalt());
    			String endDateStr = PasswordUtil.decrypt(authArrays[2], "root",// 解密密钥，root，写死
    					PasswordUtil.getStaticSalt());
    			String meterNum = PasswordUtil.decrypt(authArrays[3], "root",// 解密密钥，root，写死
    					PasswordUtil.getStaticSalt());
    			if(!mac.equals(MacAddressUtil.getMacAddress())){
    				reason = MutiLangUtil.doMutiLang("login.incorrectPcCode");
    				Globals.pcCodeMap.clear();
    				return Boolean.FALSE; 
    			}
    			
    			if(!isInteger(meterNum) || Integer.valueOf(meterNum) == 0){
    				reason = MutiLangUtil.doMutiLang("login.meterNumUnavailable");
    				Globals.pcCodeMap.clear();
    				return Boolean.FALSE; 
    	    	}
    			
    			Date now = new Date();
    			Date startDate =  DateUtils.parseDate(startDateStr, "yyyy-MM-dd HH:mm:ss");
    			Date endDate = DateUtils.parseDate(endDateStr, "yyyy-MM-dd HH:mm:ss");
    			if (startDate.getTime() > now.getTime()) {
    				reason =  MutiLangUtil.doMutiLang("login.startTimeNotEffect");
    				Globals.pcCodeMap.clear();
    				return Boolean.FALSE;
    			}else if (endDate.getTime() < now.getTime()) {
    				reason =  MutiLangUtil.doMutiLang("login.pcCodeOverdue");
    				Globals.pcCodeMap.clear();
    				return Boolean.FALSE;
    			}
    		}else{
    			reason =  MutiLangUtil.doMutiLang("login.pcCodeUnavailable");
    			Globals.pcCodeMap.clear();
    			return Boolean.FALSE;
    		}
    	
    		Globals.pcCodeMap.put("pc-code", readme);
	  
			return Boolean.TRUE;
	}

	@Override
	protected boolean onAccessDenied(ServletRequest request,
			ServletResponse response) throws Exception {
		// 先退出
		Subject subject = getSubject(request, response);
		subject.logout();
		WebUtils.getSavedRequest(request);
		
		toLogin(request, response);
		return Boolean.FALSE;
	}

	private void toLogin(ServletRequest request, ServletResponse response) {
		PrintWriter out = null;
		try {
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.println("<script type=\"text/javascript\">var r=confirm(\""+reason+"\");if (r==true){ window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\"; } else{ window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\"; }</script>");
		
			
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.flush();
				out.close();
			}
		}
	}
	
    
    public static boolean isInteger(String input){  
    	Matcher mer = Pattern.compile("^[0-9]+$").matcher(input);  
        return mer.find();  
    }  

}
