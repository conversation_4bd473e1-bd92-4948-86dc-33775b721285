package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: AssetLineManagementDao
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月3日 下午2:57:53
 *
 */
@MyBatisDao
public interface AssetLineManagementDao extends CrudDao<AssetLine> {
	List<AssetLine> findLinesForJqGrid(JqGridSearchTo jqGridSearchTo);
	List<AssetCalcObj> findCalObjectsForJqGrid(JqGridSearchTo jqGridSearchTo);
	List<AssetCalcObj> getListForCalObj(AssetCalcObj assetCalcObj);
	Integer updateCalcObj(AssetCalcObj pojo);
	Integer saveCalcObj(AssetCalcObj pojo);
	Integer deleteCalcObj(AssetCalcObj entity);
	AssetCalcObj getCalcObj(AssetCalcObj entity);
	
	List<AssetCalcObjMap> findCalObjectMapsForJqGrid(JqGridSearchTo jqGridSearchTo);
	Integer updateCalcObjMap(AssetCalcObjMap pojo);
	Integer saveCalcObjMap(AssetCalcObjMap pojo);
	Integer deleteCalcObjMap(AssetCalcObjMap entity);
	AssetCalcObjMap getCalcObjMap(AssetCalcObjMap entity);
	
	Integer deleteCalcObjByEntityId(AssetCalcObj entity);
	
	List<AssetMeter> queryMeterSn(AssetMeter meter);
}
