/*
 * 鏂囦欢鍚嶏細TaskDlms.java
 * 鐗堟潈锛欳opyright by Power7000 Team
 * 鎻忚堪锛�
 * 淇敼浜猴細jybai
 * 淇敼鏃堕棿锛�2017骞�12鏈�22鏃�
 * 璺熻釜鍗曞彿锛�
 * 淇敼鍗曞彿锛�
 * 淇敼鍐呭锛�
 */

package clouesp.hes.core.schedules.Third;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import clouesp.hes.common.IntervalCalc.IntervalCalcController;
import clouesp.hes.common.asset.CalcObj;
import clouesp.hes.common.asset.Communicator;
import clouesp.hes.common.asset.Communicators;
import clouesp.hes.common.asset.DataItemInfo;
import clouesp.hes.common.asset.DataItemInfos;
import clouesp.hes.common.asset.DataitemMap;
import clouesp.hes.common.asset.DataitemMaps;
import clouesp.hes.common.asset.DictDeviceModel;
import clouesp.hes.common.asset.DictDeviceModels;
import clouesp.hes.common.asset.MeasurementGroup;
import clouesp.hes.common.asset.Meters;
import clouesp.hes.common.asset.MeasurementGroups;
import clouesp.hes.common.asset.Meter;
import clouesp.hes.common.asset.ProfileDetail;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerLevel;
import clouesp.hes.common.mqbus.MQBus;
import clouesp.hes.common.mqbus.MQMessage;
import clouesp.hes.common.mqbus.MQMessageType;
import clouesp.hes.common.mqbus.MQTopicQueueType;
import clouesp.hes.common.mqbus.ServiceType;
import clouesp.hes.common.storage.Data;
import clouesp.hes.common.storage.DataController;
import clouesp.hes.common.storage.DataProgress;
import clouesp.hes.common.storage.DataProgressController;
import clouesp.hes.common.storage.MissDataController;
import clouesp.hes.common.task.Task;
import clouesp.hes.common.task.TaskScheduler;
import clouesp.hes.common.task.TaskType;
import clouesp.hes.core.common.protocol.dlms.ProtocolModuleDlms;
import clouesp.hes.core.common.protocol.dlms.ProtocolPacketDlms;
import clouesp.hes.core.common.protocol.dlms.asn1.ASN1Sequence;
import clouesp.hes.core.common.protocol.dlms.asn1.ASN1Type;
import clouesp.hes.core.common.protocol.dlms.asn1.CosemData;
import clouesp.hes.core.common.protocol.dlms.asn1.CosemDataType;
import clouesp.hes.core.common.protocol.dlms.cosem.CosemObisItem;
import clouesp.hes.core.common.protocol.dlms.cosem.DataAccessResult;
import clouesp.hes.core.common.protocol.dlms.cosem.SelectiveAccessDescriptor;
import clouesp.hes.core.common.protocol.utils.FrameSeqManager;
import clouesp.hes.core.common.protocol.utils.NormalUtils;
import clouesp.hes.core.schedules.Configuration;
import clouesp.hes.core.schedules.ScheduleUtil;
import clouesp.hes.core.schedules.utils.DataUtils;
import oracle.net.aso.e;

public class TaskPullDataDlmsItron extends Task {
   private  boolean isDebug = false ;
    private enum TaskState {
        TS_INIT, TS_REQ, TS_GET_NEXT_BLOCK, TS_RSP, TS_TMO, TS_FIN
    };  
    
    private TaskState taskState = TaskState.TS_INIT;
    
    private  Meter meter = null;

    private MeasurementGroup measurementGroup = null;
    private List<ProfileDetail> profileDetails = null;
    private List<DataItemInfo> dlmsDataItemInfos = null;
    
    private DataProgress dataProgress = null;
    
    CosemObisItem cosemObisItem = null;
    LinkedList<ASN1Sequence> blockList = new LinkedList<ASN1Sequence>();
     
    MQMessage reqMessage = null;
    long lastReqTime = 0;
    int reqTimes = 0; 
    int failedCount = 0;
    private String failedInfo = null;
    private boolean isNoData = false;
    
    private int maxPoints = 1;
    private int backPoints = 1;
   
    private Date reqStartTime = null;
    private Date reqEndTime = null;
    
    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    private Communicator communicator = null;
    private int cancelCode = 0;
    private int aarqCount = 0;
    
    private int invokeId = 1;
    
    boolean bMeterRspError = false;
    private String[] profileIds;
    private int profilePos = 0;
    private Date progressDate = null;
    private List<Data> storagedatas = new ArrayList<Data>();
    private boolean initResult = true;
      
    private static Date tv = null;
    List<Date> tvSet = new ArrayList<Date>();
    
    private Date taskDate = null;
    private Date initTime = null;
    List<Date> tvSetEnd = new ArrayList<Date>();
    Date lastTv = null;
    
    private Map<String, List<String>> obisMap;
    
    public TaskPullDataDlmsItron(TaskType taskType, TaskScheduler scheduler, String taskId, int slot,
                            String meterId, String profileId) {
        super(taskType, scheduler, taskId, slot, null, meterId, profileId);

        this.taskDate = new Date();
        meter = Meters.getInstance().getMeter(meterId);
        this.communicatorId = meter.getCommunicatorId();
        communicator = Communicators.getInstance().getCommunicator(communicatorId);
        
        profileIds =  profileId.split(";");
        for (int i = 0; i < profileIds.length; i++) {
	        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId,
	            meter.getId(), "Task",
					"Task[Task id=" + id + ",Profile id=" + profileIds[i] + "]" + " created(" + (i +1) + " / " + profileIds.length + ")" );
        }
        String[] parentObiss = {
        		"0.0.98.133.61.255",//Maximum Import active demand T1
        		"0.0.98.133.62.255",//Maximum Import active demand T2
        		"0.0.98.133.63.255",//Maximum Import active demand T3
        		"0.0.98.133.64.255",//Maximum Import reactive demand T1
        		"0.0.98.133.65.255",//Maximum Import reactive demand T2
        		"0.0.98.133.66.255",//Maximum Import reactive demand T3
        		"0.0.98.133.67.255",//Maximum export active demand T1
        		"0.0.98.133.68.255",//Maximum export active demand T2
        		"0.0.98.133.69.255",//Maximum export active demand T3
        		"0.0.98.133.70.255",//Maximum export reactive demand T1
        		"0.0.98.133.71.255",//Maximum export reactive demand T2
        		"0.0.98.133.72.255" //Maximum export reactive demand T3
        		};
        
        String[] obiss = {
        		"4#1.1.1.6.1.255#2",
        		"4#1.1.1.6.1.255#5",
           		"4#1.1.1.6.2.255#2",
        		"4#1.1.1.6.2.255#5",
           		"4#1.1.1.6.3.255#2",
        		"4#1.1.1.6.3.255#5",
           		"4#1.1.3.6.1.255#2",
        		"4#1.1.3.6.1.255#5",
        		"4#1.1.3.6.2.255#2",
        		"4#1.1.3.6.2.255#5",
        		"4#1.1.3.6.3.255#2",
        		"4#1.1.3.6.3.255#5",
        		"4#1.1.2.6.1.255#2",
        		"4#1.1.2.6.1.255#5",
        		"4#1.1.2.6.2.255#2",
        		"4#1.1.2.6.2.255#5",
        		"4#1.1.2.6.3.255#2",
        		"4#1.1.2.6.3.255#5",
        		"4#1.1.4.6.1.255#2",
        		"4#1.1.4.6.1.255#5",
          		"4#1.1.4.6.2.255#2",
        		"4#1.1.4.6.2.255#5",
          		"4#1.1.4.6.3.255#2",
        		"4#1.1.4.6.3.255#5"
        		};
        obisMap = new HashMap<String, List<String>>(); //用于需量解析
        int index = 0;
        for (String parentObis : parentObiss) {
        	List<String> obisList = new ArrayList<String>();
        	obisList.add(obiss[index++]);
        	obisList.add(obiss[index++]);
        	obisMap.put(parentObis, obisList);
        } 
    }
   
    private int init(){

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    	failedInfo = null;
    	progressDate = null;
    	cosemObisItem = null;
    	blockList.clear();
    	this.setProfileId(profileIds[profilePos]);
        if(meter == null){ 
        	initResult =  false;
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, "0",
                "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " failed init casued by failed to get meter id[" + meterId + "]");             
            return -1;
        } 
        
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId,
            meter.getId(), "Task",
            "Task[Task id=" + id + ",Profile id=" + getProfileId() + "]" + " init");            
   
        measurementGroup = MeasurementGroups.getInstance().getMeasurementDetail(
            meter.getMgGroupId(), getProfileId());

        if (measurementGroup == null){
        	initResult =  false;
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
                "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " failed init casued by failed to get measurement group");                      
            return -1;          
        }  
        
		if (dataProgress == null) {

			String tmpReqStartTime = "null" ;
			String tmpEndStartTime = "null" ;
			dataProgress = DataProgressController.getInstance().getDataProgress(meter.getId(), getProfileId());
			if (dataProgress == null) {
				reqStartTime = generateReqStartTime(new Date(), true);
				if ( reqEndTime != null ) tmpReqStartTime = sdf.format(reqStartTime) ;
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId,
						meter.getId(), "Task",
						"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]" + " The progress is empty, and the current time is used as the progress time. reqStartTime = " + tmpReqStartTime );
			} else {
				reqStartTime = generateReqStartTime(dataProgress.getTv(), true);
				String prgTv = "null" ;
				if ( dataProgress.getTv() != null ) prgTv = sdf.format(dataProgress.getTv()) ;
				if ( reqStartTime != null ) tmpEndStartTime = sdf.format(reqStartTime) ;
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId,
						meter.getId(), "Task",
						"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]" + "Load progress time from Db ,  reqStartTime = " + tmpEndStartTime + ", ProgressTv = " + prgTv  );

			}

			if (reqStartTime == null) {
				initResult =  false;
				String prgTv = "null" ;
				if ( dataProgress.getTv() != null ) prgTv = sdf.format(dataProgress.getTv()) ;
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task",
						"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]"
								+ " Request start time exceed the reqStartTime = null , ProgressTv = " + prgTv  );
				return -1;
			}

			reqEndTime = generateReqEndTime();
			if (reqEndTime == null) {
				initResult =  false;
				String prgTv = "null" ;
				if ( dataProgress.getTv() != null ) prgTv = sdf.format(dataProgress.getTv()) ;
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task",
						"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]"
								+ " Request end time exceed the reqEndTime = null , ProgressTv = " + prgTv  );
				return -1;
			}
		}
		        
        String freqType = measurementGroup.getProfileFreqType();
        freqType = freqType.toLowerCase();
        
        if (!"monthly".equals(freqType)) {  	
            profileDetails = measurementGroup.getProfileDetails();
            if(profileDetails == null) {
            	initResult =  false;
                Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
                    "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " failed init casued by failed to get profile detail");             
                return -1;
            }
            
            if(profileDetails.size() == 0) {
            	initResult =  false;
                Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
                    "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " failed init casued by failed to get profile detail");               
                return -1; 
            }        	
        	
	        dlmsDataItemInfos = new ArrayList<DataItemInfo>();
	        for(ProfileDetail profileDetail : profileDetails){
	            DataItemInfo dlmsDataItemInfo = DataItemInfos.getInstance().getDlmsDataItemInfo(profileDetail.getInnerCode());
	            if(dlmsDataItemInfo == null)
	                continue;
	            dlmsDataItemInfos.add(dlmsDataItemInfo);
	        }    
	        if(dlmsDataItemInfos.size() != profileDetails.size()){
	        	initResult =  false;
	            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
	                "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " failed init casued by failed to get profile detail");                          
	            return -1; 
	        }
        }
        
        if (dataProgress == null) {
            dataProgress = new DataProgress();
            dataProgress.setDeviceId(meter.getId());
            dataProgress.setProfileId(getProfileId());
            dataProgress.setTaskState(0);
            dataProgress.setTv(Timestamp.valueOf(sdf.format(reqStartTime.getTime())));
			DataProgressController.getInstance().putDataProgress(dataProgress);
        } 
		if (initTime == null) {
			initTime = reqStartTime;
			// if (freqType.equalsIgnoreCase("minutely")) {
			// 	Calendar cal = Calendar.getInstance();
			// 	cal.setTime(initTime);
			// 	cal.set(Calendar.MINUTE, 10);
			// 	initTime = cal.getTime();
			// }
		}
        return 0;
    }
  
    private Date generateReqStartTime(Date time, boolean next) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
    //    System.out.println("time = " + sdf.format(time));
        int freq = measurementGroup.getProfileFreq();
        String freqType = measurementGroup.getProfileFreqType();
        freqType = freqType.toLowerCase();

        if (freqType.equalsIgnoreCase("minutely")) {
            if (dataProgress != null) {
                if (next) {
                	cal.add(Calendar.MINUTE, -freq);
                	cal.add(Calendar.DAY_OF_MONTH, 1);
                }
            }
            else{         
            	cal.add(Calendar.DAY_OF_MONTH, -1);
            }       
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0); 
            cal.set(Calendar.SECOND, 0);  
            cal.set(Calendar.MILLISECOND, 0);  
            
            if (System.currentTimeMillis() - cal.getTime().getTime() < 24 * 3600 * 1000) {
            	return null;
            } 
        }
        else if (freqType.equalsIgnoreCase("monthly")) {
            maxPoints = 12;
            if (dataProgress != null) {
                if (next) cal.add(Calendar.MONTH, freq);
            }
            else
                cal.add(Calendar.MONTH, -(freq * backPoints));
            
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);             
        }
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        
        if (cal.getTime().getTime() >= System.currentTimeMillis())
            return null;
 
        return cal.getTime();
    }
    
    private Date generateReqEndTime(){
        if(reqStartTime == null)
            return null;
        if(reqStartTime.getTime() >= System.currentTimeMillis())
            return null;
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(reqStartTime);
        int freq = measurementGroup.getProfileFreq();
        String freqType = measurementGroup.getProfileFreqType(); 
        
        int points = DataUtils.getPoints(cal.getTime(), freq, freqType);
        if (points > maxPoints) points = maxPoints;
        if(points <= 0)
            points = 1;        
        
        if (freqType.equalsIgnoreCase("minutely")) {
        	cal.add(Calendar.DAY_OF_MONTH, 1); 	
        }
        else if (freqType.equalsIgnoreCase("monthly")) {
            cal.add(Calendar.MONTH, freq * (points - 1));
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));  
            cal.set(Calendar.HOUR_OF_DAY, 23);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
        }
         
        if(cal.getTime().getTime() < reqStartTime.getTime())
            return null;
 
        return cal.getTime();
    }
    
    public int execute(MQMessage message){
    	try {
	        cancelCode = preProcessMsg(message);
	        if(cancelCode == -100){
	            cancelCode = 0;
	            return 0;
	        }
	        
	        switch(taskState){          
	            case TS_INIT:
	                if (init() == 0)
	                    taskState = TaskState.TS_REQ;
	                else
	                    taskState = TaskState.TS_FIN;
	                break;
	            case TS_REQ:
	                if (request() == 0)
	                    taskState = TaskState.TS_RSP;//已经发送请求，进入等待响应帧状态
	                else
	                    taskState = TaskState.TS_FIN;//没有需要抄读的数据，结束任务                 
	                break; 
	                
	            case TS_GET_NEXT_BLOCK:        
	                if(requestNext() == 0)
	                    taskState = TaskState.TS_RSP;//已经发送请求，进入等待响应帧状态
	                else
	                    taskState = TaskState.TS_FIN;//没有需要抄读的数据，结束任务                   
	                break;                            
	            case TS_RSP:
	                if (message != null) {
	                    reqTimes = 0;
	                    int ret = handlerResponse(message);
	                    switch (ret) {
	                        case 0: 
	                        case 3:
	                            int retp = 0;                           
	                            if(ret == 0)
	                                retp = processData(); 
	                            else if(ret == 3)
	                                retp = processNoData();
	                            
	                            if(retp == 1)
	                                taskState = TaskState.TS_FIN;
	                            else{       
	                                failedProcess();                                
	                            }
	                            cosemObisItem.resultData = new CosemData();
	                            blockList.clear();
	                            break;
	                        case 1:
	                            taskState = TaskState.TS_GET_NEXT_BLOCK;
	                            break;
	                        case 2:
	                            taskState = TaskState.TS_FIN;
	                            break;
	                        case -20:
	                        	break;
	                        default:                        
	                            failedProcess();                           
	                            break;
	                    }
	                }
	                else{
	                    int ret = checkReqState();
	                    if (ret == 0)
	                        taskState = TaskState.TS_TMO;
	                    else if(ret == 1) {
	                        taskState = TaskState.TS_FIN;  
	                    }
	                }
	                break; 
	       
	            case TS_TMO:
	                rerequest();
	                taskState = TaskState.TS_RSP;
	                break;                    
	            default:
	                break;
	        }            
	
	        if (taskState == TaskState.TS_FIN) 
	        	done();
    	}
    	catch(Exception e) {
    		e.printStackTrace();
    		taskState = TaskState.TS_FIN;
    		failedInfo = "Error: " + e.getMessage();
    	}

        return 0;
    }
   
    private int preProcessMsg(MQMessage message){
        int preCode = 0;
        if(message != null){
            if(message.getMessageId() == null
            || reqMessage == null
            || !message.getMessageId().equals(reqMessage.getMessageId())){
            	String deviceSn = null;
            	String deviceType = null;
				String reqMessageId = null ;
				if ( reqMessage != null ) reqMessageId = reqMessage.getMessageId() ;
            	if(message.getFromType() == ServiceType.ST_METER){
            		deviceType = "meter";
            		Meter fromMeter = Meters.getInstance().getMeter(message.getFromId());
            		if(fromMeter != null)
            			deviceSn = fromMeter.getSn();
            	}
            	else if(message.getFromType() == ServiceType.ST_COMMUNICATOR){
            		deviceType = "communicator";
            		Communicator communicator = Communicators.getInstance().getCommunicator(message.getFromId());
            		if(communicator != null)
            			deviceSn = communicator.getSn();
            	}
           	
                Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
                "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " Error message[ReqMsgId = " + reqMessageId + " , RevMsgId = " + message.getMessageId() + " , MsgType = " + message.getMessageType().name() + " , Device sn=" + deviceSn + ", Device type="  + deviceType + ", To id="  + message.getToId() + "]");
                return -100;
            } 
            else if(message.getMessageType() == MQMessageType.MST_CHANNEL_CONN_FAILED
                || message.getMessageType() == MQMessageType.MST_CHANNEL_BUSY
                || message.getMessageType() == MQMessageType.MST_COSEM_ASSOCIATE_FAILED
                || message.getMessageType() == MQMessageType.MST_PACKET_DECRYPT_FAILED
                || message.getMessageType() == MQMessageType.MST_METER_COMM_FAILD
                || message.getMessageType() == MQMessageType.MST_METER_READ_FAILD
                || message.getMessageType() == MQMessageType.MST_DCU_NOT_METER
                ||  message.getMessageType() == MQMessageType.MST_AARQ_REDO
                ) {
                
                reqTimes = 0;
                
                if(message.getMessageType() == MQMessageType.MST_CHANNEL_CONN_FAILED)
                    failedInfo = "offline";
                else if(message.getMessageType() == MQMessageType.MST_CHANNEL_BUSY)
                    failedInfo = "busy";
                else if(message.getMessageType() == MQMessageType.MST_COSEM_ASSOCIATE_FAILED)
                    failedInfo = "associate"; 
                else if(message.getMessageType() == MQMessageType.MST_PACKET_DECRYPT_FAILED)
                    failedInfo = "decrypt";  
                else if(message.getMessageType() == MQMessageType.MST_METER_COMM_FAILD){
                    preCode = -2;
                    failedInfo = "meter is offline";  
                }
                else if(message.getMessageType() == MQMessageType.MST_METER_READ_FAILD)
                    failedInfo = "faild read meter"; 
                else if(message.getMessageType() == MQMessageType.MST_DCU_NOT_METER){
                    preCode = -3;
                    failedInfo = "no meter in dcu"; 
                }
                else if(message.getMessageType() == MQMessageType.MST_AARQ_REDO){
                	if(aarqCount > 1)
                		failedInfo = "aarq redo";
                	else{
                		taskState = TaskState.TS_TMO;
                		reqTimes = 0;
						Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, 
								meter.getId(), "Task", "TaskId:[" + id + "]" + ", ODR:[AARQ REDO]");                		
                	}
                    
                    aarqCount++;
                }
                if(aarqCount > 2 || message.getMessageType() != MQMessageType.MST_AARQ_REDO)
                	taskState = TaskState.TS_FIN;
            }
        } 
        return preCode;
    }
    
    private void failedProcess(){
        failedCount++; 
        if(failedCount < Configuration.dataFailedLimit){
            rerequest();
            taskState = TaskState.TS_RSP;
        }
        else
            taskState = TaskState.TS_FIN;         
    }
    
    private int getFromIndex() {
    	int fromIndex = 0;
		int freq = measurementGroup.getProfileFreq();
	    String freqType = measurementGroup.getProfileFreqType();
	    freqType = freqType.toLowerCase();
	    Date curTime = new Date();
	    long span = curTime.getTime() - reqStartTime.getTime();
	   	    
	    switch (freqType) {
	    case "minutely":
	    {
	    	fromIndex = (int)(span / (freq * 60 * 1000)) + 2;
	    	break;
	    }
	    case "hourly":
	    {
	    	fromIndex = (int)(span / (freq * 3600 * 1000)) + 1;
	    	break;
	    }
	    case "daily":
	    {
	    	fromIndex = (int)(span / (freq * 24 * 3600 * 1000)) + 1;
	    	break;
	    }
	    case "monthly":
	    {
	    	Calendar calStart = Calendar.getInstance();
	    	calStart.setTime(reqStartTime);
	    	Calendar calCur = Calendar.getInstance();
	    	fromIndex = (calCur.get(Calendar.YEAR) - calStart.get(Calendar.YEAR)) * 12 
	    	+ (calCur.get(Calendar.MONTH) - calStart.get(Calendar.MONTH)) + 1;
	    
	    	break;
	    }
    	default:
    		break;
	    }
		return fromIndex;
    }
    
    private int request() {
        if (measurementGroup == null) 
        	return -1;

        String protocolCode = measurementGroup.getProtocolCode();
        if (protocolCode == null){
        	Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Protocol code is null");      	
        	return -1;
        }

        String[] params = protocolCode.split("#");

        if (params == null || params.length != 3){
        	Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Error protocol code[" +  protocolCode + "]");      	
        	return -1;
        }
		String freqType = measurementGroup.getProfileFreqType();
		freqType = freqType.toLowerCase();
        if (cosemObisItem == null) {
            cosemObisItem = new CosemObisItem();
            cosemObisItem.classId = Integer.parseInt(params[0]);
            cosemObisItem.attributeId = Integer.parseInt(params[2]);
            
            if (!"monthly".equals(freqType)) {
            	cosemObisItem.instanceId = params[1];
            	cosemObisItem.accessSelector = 1;
            }  
            
            cosemObisItem.resultData = new CosemData();
        }
   
		if ("monthly".equals(freqType)) {
			int fromIndex = getFromIndex();
			if (fromIndex > 12) {
				fromIndex = 12;
			}
			cosemObisItem.instanceId = params[1].replace("*", String.valueOf(100 + fromIndex));
		} else {
	        SelectiveAccessDescriptor descriptor = new SelectiveAccessDescriptor();  
	        descriptor.selectByPeriodOfTime(sdf.format(reqStartTime), sdf.format(reqEndTime));
	        cosemObisItem.data.assignValue(descriptor.getParameter());
		}
  
        ProtocolPacketDlms packet = null;
        String deviceId = meter.getId();
		ServiceType sendType = ServiceType.ST_METER;
		int destAddr = 1;
		if (communicator.getDeviceType() == 203) {
			destAddr = 0;
			sendType = ServiceType.ST_COMMUNICATOR;
			deviceId = meter.getCommunicatorId();
		} else if (communicator.getDeviceType() == 204) {
			String ldn = meter.getMac();
			destAddr = Integer.parseInt(ldn.substring(ldn.length() - 4, ldn.length())) + 0x80;

		} else {
			DictDeviceModel dictDeviceModel = DictDeviceModels.getInstance().getDictDeviceModel(meter.getModel());
			if (dictDeviceModel != null && dictDeviceModel.getDestAddr() != null) {
				destAddr = dictDeviceModel.getDestAddr();
			}

		}
		packet = new ProtocolPacketDlms(meter.getSrcAddr(), destAddr);        
        invokeId = FrameSeqManager.getInstance().nextInvokeId(meter.getId());
        
        if (ProtocolModuleDlms.getInstance().encodeGet(cosemObisItem, packet, invokeId) == 0) {
        	
    		byte[] sendPacket = packet.getPacketBuffer();       	
            reqMessage = new MQMessage(Configuration.hostId, ServiceType.ST_SCHEDULER, UUID.randomUUID().toString(),
                MQMessageType.MST_SCHEDULER_TASK_REQ, sendPacket, deviceId,
                sendType, Configuration.channelHostId, ServiceType.ST_CHANNEL, meter.getId(),
                ServiceType.ST_METER);
            
            Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task", "Request[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]");  
            Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Packet", "Send[" + sendPacket.length + "]: " + NormalUtils.bytesToHexString(sendPacket));             
            MQBus.getInstance().putSend(MQTopicQueueType.QUEUE_REQ_SCHEDULER, reqMessage);
         
            lastReqTime = System.currentTimeMillis();
            reqTimes++;
            return 0;
        }
        
        Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Request[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " Packet encode get exception"); 

        return -1;
    }
    
	public int requestNext() {
		ProtocolPacketDlms packet = null;
        String deviceId = meter.getId();
        ServiceType sendType = ServiceType.ST_METER;
		if (communicator.getDeviceType() == 203) {
			packet = new ProtocolPacketDlms(meter.getSrcAddr(), 0);
        	sendType = ServiceType.ST_COMMUNICATOR;
        	deviceId = meter.getCommunicatorId();			
		} 
		else if(communicator.getDeviceType() == 204) {
			String ldn = meter.getMac();
			int destAddr = Integer.parseInt(ldn.substring(ldn.length() - 4, ldn.length())) + 0x80;
			packet = new ProtocolPacketDlms(meter.getSrcAddr(), destAddr);
        }
		else
			packet = new ProtocolPacketDlms(meter.getSrcAddr());

		invokeId = FrameSeqManager.getInstance().nextInvokeId(meter.getId());
		int ret = ProtocolModuleDlms.getInstance().encodeGetNext(invokeId, blockList.size(), packet);
		if (ret < 0) {
			Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task",
					"Request Next[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) + ",End="
							+ sdf.format(reqEndTime) + "]" + " Packet encode get exception");
			return -1;
		}

		reqMessage = new MQMessage(Configuration.hostId, ServiceType.ST_SCHEDULER, UUID.randomUUID().toString(),
				MQMessageType.MST_SCHEDULER_TASK_REQ, packet.getPacketBuffer(), deviceId, sendType,
				Configuration.channelHostId, ServiceType.ST_CHANNEL, meter.getId(), ServiceType.ST_METER);

		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task",
				"Request Next[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) + ",End="
						+ sdf.format(reqEndTime) + "]");
		Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Packet", "Send["
				+ packet.getPacketBuffer().length + "]: " + NormalUtils.bytesToHexString(packet.getPacketBuffer()));

		MQBus.getInstance().putSend(MQTopicQueueType.QUEUE_REQ_SCHEDULER, reqMessage);

		lastReqTime = System.currentTimeMillis();
		reqTimes++;

		return 0;
	}
    
    private int rerequest() {
        if(reqMessage == null) {
        	request();
            return 0;
        }
        
        byte[] data = (byte[])reqMessage.getPayload();
	            
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task", "Rerequest[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]");  
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Packet", "Send[" + data.length + "]: " + NormalUtils.bytesToHexString(data));        
        
        MQBus.getInstance().putSend(MQTopicQueueType.QUEUE_REQ_SCHEDULER, reqMessage);
        lastReqTime = System.currentTimeMillis();
        reqTimes++;
        return 0;
    }
    
    private int checkReqState(){
        if(System.currentTimeMillis() - lastReqTime > Configuration.tcpClientTimeout){
            if(reqTimes > Configuration.tcpClientReqTimes)
                return 1;
            return 0;
        }
        return 2;
    }
       
    private int handlerResponse(MQMessage message){
        reqTimes = 0;
     
        if (message == null){
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " MQ Message is not expected");      
            return -1;
        }
        Object payload = message.getPayload();
        if (payload == null || !(payload instanceof byte[])){
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " Packet is null"); 
            return -1;
        }
        
        byte[] buf = (byte[])payload;

        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Packet", "Recv[" + buf.length + "]: " + NormalUtils.bytesToHexString(buf));     
        
        ProtocolPacketDlms packet = new ProtocolPacketDlms(1);
        packet.setBufferPacket(buf, buf.length);
        int ret = 0;
        ret = packet.decode();  
        if (ret < 0){
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Packet", "Recv[" + buf.length + "]: " + NormalUtils.bytesToHexString(buf)); 
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " Packet decode exception");             
            return -1;
        }
        
        byte[] apdu = packet.getDataAreaBuffer();        
        int packetInvokeId = apdu[2] & 0x0F;
        if (packetInvokeId != invokeId){
        	 CosemObisItem item = new  CosemObisItem();
        	 ret = ProtocolModuleDlms.getInstance().decodeGet(packet.getDataAreaBuffer(), item, blockList);
        	 if (ret != 1) {
					if(null == item.resultData) {
						bMeterRspError = true;
						failedInfo = DataAccessResult.parseResult(item.resultCode).toString();
				        Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Packet", "Recv[" + buf.length + "]: " + NormalUtils.bytesToHexString(buf)); 
				        Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " exception:[" + failedInfo + "]");             
						return -1;
					}
			}      	
        	
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Packet", "Recv[" + buf.length + "]: " + NormalUtils.bytesToHexString(buf)); 
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " Invoke id mismatch");             
            return -20;
        }        
        
        try{
            ret = ProtocolModuleDlms.getInstance().decodeGet(packet.getDataAreaBuffer(), cosemObisItem, blockList);
        }
        catch(Exception e){
        	failedInfo = e.getMessage();
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, "0", "System",  e.getMessage() + "[Meter SN:" + meter.getSn() + "]"); 
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", e.getMessage());             
            ret = -1;
        }
       
        if(ret < 0 
        	&& cosemObisItem.resultCode != 13
        	&& cosemObisItem.resultCode != 250){
			bMeterRspError = true;
			failedInfo = DataAccessResult.parseResult(cosemObisItem.resultCode).toString();       	
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Packet", "Recv[" + buf.length + "]: " + NormalUtils.bytesToHexString(buf)); 
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " " + failedInfo);             
            return -1;
        }
        else {
			if (cosemObisItem.resultCode == 13 
				|| cosemObisItem.resultCode == 250
				|| (cosemObisItem.resultData != null
					&& cosemObisItem.resultData.isArray() 
					&& cosemObisItem.resultData.getArray().length == 0)) {
				isNoData = true;
				Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task",
						"Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) + ",End="
								+ sdf.format(reqEndTime) + "]" + " No data");
				if (DataUtils.isExpired(reqEndTime, measurementGroup.getProfileFreqType())) {
					return 3;
				} else
					return 2;
			}
        }
        return ret;
    }
           
	private int processNoData() {	
		profilePos = profileIds.length - 1;
		
		Calendar cal = Calendar.getInstance();
		cal.setTime(reqEndTime);
		String freqType = measurementGroup.getProfileFreqType();
		freqType = freqType.toLowerCase();
		
		if (freqType.equalsIgnoreCase("minutely")) {
			cal.set(Calendar.MINUTE, 0);
		} else if (freqType.equalsIgnoreCase("daily")) {
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 0);
		} else if (freqType.equalsIgnoreCase("monthly")) {
			cal.set(Calendar.DAY_OF_MONTH, 1);
			cal.set(Calendar.HOUR_OF_DAY, 0);
			cal.set(Calendar.MINUTE, 0);
		}
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		progressDate = cal.getTime();  
		
        return 1;        
    }

    private int processData() {
        CosemData resultData = cosemObisItem.resultData;
        if(resultData == null){
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " The data is null");           
            return -1;
        }
        if (!resultData.isArray()) {
            Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime) +",End=" + sdf.format(reqEndTime) + "]" + " The data type is wrong");            
            return -1; 
        }
        CosemData[] arrays = resultData.getArray();
        tvSet.clear();
        
        String freqType = measurementGroup.getProfileFreqType();
        freqType = freqType.toLowerCase();
		if ("monthly".equals(freqType)) {
			for (CosemData array : arrays) {
				tv = null;
				processData(array);
			}
		} else {
			tv = null;
			for (CosemData array : arrays) {
				processDataMinutely(array);
			}
		}
        
        if (tvSet.size() == 0)
            return -1; 
        Collections.sort(tvSet, new Comparator<Date>() {
            public int compare(Date arg0, Date arg1) {
                if (arg0.getTime() > arg1.getTime())
                    return 1;
                else if (arg0.getTime() < arg1.getTime())
                    return -1;
                else
                    return 0;
            }
        });       
        progressDate = tvSet.get(tvSet.size() - 1);
        
		if (profilePos == profileIds.length - 1) {
			tvSetEnd.addAll(tvSet);
		}
        
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task", "Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(tvSet.get(0)) +",End=" + sdf.format(tvSet.get(tvSet.size() - 1)) + "]" + " succeed");         
        return 1;
    }
        
    private boolean processDemandData(String obisCode, CosemData cosemData) {
    	if (tv == null || obisCode == null || !obisMap.containsKey(obisCode)) {
    		return false;
    	}

		boolean isDebugInfo = false ;
		if ( isDebug ){

			System.out.println("processDemandData:  parentObisCode =" + obisCode);
			if ("0.0.98.133.61.255".equals(obisCode))  {
				isDebugInfo = true ;
			}

		}
//    	System.out.println("parentObisCode: " + obisCode);
    	
    	CosemData[] arrDatas = cosemData.getArray();
    	if (arrDatas.length == 0) {

			if ( isDebugInfo ) System.out.println("processDemandData: arrDatas.length = 0");
    		return false;
    	}
    	CosemData arrData = arrDatas[0];
    	
		if (!arrData.isStructure()) {
			if ( isDebugInfo ) System.out.println("processDemandData: !arrData.isStructure()");
			return false;
		}
		ASN1Type[] members = arrData.getStructure().getMembers();
		if (members.length != 3) {
			if ( isDebugInfo ) System.out.println("processDemandData: members.length != 3 , " + members.length );
			return false;
		}
		int index = 0;
		int obisi = 0;
		List<String> obisList = obisMap.get(obisCode);
		if ( isDebugInfo ) System.out.println("processDemandData: obisList.size() =  " + obisList.size() );
		for (ASN1Type member : members) {
			index++;
			if (index == 2) {
				continue;
			}
			String tmpObis = obisList.get(obisi++) ;
			DataitemMap dataitemMap = getDataitemMap(tmpObis);
			if (dataitemMap == null) {
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, Configuration.serviceId, meter.getId(),
						"Task", "Response[Profile id=" + getProfileId() + "]" +
								"dict_dataitem_map is not configured( ParentObisCode = " + obisCode + " ): OBIS =  " + tmpObis  );

				if ( isDebugInfo ) System.out.println("processDemandData: dataitemMap == null , tmpObis =  " + tmpObis + " , index = " + index );

				continue;
			}
			DataItemInfo dtItemInfo = DataitemMaps.getInstance().getDataitemMapbyKey(dataitemMap.getId(),
					meter.getModel());
			if ( isDebugInfo ) System.out.println("processDemandData:  dtItemInfo.getDataitemId() =  " + dtItemInfo.getDataitemId() +  " , index = " + index );
			String value = DataUtils.parseData(dtItemInfo.getParseType(), dtItemInfo.getScale(),
					dtItemInfo.getShowUnit(), (CosemData) member);	
    		Data storageData = new Data();
				storageData.setId(meter.getId());
				storageData.setTv(tv);
				storageData.setUpdateTv(new Date());
				storageData.setDataItemId(dtItemInfo.getDataitemId());
				storageData.setValue(value);
        		storagedatas.add(storageData);

			if ( isDebugInfo ) System.out.println("processDemandData:  dtItemInfo.getDataitemId() =  " + dtItemInfo.getDataitemId() + dataitemMap.getName() + "[" + sdf.format(tv) + "]:" + value +   " , index = " + index );
//        	System.out.println(dataitemMap.getName() + "[" + sdf.format(tv) + "]:" + value);	
        		
        		
		}
    	return true;
    }
    
    private void processData(CosemData cosemData) {
    	if (cosemData.isArray()) {
    		CosemData[] arrDatas = cosemData.getArray();
    		for (CosemData arrData : arrDatas) {
    			processData(arrData);
    		}
    		
    	} else if (cosemData.isStructure()) {
    		ASN1Type[] members = cosemData.getStructure().getMembers();
    		String obisCode = null;
    		for (ASN1Type member : members) {
    			CosemData memberData = (CosemData) member;
				if (memberData.isArray() || memberData.isStructure()) {
					if (processDemandData(obisCode, memberData)) {
						continue;
					}
					processData(memberData);
				}
				else {
	    			if (memberData.getTagValue() == CosemDataType.OCTET_STRING) {
	    				if (memberData.getOctetString().length == 6) {
	    					obisCode = convertObisCode(memberData.getOctetString());
							if ( isDebug ){
								System.out.println("ItronInfo: obisCode = " + obisCode );
								if ("0.0.98.133.61.255".equals(obisCode)){
									System.out.println("ItronInfo: Maximum Demand Register 1 -Active energy import(+A) rate 1 (Monthly) [Unit: kW])") ;
								}
							}
	    					continue;
	    				}
	    			}
	    			if (obisCode == null) {
	    				continue;
	    			}
	    			
					if (memberData.getTagValue() == CosemDataType.OCTET_STRING
						&& memberData.getOctetString().length == 12
						&& "1.0.98.134.1.255".equals(obisCode)) {
						try {
							tv = memberData.getDateTime().getDate();
						} catch(Exception e) {
							
						}
						if (tv == null) {
							tv = reqStartTime;
						}
						Calendar cal = Calendar.getInstance();
	    				cal.setTime(tv);
	    				cal.set(Calendar.DAY_OF_MONTH, 1);
	    				cal.set(Calendar.HOUR_OF_DAY, 0);
	    				cal.set(Calendar.MINUTE, 0);
	    				cal.set(Calendar.SECOND, 0);
	    				cal.set(Calendar.MILLISECOND, 0);
	    				tv = cal.getTime();
	    				
						if (tv.getTime() < reqStartTime.getTime() || tv.getTime() > reqEndTime.getTime()) {
							Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
									"Task",
									"Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime)
											+ ",End=" + sdf.format(reqEndTime) + "]" + " The time out period["
											+ sdf.format(tv) + "]");
							tv = null;
							break;
						}
						tvSet.add(tv);
						continue;
					}
									
	    			if (tv == null) {
	    				continue;
	    			}
	    			
	    			if (memberData.getTagValue() == 0) {
	    				continue;
	    			}
	    				
	    			DataitemMap dataitemMap = getDataitemMap(obisCode); //映射关系都存在DICT_DATAITEM_MAP表（不会用到DICT_DATAITEM表，和以前的定义有所不同）
	    			if (dataitemMap == null) {	 
//	    				System.out.println(obisCode);
	    				Logger.getInstance().writeLogInfo(LoggerLevel.WARN  , Configuration.serviceId, 
	    						meter.getId(), "Task", "Not find obis code in dict_dataitem_map[" + obisCode + "]");
	    				continue;
	    			}
	    			
	    			DataItemInfo dtItemInfo = DataitemMaps.getInstance().getDataitemMapbyKey(dataitemMap.getId(), meter.getModel());   			
	    			String	value = DataUtils.parseData(
	    					dtItemInfo.getParseType(),
							dtItemInfo.getScale(),
							dtItemInfo.getShowUnit(),
	    					memberData);	
	    			
//	    			System.out.println(
//	    					"[" + dtItemInfo.getDataitemId() + "][" 
//	    					+ "[" + obisCode + "][" 
//	    					+ dataitemMap.getName() + "]["
//	    					+ memberData.getTagValue() + "]:"
//	    					+ value);
	    			
	    			Logger.getInstance().writeLogInfo(LoggerLevel.INFO  , Configuration.serviceId, 
    						meter.getId(), "Task", "Find obis code[" + obisCode + "]");
	    			
	        		Data storageData = new Data();
					storageData.setId(meter.getId());
					storageData.setTv(tv);
					storageData.setUpdateTv(new Date());
					storageData.setDataItemId(dtItemInfo.getDataitemId());
					storageData.setValue(value);
	        		storagedatas.add(storageData);
	        		
				}
    		}
    	} 	
    }
    
    private void processDataMinutely(CosemData cosemData) {
    	int freq = measurementGroup.getProfileFreq();
      	if (cosemData.isArray()) {
    		CosemData[] arrDatas = cosemData.getArray();
    		for (CosemData arrData : arrDatas) {
    			processDataMinutely(arrData);
    		}
    		
    	} else if (cosemData.isStructure()) {
    		ASN1Type[] members = cosemData.getStructure().getMembers();
    		//int index = -1 ;
			int dataItemIndex = 0; // 用于数据项的索引，不包括时间字段	 
    		for (ASN1Type member : members) {
				//index++;
    			CosemData memberData = (CosemData)member;
    			if (memberData.getTagValue() == 0) {
    				continue;
    			}
    			if (memberData.isStructure() || memberData.isArray()) {
    				processDataMinutely(memberData);
    				continue;
    			}
    			if (memberData.getTagValue() == CosemDataType.OCTET_STRING
    				&& memberData.getOctetString().length == 12 ) {
						byte[] by = memberData.getValue();
						Calendar cal = Calendar.getInstance();
						if (tv == null){							
								for (int i = 6; i < 9; i++) {
									if (by[i] != (byte)0xFF) {
										continue;
									}
									by[i] = (byte)0x00;
								}	
								memberData.setValue(by);
								tv = memberData.getDateTime().getDate();	
								//System.out.println("tv0 = " + sdf.format(tv));							
								cal.setTime(tv);
								int minute = (int)Math.floor(cal.get(Calendar.MINUTE) / freq) * freq;
								cal.set(Calendar.MINUTE, minute);
								cal.set(Calendar.SECOND, 0);
								cal.set(Calendar.MILLISECOND, 0);
								cal.add(Calendar.MINUTE, freq);  // 向后推一个freq时长
								tv = cal.getTime();
								if (tv.getTime() < reqStartTime.getTime() || tv.getTime() > reqEndTime.getTime() + freq * 60 * 1000) {
									Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
											"Task",
											"Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime)
													+ ",End=" + sdf.format(reqEndTime) + "]" + " The time out period["
													+ sdf.format(tv) + "]");
									tv = null;
									break;
								}
						  }  else { 													 
							   // System.out.println("tv1 = " + sdf.format(tv));
								if ( by[1] == (byte)0xFF && by[2] == (byte)0xFF && by[3] == (byte)0xFF && by[4] == (byte)0xFF    
										&& by[6] != (byte)0xFF && by[7] != (byte)0xFF && by[8] != (byte)0xFF ) {

										  cal.setTime(tv);
										  by[1] = 	(byte )(((cal.get(Calendar.YEAR) & 0xFF00 ) >> 8) & 0xFF);
										  by[2] = 	(byte )(cal.get(Calendar.YEAR) & 0xFF);
										  by[3] = 	(byte )((cal.get(Calendar.MONTH) + 1 ) & 0xFF);;
										  by[4] = 	(byte )(cal.get(Calendar.DAY_OF_MONTH) & 0xFF);;	
										  memberData.setValue(by);	
										  tv = memberData.getDateTime().getDate();	
										 // System.out.println("tv2 = " + sdf.format(tv));
										  cal.setTime(tv);
										  cal.set(Calendar.SECOND, 0);
								          cal.set(Calendar.MILLISECOND, 0);
										  int minute = (int)Math.ceil((double)cal.get(Calendar.MINUTE) / freq) * freq;  // 改为向上取整
										  cal.set(Calendar.MINUTE, minute);
										  tv = cal.getTime();
										//   System.out.println("tv3 = " + sdf.format(tv));							 						         
								}  
						}					
    			} else if ((memberData.getTagValue() == CosemDataType.LONG ||  memberData.getTagValue() == CosemDataType.UNSIGNED_LONG  )
				 && dataItemIndex +1 < dlmsDataItemInfos.size()  ) {
    				
    				// Calendar cal = Calendar.getInstance();
    				// cal.setTime(tv);
					// if ( cal.get(Calendar.HOUR_OF_DAY) == 6 ){
					// 	System.out.println("ItronInfo: 6 ");
					// } 
    				// if ((storagedatas.size() % (dlmsDataItemInfos.size() - 1)) == 0) {
    				// 	// cal.add(Calendar.MINUTE, freq * (tvSet.size()));
    				// 	 tvSet.add(cal.getTime());
                    //     Calendar calNew = Calendar.getInstance();
                    //     calNew.setTime(tv);
                    //     calNew.add(Calendar.MINUTE, freq );
					// 	tv = calNew.getTime();
    				// }  
    				// else {
    				// 	cal.add(Calendar.MINUTE, freq * (tvSet.size() - 1));
    				// }
					if ( tv == null ) {
						Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(),
											"Task",
											"Response[Profile id=" + getProfileId() + ",Start=" + sdf.format(reqStartTime)
													+ ",End=" + sdf.format(reqEndTime) + "]" + " The time is null");  
						 continue;
					}
    				
				//	if (dataItemIndex < dlmsDataItemInfos.size()  ) {//

					DataItemInfo dtItemInfo = dlmsDataItemInfos.get(dataItemIndex+1); //曲线数据项第一个仍然配置时间数据项，但时间数据项不用作解析，所以第0个索引要跳过。
					DataItemInfo dtItemMap = DataitemMaps.getInstance()
							.getDataitemMapbyKey(dtItemInfo.getDataitemId(), meter.getModel());
					if (dtItemMap != null) {
						dtItemInfo = dtItemMap;
					}
											
					String strValue = DataUtils.parseData(dtItemInfo.getParseType(), dtItemInfo.getScale(),
							dtItemInfo.getShowUnit(), memberData);
					Data storageData = new Data();
					storageData.setId(meter.getId());
					storageData.setTv(tv);
					storageData.setUpdateTv(new Date());
					storageData.setDataItemId(dtItemInfo.getDataitemId());
					storageData.setValue(strValue);
					storagedatas.add(storageData);
					if ( !tvSet.contains(tv)) {
						tvSet.add(tv);
					}						
					//}
    				dataItemIndex++; 
				if (  dataItemIndex +1  >=  dlmsDataItemInfos.size() ) {
						Calendar cal = Calendar.getInstance();
						cal.setTime(tv);
						cal.add(Calendar.MINUTE, freq );
						tv = cal.getTime();
					}
    			}
    		}
    	} 
    }   
    
    private  String convertObisCode(byte[] obisString) {
		StringBuffer obisBuf = new StringBuffer();
		for (int i = 0; i < obisString.length; i++) {
			obisBuf.append( (obisString[i] & 0xFF)).append(".");
		}
		obisBuf.deleteCharAt(obisBuf.length()-1);
		String obis = obisBuf.toString();

		return obis;
	}
    
    private DataitemMap getDataitemMap(String obisCode) {
    	List<DataitemMap> dataitemMaps = DataitemMaps.getInstance().getDataitemMapsByModel(meter.getModel());
    	if (dataitemMaps != null) {
    		String freqType = measurementGroup.getProfileFreqType();
    		freqType = freqType.toLowerCase();
    		
    		for (DataitemMap dataitemMap : dataitemMaps) {
    			if ("monthly".equals(freqType) && !dataitemMap.getId().startsWith("13.")) {
    				continue;
    			}
    			else if ("daily".equals(freqType) && !dataitemMap.getId().startsWith("11.")) {
    				continue;
    			}  
    			else if ("minutely".equals(freqType) && !dataitemMap.getId().startsWith("0.")) {
    				continue;
    			}   			
    			
    			String code = dataitemMap.getProtocolCode();
    			if (code == null)
    				continue;
    			if (code.indexOf(obisCode) != -1) {
    				return dataitemMap;
    			}
    		}
    	}
    	return null;
    }
      
    private String getCancelCause(){
        String cause = "";
        if(cancelCode == -1)
            cause = "timeout";
        else if(cancelCode == -2)
            cause = "meter is offline";
        else if(cancelCode == -3)
            cause = "no meter in dcu";   
        return cause;
    }    
    
	private int done() {
		int state = 0;
	
		if (reqTimes > Configuration.tcpClientReqTimes || failedCount >= Configuration.dataFailedLimit
				|| failedInfo != null) {
			if (!bMeterRspError) {
				if (reqTimes > Configuration.tcpClientReqTimes) {
					failedInfo = "timeout";
				}
				if (failedCount >= Configuration.dataFailedLimit) {
					failedInfo = "decode exception";
				}
			}
			if (failedInfo.equals("decode exception") || failedInfo.equals("offline") || failedInfo.equals("associate")
					|| failedInfo.equals("decrypt") || failedInfo.equals("meter is offline")
					|| failedInfo.equals("faild read meter") || failedInfo.equals("no meter in dcu")
					|| failedInfo.equals("aarq redo") || bMeterRspError)
				Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task",
						"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]" + " failed casued by "
								+ failedInfo);
			else
				Logger.getInstance().writeLogInfo(LoggerLevel.WARN, Configuration.serviceId, meter.getId(), "Task",
						"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]" + " failed casued by "
								+ failedInfo);
			if (dataProgress != null) {
				dataProgress.setTaskState(state);
				dataProgress.setLastTaskTv(Timestamp.valueOf(sdf.format(new Date())));
				dataProgress.setFailedInfo(failedInfo);
				for(String profileId : profileIds){
					DataProgress dataProgress0 = new DataProgress() ;
					dataProgress0.setTaskState(dataProgress.getTaskState());
					dataProgress0.setLastTaskTv(dataProgress.getLastTaskTv());
					dataProgress0.setProfileId(profileId);
					dataProgress0.setTv(dataProgress.getTv());
					dataProgress0.setFailedInfo(dataProgress.getFailedInfo());
					dataProgress0.setDeviceId(dataProgress.getDeviceId());
					DataProgressController.getInstance().putDataProgress(dataProgress0);
				}
			}
			
			
		} else {

			Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task",
					"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]" + " succeed ");

			if (isNoData) {
				failedInfo = "No data in meter";
			} else {
				state = 1;
			}
		}

		if (initResult && dataProgress == null) {
			Logger.getInstance().writeLogInfo(LoggerLevel.ERROR, Configuration.serviceId, meter.getId(), "Task",
					"Task[Task id=" + id + ",Profile id=" + getProfileId() + "]"
							+ " Failed create data schdule progress");
		}
		
		if (profilePos == profileIds.length - 1 && initResult && progressDate != null) {
			if (storagedatas.size() > 0) {
				Collections.sort(storagedatas, new Comparator<Data>() {
					public int compare(Data data0, Data data1) {
						if (data0.getTv().getTime() > data1.getTv().getTime())
							return 1;
						else if (data0.getTv().getTime() < data1.getTv().getTime())
							return -1;
						else
							return 0;
					}
				});

				DataController.getInstance().pushDatas(storagedatas);

				if (Configuration.enableInterval == 1) {
					IntervalCalcController.getInstance().pushDatas(measurementGroup.getProfileFreqType(),
							measurementGroup.getProfileFreq(), storagedatas);
				}
				storagedatas.clear();
			}
		}

		boolean fin = true;
				
		if (initResult && progressDate != null) {
			if (profilePos == profileIds.length - 1) {
				
				if (dataProgress != null) {
					lastTv = progressDate;
					dataProgress.setTv(Timestamp.valueOf(sdf.format(progressDate)));
					dataProgress.setTaskState(state);
					dataProgress.setLastTaskTv(Timestamp.valueOf(sdf.format(new Date())));
					dataProgress.setFailedInfo(failedInfo);
					for(String profileId : profileIds){
						DataProgress dataProgress0 = new DataProgress() ;
						dataProgress0.setTaskState(dataProgress.getTaskState());
						dataProgress0.setLastTaskTv(dataProgress.getLastTaskTv());
						dataProgress0.setProfileId(profileId);
						dataProgress0.setTv(dataProgress.getTv());
						dataProgress0.setFailedInfo(dataProgress.getFailedInfo());
						dataProgress0.setDeviceId(dataProgress.getDeviceId());
						DataProgressController.getInstance().putDataProgress(dataProgress0);
					}
				}					
				
				reqStartTime = generateReqStartTime(progressDate, true);
				if (reqStartTime != null) {
					reqEndTime = generateReqEndTime();
					if (reqEndTime != null) {
						profilePos = 0;			
						taskState = TaskState.TS_INIT;
						fin = false;
					}
				}
			} else {
				profilePos++;
				taskState = TaskState.TS_INIT;
				fin = false;
			}
		}

		if (!fin) {
			return 0;
		}
		
		if (lastTv != null) {
			int freq = measurementGroup.getProfileFreq();
			String freqType = measurementGroup.getProfileFreqType();
			freqType = freqType.toLowerCase();				
			MissDataController.getInstance().checkAndProcMissData(
				meter.getId(), 
				profileIds[0], 
				initTime, 
				lastTv, 
				freq, 
				freqType, 
				taskDate, 
				state, 
				failedInfo, 
				tvSetEnd);
		}				

		for (String profileId : profileIds) {
			Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(), "Task",
					"Task[Task id=" + id + ",Profile id=" + profileId + "]" + " completed");
		}

		if (scheduler != null) {
			if (reqTimes > Configuration.tcpClientReqTimes)
				cancelCode = -1;
			if (cancelCode < 0) {
				String cause = getCancelCause();
				LoggerLevel level = LoggerLevel.WARN;
				if (cancelCode != -1)
					level = LoggerLevel.ERROR;
				List<Task> tasks = scheduler.getTasks(slot, meter.getId());
				for (Task t : tasks) {
					if (t == this)
						continue;
					ScheduleUtil.getInstance().processCancel(t, cause, level);
				}
				scheduler.removeTask(slot, meter.getId());
			} else
				scheduler.removeTask(slot, this);
		}
		return 0;
	}
 
    public int cancelTask(String cause, LoggerLevel level){
        if(init() == 0){
            Logger.getInstance().writeLogInfo(level, Configuration.serviceId, meter.getId(),
                "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " cancelled casued by " + cause);                  
            
            if (dataProgress != null){
                dataProgress.setTaskState(0);
                dataProgress.setLastTaskTv(Timestamp.valueOf(sdf.format(new Date())));
                dataProgress.setFailedInfo(cause);
				for(String profileId : profileIds){
					DataProgress dataProgress0 = new DataProgress() ;
					dataProgress0.setTaskState(dataProgress.getTaskState());
					dataProgress0.setLastTaskTv(dataProgress.getLastTaskTv());
					dataProgress0.setProfileId(profileId);
					dataProgress0.setTv(dataProgress.getTv());
					dataProgress0.setFailedInfo(dataProgress.getFailedInfo());
					dataProgress0.setDeviceId(dataProgress.getDeviceId());
					DataProgressController.getInstance().putDataProgress(dataProgress0);
				}
            }
        }
        
        Logger.getInstance().writeLogInfo(LoggerLevel.INFO, Configuration.serviceId, meter.getId(),
            "Task", "Task[Task id=" + id +",Profile id="+ getProfileId() +"]" + " completed");               
        return 0;
    }
}
