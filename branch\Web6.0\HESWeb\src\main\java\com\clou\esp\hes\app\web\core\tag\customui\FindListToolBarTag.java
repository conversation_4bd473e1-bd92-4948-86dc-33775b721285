package com.clou.esp.hes.app.web.core.tag.customui;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.tagext.Tag;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;

/**
 * 文件名：FindListToolBarTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：查询列表展示查询工具条标签
 * 修改人：严浪
 * 修改时间：2017年3月1日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FindListToolBarTag  extends TagSupport {

    /**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    
    protected String url;
    protected String title;
    private String width="60%";//弹出窗宽度
    private String height="90%";//弹出窗高度
    private String funname;//自定义函数名称
    private String icon;//图标
    private String onclick;
    private String operationCode;//按钮的操作Code
    private String langArg;//按钮的操作Code
  
    public int doStartTag() throws JspTagException {
        return EVAL_PAGE;
    }
    public int doEndTag() throws JspTagException {
    	title=MutiLangUtil.doMutiLang(title, langArg);
        Tag t = findAncestorWithClass(this, FindListTag.class);
        FindListTag parent = (FindListTag) t;
        parent.setToolBar(url, operationCode, title, icon, width, height, funname, onclick);
        return EVAL_PAGE;
    }
    
    
    public String getLangArg() {
        return langArg;
    }
    public void setUrl(String url) {
        this.url = url;
    }
    public void setTitle(String title) {
        this.title = title;
    }
    public void setFunname(String funname) {
        this.funname = funname;
    }
    public void setIcon(String icon) {
        this.icon = icon;
    }
    public void setOnclick(String onclick) {
        this.onclick = onclick;
    }
    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }
    public void setLangArg(String langArg) {
        this.langArg = langArg;
    }
 
    public void setWidth(String width) {
        this.width = width;
    }
    public void setHeight(String height) {
        this.height = height;
    }
    
    
    
    

}
