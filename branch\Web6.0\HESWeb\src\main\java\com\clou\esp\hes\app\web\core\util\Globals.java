package com.clou.esp.hes.app.web.core.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

/**
 * 全局变量
 * 
 * <AUTHOR>
 * 
 */
public class Globals {
	// 租户系统配置参数KEY
	/**
	 * 系统版本号
	 */
	public static final String HESWEB_SYSTEM_VERSION="HESWEB_SYSTEM_VERSION";
	/**
	 * 当前登录用户session获取key值
	 */
	public static final String SESSION_DEVICE_TYPE = "SESSION_DEVICE_TYPE";
	/**
	 * 登录错误次数
	 */
	public static final String LOGIN_ERROR_LIMIT_COUNT = "LOGIN_ERROR_LIMIT_COUNT";
	/**
	 * JAVA时间格式
	 */
	public static final String JAVA_TIME_FORMAT = "JAVA_TIME_FORMAT";
	/**
	 * JS时间格式
	 */
	public static final String JS_TIME_FORMAT = "JS_TIME_FORMAT";
	/**
	 * 钱符号
	 */
	public static final String MONEY_SYMBOL = "MONEY_SYMBOL";
	/**
	 * 登录logo
	 */
	public static final String SYSTEM_LOGIN_LOGO = "SYSTEM_LOGIN_LOGO";
	/**
	 * 合作伙伴登录logo
	 */
	public static final String PARTNER_LOGIN_LOGO = "PARTNER_LOGIN_LOGO";
	/**
	 * 系统主页logo
	 */
	public static final String SYSTEM_HOME_LOGO = "SYSTEM_HOME_LOGO";
	/**
	 * 系统主页logo
	 */
	public static final String PARTNER_HOME_LOGO = "PARTNER_HOME_LOGO";
	/**
	 * 售电logo
	 */
	public static final String VENDING_LOGO = "VENDING_LOGO";
	/**
	 * 售电公司名称
	 */
	public static final String VENDING_COMPANY_NAME = "VENDING_COMPANY_NAME";
	/**
	 * 售电名称
	 */
	public static final String VENDING_MAKE = "VENDING_MAKE";

	/**
	 * 防止重复提交requesttoken
	 */
	public static final String SESSION_AVOID_DUPLICATE_TOKEN = "SESSION_AVOID_DUPLICATE_TOKEN";
	/**
	 * 当前登录用户session获取key值
	 */
	public static final String SESSION_LOGIN_SYS_USER = "SESSION_LOGIN_SYS_USER";

	/**
	 * 数据权限获取orgId
	 */
	public static final String REQUESST_DATA_AUTH_ORG_ID = "REQUESST_DATA_AUTH_ORG_ID";
	/**
	 * 数据权限获取orgId
	 */
	public static final String REQUESST_DATA_AUTH_ORG_DATA_CODE = "REQUESST_DATA_AUTH_ORG_DATA_CODE";

	/**
	 * 数据权限获取ventorId
	 */
	public static final String REQUESST_DATA_AUTH_VENDOR_ID = "REQUESST_DATA_AUTH_VENDOR_ID";
//	/**
//	 * 登陆界面
//	 */
//	public static final String LOGIN_WEB_STYLE = ResourceUtil
//			.getConfigByName("login.web.style");
//	/**
//	 * 是否开发模式
//	 */
//	public static final boolean isDevelopment = Boolean
//			.parseBoolean(ResourceUtil.getConfigByName("isDevelopment"));
	/**
	 * 系统请求异常i18n
	 */
	public static final String SYSTEM_REQEXC = "system.reqexc";
	
	/**
	 * dot符号
	 */
	public static final String DOT = ".";

	public static final String MDM = "MDM";

	public static final String HES = "HES";
	public static final Map<String,String> pcCodeMap = new HashMap<>();
	
	public static final boolean isDevelop = (StringUtils.isNotBlank(System.getenv("DEV")) && "CS".equals(System.getenv("DEV"))) ? true : false;
	
	//public static final String PROTOCOL_ID = ResourceUtil.getSessionattachmenttitle("protocol.id");
}
