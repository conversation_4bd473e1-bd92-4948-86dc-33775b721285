package com.clou.esp.hes.app.web.core.util;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import com.clou.esp.hes.app.web.model.report.AssetReportTemplate;

public class ReadReportFile {
	/*
     * 读取指定路径下的文件名和目录名
     */
    public static List<AssetReportTemplate> getFileList() {
    	List<AssetReportTemplate> list =new ArrayList();
        File file = new File("D:\\ureportfiles");
      
        File[] fileList = file.listFiles();
        
        for (int i = 0; i < fileList.length; i++) {
            if (fileList[i].isFile()) {
            	AssetReportTemplate assetReportTemplate = new AssetReportTemplate();
                String fileName = fileList[i].getName();
                System.out.println("文件：" + fileName);    
                assetReportTemplate.setId(fileName);
                assetReportTemplate.setReportName(fileName);
                list.add(assetReportTemplate);
            }

        }
        return list;
    }
}
