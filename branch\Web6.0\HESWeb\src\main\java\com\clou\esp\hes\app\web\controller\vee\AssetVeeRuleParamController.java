/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRuleParam{ } 
 * 
 * 摘    要： assetVeeRuleParam
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:38:24
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.vee.AssetVeeRuleParam;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleParamService;

/**
 * <AUTHOR>
 * @时间：2019-03-04 09:38:24
 * @描述：assetVeeRuleParam类
 */
@Controller
@RequestMapping("/assetVeeRuleParamController")
public class AssetVeeRuleParamController extends BaseController{

 	@Resource
    private AssetVeeRuleParamService assetVeeRuleParamService;

	/**
	 * 跳转到assetVeeRuleParam列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/assetVeeRuleParamList");
    }

	/**
	 * 跳转到assetVeeRuleParam新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetVeeRuleParam")
	public ModelAndView assetVeeRuleParam(AssetVeeRuleParam assetVeeRuleParam,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(assetVeeRuleParam.getId())){
			try {
                assetVeeRuleParam=assetVeeRuleParamService.getEntity(assetVeeRuleParam.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("assetVeeRuleParam", assetVeeRuleParam);
		}
		return new ModelAndView("/vee/assetVeeRuleParam");
	}


	/**
	 * assetVeeRuleParam查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetVeeRuleParamService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetVeeRuleParam信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetVeeRuleParam assetVeeRuleParam,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetVeeRuleParamService.deleteById(assetVeeRuleParam.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetVeeRuleParam信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetVeeRuleParam assetVeeRuleParam,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetVeeRuleParam t=new  AssetVeeRuleParam();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetVeeRuleParam.getId())){
        	t=assetVeeRuleParamService.getEntity(assetVeeRuleParam.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetVeeRuleParam, t);
				assetVeeRuleParamService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetVeeRuleParamService.save(assetVeeRuleParam);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}