package com.clou.esp.hes.app.web.core.tag.vo;

/**
 * 文件名：FindListColumn.java
 * 版权：Copyright by Power7000g Team
 * 描述：
 * 修改人：严浪
 * 修改时间：2017年3月20日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FindListColumn {
    /**数据库对于字段名称*/
    private String name;
    /**列标题*/
    private String title;
    /**国家key*/
    private String langArg;
    /**设置列的css。多个class之间用空格分隔，如：'class1 class2' 。表格默认的css属性是ui-ellipsis*/
    private String classes;
    /**是否隐藏*/
    private boolean hidden=false;
    /**是否查询*/
    private boolean query=false;
    /**靠左靠右居中left right center*/
    private String align;
    /**默认列的宽度，只能是象素值，不能是百分比*/
    private Integer width;
    /**时间格式*/
    private String formatter;   //"Y-m-d H:i:s";
    /**自定义方法*/
    private String formatterjs;
    /**是否排序*/
    private boolean sortable=false;
    /**字段查询模式：single单字段查询；scope范围查询*/
    private String queryMode="single";
    /**过滤替换:1:男,2:女 */
    private String replace;
    /**过滤URL*/
    private String url;
    /**当URL不为空时 id,Name*/
    private String dictionary;
    /**字段类型:img:图片,url:链接地址,txt:普通内容,默认txt*/
    private String valType="txt";
    /**图片大小*/
    private String imageSize;
    /**是否固定列*/
    private boolean frozenColumn=false;
    /**默认查询值*/
    private String defaults;
    /**自定义查询THML代码*/
    private String queryHtml;
    /**自定义修改样式方法*/
    private String cellattr;
    /****/
    private boolean isWrap;
    /**列是否可编辑**/
    private boolean editable;
    /**可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.**/
    private String edittype;
    /**编辑的一系列选项。editable:true,edittype:’select’,editoptions: {dataUrl:”/jqGrid/admin/deplistforstu.action”}}或者{value:getProject()}**/
    private String editoptions;
    /**当这个字段为查询条件时，并且为下拉框时默认空内容名称如:All 或者 ---please choose--- 不填默认为All*/
    private String seleDef;
    /**自定义分割符*/
    private String splitStr;
    private String channel;

    private boolean isKey=false;

    private boolean isOnlyStartTime=false;
    
    public FindListColumn() {
        super();
        // TODO Auto-generated constructor stub
    }
    
    public FindListColumn(String name, String title, String langArg,
			String classes, boolean hidden, boolean query, String align,
			Integer width, String formatter, String formatterjs,
			boolean sortable, String queryMode, String replace, String url,
			String dictionary, String valType, String imageSize,
			boolean frozenColumn, String defaults, String queryHtml,
			String cellattr,boolean isWrap,boolean editable,String edittype,String editoptions,String seleDef,String splitStr,String channel,boolean isKey,boolean isOnlyStartTime) {
		super();
		this.name = name;
		this.title = title;
		this.langArg = langArg;
		this.classes = classes;
		this.hidden = hidden;
		this.query = query;
		this.align = align;
		this.width = width;
		this.formatter = formatter;
		this.formatterjs = formatterjs;
		this.sortable = sortable;
		this.queryMode = queryMode;
		this.replace = replace;
		this.url = url;
		this.dictionary = dictionary;
		this.valType = valType;
		this.imageSize = imageSize;
		this.frozenColumn = frozenColumn;
		this.defaults = defaults;
		this.queryHtml = queryHtml;
		this.cellattr = cellattr;
		this.isWrap=isWrap;
		this.editable=editable;
		this.edittype=edittype;
		this.editoptions=editoptions;
		this.seleDef=seleDef;
		this.splitStr=splitStr;
		this.channel=channel;
		this.isKey=isKey;
		this.isOnlyStartTime = isOnlyStartTime;
	}


    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getName() {
        return name;
    }
    public String getTitle() {
        return title;
    }
    public String getLangArg() {
        return langArg;
    }
    public String getClasses() {
        return classes;
    }
    public boolean isHidden() {
        return hidden;
    }
    public String getAlign() {
        return align;
    }
    public Integer getWidth() {
        return width;
    }
    public String getFormatter() {
        return formatter;
    }
    public boolean isSortable() {
        return sortable;
    }
    public String getQueryMode() {
        return queryMode;
    }
    public String getReplace() {
        return replace;
    }
    public String getUrl() {
        return url;
    }
    public String getDictionary() {
        return dictionary;
    }
    public String getValType() {
        return valType;
    }
    public void setName(String name) {
        this.name = name;
    }
    public void setTitle(String title) {
        this.title = title;
    }
    public void setLangArg(String langArg) {
        this.langArg = langArg;
    }
    public void setClasses(String classes) {
        this.classes = classes;
    }
    public void setHidden(boolean hidden) {
        this.hidden = hidden;
    }
    public void setAlign(String align) {
        this.align = align;
    }
    public void setWidth(Integer width) {
        this.width = width;
    }
    public void setFormatter(String formatter) {
        this.formatter = formatter;
    }
    public void setSortable(boolean sortable) {
        this.sortable = sortable;
    }
    public void setQueryMode(String queryMode) {
        this.queryMode = queryMode;
    }
    public void setReplace(String replace) {
        this.replace = replace;
    }
    public void setUrl(String url) {
        this.url = url;
    }
    public void setDictionary(String dictionary) {
        this.dictionary = dictionary;
    }
    public void setValType(String valType) {
        this.valType = valType;
    }


    public boolean isFrozenColumn() {
        return frozenColumn;
    }

    public void setFrozenColumn(boolean frozenColumn) {
        this.frozenColumn = frozenColumn;
    }




    public boolean isQuery() {
        return query;
    }




    public String getFormatterjs() {
        return formatterjs;
    }




    public void setQuery(boolean query) {
        this.query = query;
    }




    public void setFormatterjs(String formatterjs) {
        this.formatterjs = formatterjs;
    }




    public String getImageSize() {
        return imageSize;
    }




    public void setImageSize(String imageSize) {
        this.imageSize = imageSize;
    }




	public String getDefaults() {
		return defaults;
	}




	public void setDefaults(String defaults) {
		this.defaults = defaults;
	}




	public String getQueryHtml() {
		return queryHtml;
	}




	public void setQueryHtml(String queryHtml) {
		this.queryHtml = queryHtml;
	}




	public String getCellattr() {
		return cellattr;
	}




	public void setCellattr(String cellattr) {
		this.cellattr = cellattr;
	}

	public boolean isWrap() {
		return isWrap;
	}

	public void setWrap(boolean isWrap) {
		this.isWrap = isWrap;
	}

	public boolean getEditable() {
		return editable;
	}

	public void setEditable(boolean editable) {
		this.editable = editable;
	}

	public String getEdittype() {
		return edittype;
	}

	public void setEdittype(String edittype) {
		this.edittype = edittype;
	}

	public String getEditoptions() {
		return editoptions;
	}

	public void setEditoptions(String editoptions) {
		this.editoptions = editoptions;
	}

	public String getSeleDef() {
		return seleDef;
	}

	public void setSeleDef(String seleDef) {
		this.seleDef = seleDef;
	}

	public String getSplitStr() {
		return splitStr;
	}

	public void setSplitStr(String splitStr) {
		this.splitStr = splitStr;
	}

	public boolean getKey() {
		return isKey;
	}

	public void setKey(boolean isKey) {
		this.isKey = isKey;
	}

    public boolean getOnlyStartTime() {
        return isOnlyStartTime;
    }

    public void setOnlyStartTime(boolean isOnly) {
        this.isOnlyStartTime = isOnly;
    }

}
