package com.clou.esp.hes.app.web.core.pushlet;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import nl.justobjects.pushlet.core.Dispatcher;
import nl.justobjects.pushlet.core.Event;

import org.apache.log4j.Logger;

import com.power7000g.core.util.json.AjaxJson;


public class PushletData {
	
	private static Logger logger = Logger.getLogger(PushletData.class);
	/**
	 * 推送指定用户消息
	 * @param aSubject 消息名称
	 * @param j 参数
	 * @param userId 用户ID
	 * @throws UnsupportedEncodingException 
	 */
	public static void pushlet(String aSubject, AjaxJson j, String userId) throws UnsupportedEncodingException{
		 Event event = Event.createDataEvent(aSubject);
		 String jToStr = URLEncoder.encode(j.toString(),"UTF-8");
		 jToStr = jToStr.replaceAll("\\+", "%20");
		 event.setField("message",jToStr );			//设置消息内容
		 // 用户是否在线
		 System.out.println("Check session ：" + OnLineClient.ClientIsOn(userId+"_"+aSubject));
		 if(OnLineClient.ClientIsOn(userId+"_"+aSubject)) { 
	        String sessionId = OnLineClient.getSessionId(userId+"_"+aSubject);	// 获取sessionId
	        Dispatcher.getInstance().unicast(event,sessionId); 					//  向userId为111的用户推送消息
	     }
	}
	/**
	 * 推送所以用户消息
	 * @param aSubject 消息名称
	 * @param j 返回参数
	 */
	public static void pushlet(String aSubject,AjaxJson j){
		Event event =Event.createDataEvent(aSubject);
		 try {
			event.setField("message", URLEncoder.encode(j.toString(),"UTF-8"));//设置消息内容
		 } catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			logger.error("推送消息时异常：", e);
		}
		Dispatcher.getInstance().multicast(event);
	}
}
