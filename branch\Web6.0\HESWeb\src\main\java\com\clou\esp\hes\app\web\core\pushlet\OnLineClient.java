package com.clou.esp.hes.app.web.core.pushlet;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap; 


/**
 * <AUTHOR>
 * @时间：2015年10月27日
 * @描述：记录在线数
 */
public class OnLineClient {
	
	public static ConcurrentMap<String, String> idMapping = new ConcurrentHashMap<String, String>();
	
	public static void addClient(String userId, String sessionId) {
		if(null==userId || userId.length()==0){
			return;
		}
        idMapping.put(userId, sessionId);
    }
     
    public static void removeClient(String sessionId) {
    	int index = sessionId.indexOf("_");
    	if(index<0){
    		return;
    	}
    	
        String id = sessionId.substring(0, index);
        if(idMapping.containsKey(id) ) {
            if(sessionId.equals(idMapping.get(id))) {
                idMapping.remove(id);
            } else {
                System.out.println("用户" + id + " : " + sessionId  + " 过时");
            }
        }
    }
     
    public static boolean ClientIsOn(String uid) {
        return idMapping.containsKey(uid);
    }
     
    public static String getSessionId(String uid) {
        return idMapping.get(uid);
    }
	
}
