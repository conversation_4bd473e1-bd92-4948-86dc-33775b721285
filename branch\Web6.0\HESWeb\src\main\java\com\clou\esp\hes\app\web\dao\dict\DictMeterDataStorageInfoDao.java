/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageInfo{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.dict;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageInfo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DictMeterDataStorageInfoDao extends CrudDao<DictMeterDataStorageInfo>{

	public  List<DictMeterDataStorageInfo> unBindForJqGrid(JqGridSearchTo jqGridSearchTo) ;
	
	public  Long   getMaxColumnIndex(String tableId);
	
	public void deleteByTableId(String tableId);
}