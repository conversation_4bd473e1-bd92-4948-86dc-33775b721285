/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetVeeRule{ } 
 * 
 * 摘    要： assetVeeRule
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-20 04:22:02
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Structures;
import ch.iec.tc57._2011.meterdefineconfig_.Values;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.power7000g.core.util.json.RoletoJson;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroup;
import com.clou.esp.hes.app.web.model.asset.AssetRouter;
import com.clou.esp.hes.app.web.model.asset.AssetScheduleScheme;
import com.clou.esp.hes.app.web.model.dict.DictCommunicationType;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;
import com.clou.esp.hes.app.web.model.dict.DictManufacturer;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysService;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRule;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRuleDataitem;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRuleParam;
import com.clou.esp.hes.app.web.model.vee.DictVeeEvent;
import com.clou.esp.hes.app.web.service.asset.AssetMeterGroupService;
import com.clou.esp.hes.app.web.service.dict.DictDataitemService;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleDataitemService;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleParamService;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleService;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventService;
import com.clou.esp.hes.app.web.service.vee.DictVeeMethodService;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @时间：2018-12-20 04:22:02
 * @描述：assetVeeRule类
 */
@Controller
@RequestMapping("/assetVeeRuleController")
public class AssetVeeRuleController extends BaseController{

 	@Resource
    private AssetVeeRuleService assetVeeRuleService;
	@Resource
    private DictVeeMethodService dictVeeMethodService;
	@Resource
    private DictDataitemService dictDataitemService;
	@Resource
    private AssetMeterGroupService assetMeterGroupService;
	@Resource
    private DictVeeEventService dictVeeEventService;
	@Resource
    private AssetVeeRuleDataitemService assetVeeRuleDataitemService;
	@Resource
    private AssetVeeRuleParamService assetVeeRuleParamService;
	
	
	/**
	 * 跳转到assetVeeRule列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/assetVeeRuleList");
    }
	
	
	@RequestMapping(value = "saveAssetVeeRuleData")
    @ResponseBody
    public AjaxJson saveTouGroupData(AssetVeeRule assetVeeRule,String veeEventDataItemList,String veeEventParamList,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        

        /*
         * 根据Vee Rule Name名字进行查询，判断是否重名
         */
        AssetVeeRule assetVeeRule1 = new AssetVeeRule();
        assetVeeRule1.setName(assetVeeRule.getName());
        assetVeeRule1.setMgId(assetVeeRule.getMgId());
    	List<AssetVeeRule> list = assetVeeRuleService.getList(assetVeeRule1);
    	if(list.size()>1 || (list.size()>0 && !list.get(0).getId().equals(assetVeeRule1.getId()))){
    		j.setSuccess(false);
    		j.setErrorMsg("The Vee Rule Name already exists");
    		return j;
    	}

        String veeRuleId=(String) assetVeeRuleService.save(assetVeeRule);
        System.out.println(veeRuleId);
        
        JSONArray veeEventDataItemJsons=null;
        if(StringUtil.isNotEmpty(veeEventDataItemList)){
        	veeEventDataItemJsons=JSONArray.fromObject(veeEventDataItemList);
        }
 
		if(veeEventDataItemJsons!=null&&veeEventDataItemJsons.size()>0){
			List<AssetVeeRuleDataitem> assetVeeRuleDataitemList = new ArrayList();
        	for(int i=0;i<veeEventDataItemJsons.size();i++){
        		JSONObject o=veeEventDataItemJsons.getJSONObject(i);
        	  
        	    String dataItemKey = o.getString("dataItemKey");
        	    String cycleCount =  o.getString("cycleCount");
        	    
        	    BigDecimal cycleCountBig =  new BigDecimal(cycleCount);

        	    String cycleType =   o.getString("cycleType");
        	    AssetVeeRuleDataitem assetVeeRuleDataitem = new AssetVeeRuleDataitem();
        	    assetVeeRuleDataitem.setRuleId(veeRuleId);
        	    assetVeeRuleDataitem.setDataitemKey(dataItemKey);
        	    assetVeeRuleDataitem.setCycleCount(cycleCountBig);
        	    assetVeeRuleDataitem.setCycleType(cycleType);
        	    assetVeeRuleDataitemList.add(assetVeeRuleDataitem);
        	}
        	assetVeeRuleDataitemService.batchSave(assetVeeRuleDataitemList);
        }
        
        JSONArray veeEventParamListJsons=null;
        if(StringUtil.isNotEmpty(veeEventParamList)){
        	veeEventParamListJsons=JSONArray.fromObject(veeEventParamList);
        }
        
        if(veeEventParamListJsons!=null && veeEventParamListJsons.size()>0){
        	List<AssetVeeRuleParam> assetVeeRuleParamList = new ArrayList();
        	for(int i=0;i<veeEventParamListJsons.size();i++){
        		JSONObject o=veeEventParamListJsons.getJSONObject(i);
        		AssetVeeRuleParam assetVeeRuleParam = new AssetVeeRuleParam();
        	    String paramKey = o.getString("paramKey");
        	    String paramValue = o.getString("defautValue");
        	    assetVeeRuleParam.setRuleId(veeRuleId);
        	    assetVeeRuleParam.setParamKey(paramKey);
        	    assetVeeRuleParam.setParamValue(paramValue);
        	    assetVeeRuleParamList.add(assetVeeRuleParam);
        	}
        	assetVeeRuleParamService.batchSave(assetVeeRuleParamList);
        }
        
        j.setMsg(MutiLangUtil.doMutiLang("system.addSucc"));
        
        return j;
	 }
	
	@RequestMapping(value = "saveEditAssetVeeRuleData")
    @ResponseBody
    public AjaxJson saveEditAssetVeeRuleData(AssetVeeRule assetVeeRule,String veeEventDataItemList,String veeEventParamList,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
       
        /*
         * 根据Vee Rule Name名字进行查询，判断是否重名
         */
        AssetVeeRule assetVeeRule1 = new AssetVeeRule();
        assetVeeRule1.setName(assetVeeRule.getName());
        assetVeeRule1.setMgId(assetVeeRule.getMgId());
    	List<AssetVeeRule> list = assetVeeRuleService.getList(assetVeeRule1);
    	if(list.size()>1 || (list.size()>0 && !list.get(0).getId().equals(assetVeeRule.getId()))){
    		j.setSuccess(false);
    		j.setErrorMsg("The Vee Rule Name already exists");
    		return j;
    	}

       int updateFlag = assetVeeRuleService.update(assetVeeRule);
       if(updateFlag > -1){
    	
    	    AssetVeeRuleDataitem assetVeeRuleDataitemDel = new AssetVeeRuleDataitem();
		    assetVeeRuleDataitemDel.setRuleId(assetVeeRule.getId());
		    int delDataItemFlag = assetVeeRuleDataitemService.delete(assetVeeRuleDataitemDel);
		    
    	    AssetVeeRuleParam assetVeeRuleParamDel = new AssetVeeRuleParam();
		    assetVeeRuleParamDel.setRuleId(assetVeeRule.getId());
		    int delParamFlag = assetVeeRuleParamService.delete(assetVeeRuleParamDel);
    	   
		   JSONArray veeEventDataItemJsons=null;
	       if(StringUtil.isNotEmpty(veeEventDataItemList)){
	       	veeEventDataItemJsons=JSONArray.fromObject(veeEventDataItemList);
	       }
	
			if(veeEventDataItemJsons!=null&&veeEventDataItemJsons.size()>0){
			 
				if(delDataItemFlag > -1){
					
				    List<AssetVeeRuleDataitem> assetVeeRuleDataitemList = new ArrayList();
				    for(int i=0;i<veeEventDataItemJsons.size();i++){
		       		JSONObject o=veeEventDataItemJsons.getJSONObject(i);
		       	  
		       	    String dataItemKey = o.getString("dataItemKey");
		       	    String cycleCount =  o.getString("cycleCount");
		       	    
		       	    BigDecimal cycleCountBig =  new BigDecimal(cycleCount);
		
				    String cycleType =   o.getString("cycleType");
				    AssetVeeRuleDataitem assetVeeRuleDataitem = new AssetVeeRuleDataitem();
				    assetVeeRuleDataitem.setRuleId(assetVeeRule.getId());
				    assetVeeRuleDataitem.setDataitemKey(dataItemKey);
				    assetVeeRuleDataitem.setCycleCount(cycleCountBig);
				    assetVeeRuleDataitem.setCycleType(cycleType);
				    assetVeeRuleDataitemList.add(assetVeeRuleDataitem);
					}
					assetVeeRuleDataitemService.batchSave(assetVeeRuleDataitemList);
				}	
			}
		  JSONArray veeEventParamListJsons=null;
		  if(StringUtil.isNotEmpty(veeEventParamList)){
			veeEventParamListJsons=JSONArray.fromObject(veeEventParamList);
		  }
		  if(veeEventParamListJsons!=null && veeEventParamListJsons.size()>0){
			  
          
		    if(delParamFlag > -1){
		     	List<AssetVeeRuleParam> assetVeeRuleParamList = new ArrayList();
		      	for(int i=0;i<veeEventParamListJsons.size();i++){
		      		JSONObject o=veeEventParamListJsons.getJSONObject(i);
		      		AssetVeeRuleParam assetVeeRuleParam = new AssetVeeRuleParam();
		      	    String paramKey = o.getString("paramKey");
		      	    String paramValue = o.getString("defautValue");
		      	    assetVeeRuleParam.setRuleId(assetVeeRule.getId());
		      	    assetVeeRuleParam.setParamKey(paramKey);
		      	    assetVeeRuleParam.setParamValue(paramValue);
		      	    assetVeeRuleParamList.add(assetVeeRuleParam);
		      	}
		      	assetVeeRuleParamService.batchSave(assetVeeRuleParamList);
		    }
	     
	      }
	      
	      	j.setMsg(MutiLangUtil.doMutiLang("system.updateSucc"));
	      
       	  }
	     return j;
	}

	/**
	 * 跳转到assetVeeRule新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "assetVeeRule")
	public ModelAndView assetVeeRule(AssetVeeRule assetVeeRule,HttpServletRequest request, Model model) {
		model.addAttribute("protocolId", ResourceUtil.getSessionattachmenttitle("protocol.id"));
		if(StringUtil.isNotEmpty(assetVeeRule.getId())){
			try {
				assetVeeRule=assetVeeRuleService.getEntity(assetVeeRule.getId());
				model.addAttribute("AssetVeeRule", assetVeeRule);
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
		}
		String veeGroupId = request.getParameter("veeGroupId");
		
		model.addAttribute("veeGroupId", veeGroupId);
		
		String classIdReplace = "1:Class 1,2:Class 2,3:Class 3,4:Class 4,5:Class 5";
		model.addAttribute("classIdReplace", classIdReplace);
		
		String ruleTypeReplace = "1:Validation,2:Estimation";
		model.addAttribute("ruleTypeReplace", ruleTypeReplace);
		
		String ruleStatusReplace = "1:Running,0:Stop";
		model.addAttribute("ruleStatusReplace", ruleStatusReplace);

		List<DictVeeEvent> dataItems= dictVeeEventService.getAllList();
		String eventIdReplace = RoletoJson.listToReplaceStr(dataItems, "id", "name");
		model.addAttribute("eventIdReplace", eventIdReplace);
		
		if(dataItems != null && dataItems.size() > 0){
			DictVeeEvent dictVeeEvent =  dataItems.get(0);
			model.addAttribute("descr", dictVeeEvent.getDescr());
			model.addAttribute("qryEventId", dictVeeEvent.getId());
			
		}
		
		return new ModelAndView("/vee/addVeeRule");
	}
	
	
	/**
	 * 跳转到assetVeeRule修改界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "editAssetVeeRule")
	public ModelAndView editAssetVeeRule(AssetVeeRule assetVeeRule,HttpServletRequest request, Model model) {
	
		model.addAttribute("protocolId", ResourceUtil.getSessionattachmenttitle("protocol.id"));
		if(StringUtil.isNotEmpty(assetVeeRule.getId())){
			try {
				assetVeeRule=assetVeeRuleService.getEntity(assetVeeRule.getId());
				model.addAttribute("assetVeeRule", assetVeeRule);
	        }
	        catch (Exception e) {
	            // TODO Auto-generated catch block
	            e.printStackTrace();
	        }
		}
		if(assetVeeRule == null){
			return new ModelAndView("/vee/editVeeRule");
		}

		String classIdReplace = "1:Class 1,2:Class 2,3:Class 3,4:Class 4,5:Class 5";
		model.addAttribute("classIdReplace", classIdReplace);
		
		String ruleTypeReplace = "1:Validation,2:Estimation";
		model.addAttribute("ruleTypeReplace", ruleTypeReplace);
		
		String ruleStatusReplace = "1:Running,0:Stop";
		model.addAttribute("ruleStatusReplace", ruleStatusReplace);
	
		List<DictVeeEvent> dataItems= dictVeeEventService.getAllList();
		String eventIdReplace = RoletoJson.listToReplaceStr(dataItems, "id", "name");
		model.addAttribute("eventIdReplace", eventIdReplace);
		DictVeeEvent dictVeeEvent = new DictVeeEvent();
		dictVeeEvent.setId(assetVeeRule.getEventId());
		DictVeeEvent dictVeeEvent1 = dictVeeEventService.get(dictVeeEvent);
	
		if(dictVeeEvent1 != null){
		
			model.addAttribute("descr", dictVeeEvent1.getDescr());
			model.addAttribute("qryEventId", dictVeeEvent1.getId());
			
		}
		
		return new ModelAndView("/vee/editVeeRule");
	}



	
	/**
	 * 跳转到veeGroup新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "veeGroup")
	public ModelAndView veeGroup(AssetMeterGroup assetMeterGroup,HttpServletRequest request, Model model) {
		model.addAttribute("protocolId", ResourceUtil.getSessionattachmenttitle("protocol.id"));
		if(StringUtil.isNotEmpty(assetMeterGroup.getId())){
			try {
				assetMeterGroup=assetMeterGroupService.getEntity(assetMeterGroup.getId());
				model.addAttribute("assetMeterGroup", assetMeterGroup);
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			
		}
		return new ModelAndView("/vee/addVeeGroup");
	}

	/**
	 * assetVeeRule查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=assetVeeRuleService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除assetVeeRule信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(AssetVeeRule assetVeeRule,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(assetVeeRuleService.deleteById(assetVeeRule.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存assetVeeRule信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })AssetVeeRule assetVeeRule,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        AssetVeeRule t=new  AssetVeeRule();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(assetVeeRule.getId())){
        	t=assetVeeRuleService.getEntity(assetVeeRule.getId());
			MyBeanUtils.copyBeanNotNull2Bean(assetVeeRule, t);
				assetVeeRuleService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            assetVeeRuleService.save(assetVeeRule);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    
    
    /**
	 * 获取设备信息
	 * @param sn
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAssetVeeRule")
	@ResponseBody
	public AjaxJson getAssetManagementName(String groupId , HttpServletRequest request) {
		AjaxJson j = new AjaxJson();
		try {
			//Meter  //Commnuicator
			AssetVeeRule assetVeeRule = new AssetVeeRule();
			assetVeeRule.setMgId(groupId);
			List<AssetVeeRule> assetVeeRuleList = assetVeeRuleService.getList(assetVeeRule);
			//List<AssetMeter> list = assetMeterService.getList(am);
			
//			for(AssetVeeRule assetVeeRule1 : assetVeeRuleList){
//				assetVeeRule1.getMethodId()
//				dictVeeMethod dictVeeMethod = new dictVeeMethod();
//				dictVeeMethodService.get(entity)
//				dictDataitemService
//			}
			
			j.put("assetVeeRuleList", assetVeeRuleList);
			
		} catch (Exception e) {
			e.printStackTrace();
			j.setSuccess(false);
			j.setMsg("Abnormal operation!");
		}
		return j;
	}
	
	
	
}