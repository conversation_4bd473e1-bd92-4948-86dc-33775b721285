package com.clou.esp.hes.app.web.dao.report;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.report.ImportExportReport;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: ImportExportReportDao
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年10月18日 上午9:52:40
 *
 */
@MyBatisDao
public interface ImportExportReportDao extends CrudDao<ImportExportReport> {
	
	List<ImportExportReport> findImportExportReport(JqGridSearchTo jqGridSearchTo);
	List<ImportExportReport> findImportExportReportDetail(JqGridSearchTo jqGridSearchTo);
	List<ImportExportReport> findImportExportParamList(Map<String, Object> p);
}
