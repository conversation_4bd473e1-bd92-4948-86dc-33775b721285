<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.PublicCmsDebtMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.cms.PublicCmsDebt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="DEBT_ID" jdbcType="VARCHAR" property="debtId" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="DEBT_TYPE" jdbcType="VARCHAR" property="debtType" />
    <result column="DEBT_AMOUNT" jdbcType="DOUBLE" property="debtAmount" />
    <result column="DEBT_REF" jdbcType="VARCHAR" property="debtRef" />
    <result column="INSTALMENT_DUE_DATE" jdbcType="VARCHAR" property="instalmentDueDate" />
    <result column="DEBT_STATUS" jdbcType="VARCHAR" property="debtStatus" />
    <result column="IS_STORAGE" jdbcType="VARCHAR" property="isStorage" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  
  
  <!-- 根据 tariff_code 更新数据 -->
	<update id="updateByDebtRef" parameterType="com.power7000.model.cms.PublicCmsDebt">
		update ppm_public_cms_debt
		<set>
			<if test="debtType != null and debtType != ''">
				DEBT_TYPE = #{debtType,jdbcType=VARCHAR},
			</if>
			<if test="debtAmount != null and debtAmount != ''">
				DEBT_AMOUNT = #{debtAmount,jdbcType=DOUBLE},
			</if>
			<if test="instalmentDueDate != null and instalmentDueDate != ''">
				INSTALMENT_DUE_DATE = #{instalmentDueDate,jdbcType=VARCHAR},
			</if>
			<if test="debtStatus != null and debtStatus != ''">
				DEBT_STATUS = #{debtStatus,jdbcType=VARCHAR},
			</if>
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
				UPDATE_DATE = now()
		</set>
		where DEBT_REF = #{debtRef,jdbcType=VARCHAR}
	</update>
  
  
  <select id="selectByDebtRef" resultMap="BaseResultMap" parameterType="java.lang.String">
    select t.* from ppm_public_cms_debt t where  t.DEBT_REF = #{debtRef,jdbcType=VARCHAR}
  </select>
  
  <insert id="insertPublicCmsDept"	parameterType="com.power7000.model.cms.PublicCmsDebt">
		<selectKey resultType="java.lang.String" order="BEFORE"	keyProperty="debtId">
					SELECT REPLACE(UUID(),'-','')
	  	</selectKey>
		insert into PPM_PUBLIC_CMS_DEBT	(DEBT_ID,CUSTOMER_ID,DEBT_TYPE,DEBT_AMOUNT,DEBT_REF,INSTALMENT_DUE_DATE,DEBT_STATUS,IS_STORAGE,CREATE_DATE,UPDATE_DATE)
		values	(#{debtId},#{customerId},#{debtType},#{debtAmount},#{debtRef},#{instalmentDueDate},#{debtStatus},#{isStorage},now(),now())
	</insert>
  
</mapper>