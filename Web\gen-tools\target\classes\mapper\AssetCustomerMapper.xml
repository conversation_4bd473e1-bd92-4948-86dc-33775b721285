<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.AssetCustomerMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.AssetCustomer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="CUSTOMER_TYPE" jdbcType="VARCHAR" property="customerType" />
    <result column="METER_ID" jdbcType="VARCHAR" property="meterId" />
    <result column="INDUSTRY_TYPE" jdbcType="VARCHAR" property="industryType" />
    <result column="TELEPHONE_NUM" jdbcType="VARCHAR" property="telephoneNum" />
    <result column="ADDR" jdbcType="VARCHAR" property="addr" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="LONGITUGE" jdbcType="VARCHAR" property="longituge" />
    <result column="LATITUDE" jdbcType="VARCHAR" property="latitude" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="GEO_CODE" jdbcType="VARCHAR" property="geoCode" />
  </resultMap>
  
  
  <insert id="insertByXml" parameterType="com.power7000.model.ghana.AssetCustomer">
		<selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
			SELECT FN_ClouESP_PPM_Get_PKID('ASSET_CUSTOMER')
		</selectKey>
		insert into ASSET_CUSTOMER
		(Utility_ID,ID,Customer_NO,Trade_ID,ORG_ID,Customer_Name,Address,Mobile_Phone,Telephone_No,E_Mail,Customer_Status,Information_Source,Create_Date,Save_DB_Date,Readme)
		values
		(#{utilityId},#{id,jdbcType=VARCHAR},#{customerNo},#{tradeId},#{orgId},#{customerName},#{address},#{mobilePhone},#{telephoneNo},#{eMail},#{customerStatus},#{informationSource},#{createDate},#{saveDbDate},#{readme})
	</insert>
	<update id="updateByCustomerNo" parameterType="com.power7000.model.ghana.AssetCustomer">
		update ASSET_CUSTOMER
		<set>
			ID=#{id}
			<if test="customerNo!= null ">
				, Customer_NO = #{customerNo}
			</if>

			<if test="tradeId!= null ">
				,Trade_ID = #{tradeId}
			</if>
			<if test="utilityId!= null ">
				,UTILITY_ID = #{utilityId}
			</if>
			<if test="orgId!= null ">
				,ORG_ID = #{orgId}
			</if>
			<if test="customerName!= null ">
				,Customer_Name = #{customerName}
			</if>
			<if test="address!= null ">
				,address = #{address}
			</if>
			<if test="mobilePhone!= null ">
				,Mobile_Phone = #{mobilePhone}
			</if>
			<if test="telephoneNo!= null ">
				,Telephone_No = #{telephoneNo}
			</if>
			<if test="eMail!= null ">
				,e_mail = #{eMail}
			</if>
			<if test="customerStatus!= null ">
				,Customer_Status = #{customerStatus}
			</if>
			<if test="informationSource!= null ">
				,Information_Source = #{informationSource}
			</if>
			<if test="saveDbDate!= null ">
				,Save_DB_Date = #{saveDbDate}
			</if>
			
		</set>
		where ID=#{id}
	</update>
  
</mapper>