package com.clou.esp.hes.app.web.core.shiro.filter;

import java.io.PrintWriter;
import java.util.List;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.shiro.web.filter.AccessControlFilter;
import org.springframework.beans.factory.annotation.Autowired;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.system.SysMenuService;
import com.power7000g.core.util.json.AjaxJson;

/**
 * 判断登录
 * 
 * <AUTHOR>
 * 
 */
public class LoginFilter extends AccessControlFilter {
	final static Class<LoginFilter> CLASS = LoginFilter.class;
	@Autowired
	private SysMenuService sysMenuService;

	@Override
	protected boolean isAccessAllowed(ServletRequest request,
			ServletResponse response, Object mappedValue) throws Exception {
		SysUser token = TokenManager.getToken();
		
		if(token == null){
			boolean exitFlag = true;
			int i = 0;
			while(exitFlag && i < 1){
				i++;
				Thread.sleep(10);
				token = TokenManager.getToken();
				

				HttpServletRequest httpServletRequest = (HttpServletRequest)request;
				
				if(httpServletRequest.getRequestURI().contains("getCurrentEventSum") || httpServletRequest.getRequestURI().contains("pushlet")
						 || httpServletRequest.getRequestURI().contains("interfaces")){
				
					return Boolean.TRUE;
				}
				
				if(token != null){
					exitFlag = false;
				}
			}
		}
        
		if (null != token || isLoginRequest(request, response)) {// &&
			HttpSession session = ((HttpServletRequest) request)
					.getSession(true);
			SysUser su = (SysUser) session
					.getAttribute(Globals.SESSION_LOGIN_SYS_USER);
			if (su == null) {
				session.setAttribute(Globals.SESSION_LOGIN_SYS_USER, token);
				List<DictMenu> menus = sysMenuService
						.getMenusByUserId(token);
				session.setAttribute("menus", menus);
			}
			
			// System.out.println("SysUser==null+++++"+su);
			
			return Boolean.TRUE;
		}
		
		if (ShiroFilterUtils.isAjax(request)) {// ajax请求
			AjaxJson json = new AjaxJson();
			json.setErrorMsg("Log in overtime, please login again!");
			ShiroFilterUtils.out(response, json);
		}
		return Boolean.FALSE;

	}

	@Override
	protected boolean onAccessDenied(ServletRequest request,
			ServletResponse response) throws Exception {
		// 保存Request和Response 到登录后的链接
		// saveRequestAndRedirectToLogin(request, response);
		toLogin(request, response);
		return Boolean.FALSE;
	}

	private void toLogin(ServletRequest request, ServletResponse response) {
		PrintWriter out = null;
		try {
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.println("<script type=\"text/javascript\">if(window.parent.layer){window.parent.layer.confirm('Log in overtime, please login again!', {"
					  +" title:'Message', closeBtn: 0, btn: ['OK'],  icon: 0, skin: 'layer-ext-moon'}, function(){window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";});}else{window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";}</script>");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.flush();
				out.close();
			}
		}
	}

}
