package com.clou.esp.hes.app.web.core.util;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.xdemo.superutil.j2se.ReflectUtils;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.PageSize;
import com.lowagie.text.Phrase;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.power7000g.core.excel.Excel;
import com.power7000g.core.excel.ExcelDataFormatter;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;

/**
 * 生成pdf
 * 
 * <AUTHOR>
 * 
 */
public class CreatePdf {
	Document document = new Document();// 建立一个Document对象

	private static Font headfont;// 设置字体大小
	private static Font keyfont;// 设置字体大小
	private static Font textfont;// 设置字体大小

	static {
		// 中文格式
		BaseFont bfChinese;
		try {
			// 设置中文显示
			// bfChinese = BaseFont.createFont("STSong-Light",
			// "UniGB-UCS2-H",BaseFont.NOT_EMBEDDED);
			// bfChinese = BaseFont.createFont(
			// "c://windows//fonts//simsun.ttc,1" , BaseFont.IDENTITY_H,
			// BaseFont.EMBEDDED);
			bfChinese = BaseFont.createFont(CreatePdf.class.getResource("/")
					+ "simsun.ttc,1", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
			headfont = new Font(bfChinese, 10, Font.BOLD);// 设置字体大小
			keyfont = new Font(bfChinese, 8, Font.BOLD);// 设置字体大小
			textfont = new Font(bfChinese, 8, Font.NORMAL);// 设置字体大小
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 文成文件
	 * 
	 * @param file
	 *            待生成的文件名
	 */
	public CreatePdf(File file) {
		document.setPageSize(PageSize.A4);// 设置页面大小
		try {
			PdfWriter.getInstance(document, new FileOutputStream(file));
			document.open();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public CreatePdf() {

	}

	public void initFile(File file) {
		document.setPageSize(PageSize.A4);// 设置页面大小
		try {
			PdfWriter.getInstance(document, new FileOutputStream(file));
			document.open();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	int maxWidth = 520;

	/**
	 * 为表格添加一个内容
	 * 
	 * @param value
	 *            值
	 * @param font
	 *            字体
	 * @param align
	 *            对齐方式
	 * @return 添加的文本框
	 */
	public PdfPCell createCell(String value, Font font, int align) {
		PdfPCell cell = new PdfPCell();
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setHorizontalAlignment(align);
		cell.setPhrase(new Phrase(value, font));
		return cell;
	}

	/**
	 * 为表格添加一个内容
	 * 
	 * @param value
	 *            值
	 * @param font
	 *            字体
	 * @return 添加的文本框
	 */
	public PdfPCell createCell(String value, Font font) {
		PdfPCell cell = new PdfPCell();
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setHorizontalAlignment(Element.ALIGN_CENTER);
		cell.setPhrase(new Phrase(value, font));
		return cell;
	}

	/**
	 * 为表格添加一个内容
	 * 
	 * @param value
	 *            值
	 * @param font
	 *            字体
	 * @param align
	 *            对齐方式
	 * @param colspan
	 *            占多少列
	 * @return 添加的文本框
	 */
	public PdfPCell createCell(String value, Font font, int align, int colspan) {
		PdfPCell cell = new PdfPCell();
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setHorizontalAlignment(align);
		cell.setColspan(colspan);
		cell.setPhrase(new Phrase(value, font));
		return cell;
	}

	/**
	 * 为表格添加一个内容
	 * 
	 * @param value
	 *            值
	 * @param font
	 *            字体
	 * @param align
	 *            对齐方式
	 * @param colspan
	 *            占多少列
	 * @param boderFlag
	 *            是否有有边框
	 * @return 添加的文本框
	 */
	public PdfPCell createCell(String value, Font font, int align, int colspan,
			boolean boderFlag) {
		PdfPCell cell = new PdfPCell();
		cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
		cell.setHorizontalAlignment(align);
		cell.setColspan(colspan);
		cell.setPhrase(new Phrase(value, font));
		cell.setPadding(3.0f);
		if (!boderFlag) {
			cell.setBorder(0);
			cell.setPaddingTop(15.0f);
			cell.setPaddingBottom(8.0f);
		}
		return cell;
	}

	/**
	 * 创建一个表格对象
	 * 
	 * @param colNumber
	 *            表格的列数
	 * @return 生成的表格对象
	 */
	public PdfPTable createTable(int colNumber) {
		PdfPTable table = new PdfPTable(colNumber);
		try {
			table.setTotalWidth(maxWidth);
			table.setLockedWidth(true);
			table.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.getDefaultCell().setBorder(1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return table;
	}

	public PdfPTable createTable(float[] widths) {
		PdfPTable table = new PdfPTable(widths);
		try {
			table.setTotalWidth(maxWidth);
			table.setLockedWidth(true);
			table.setHorizontalAlignment(Element.ALIGN_CENTER);
			table.getDefaultCell().setBorder(1);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return table;
	}

	public PdfPTable createBlankTable() {
		PdfPTable table = new PdfPTable(1);
		table.getDefaultCell().setBorder(0);
		table.addCell(createCell("", keyfont));
		table.setSpacingAfter(20.0f);
		table.setSpacingBefore(20.0f);
		return table;
	}
	
	public <T> void generatePDF(List<T> list,ExcelDataFormatter edf,Class group)
			throws Exception {
		Field[] fields = ReflectUtils.getClassFieldsAndSuperClassFields(list.get(0).getClass());
		List<PdfPCell> cellList=new ArrayList<PdfPCell>();
		Excel excel = null;
		for (Field field : fields) {
			field.setAccessible(true);
			excel = field.getAnnotation(Excel.class);
			if (excel == null || excel.skip() == true) {
				continue;
			}
			Class[] groups = excel.groups();
			if (group != null && groups != null && groups.length > 0) {
				List<Class> groupList = Arrays.asList(groups);
				if (!groupList.contains(group)) {
					continue;
				}
			} else {
				continue;
			}
			// 写入标题
			PdfPCell  cell = new PdfPCell();
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setPhrase(new Phrase(excel.name(), keyfont));
			cellList.add(cell);
		}
		Class classType = list.get(0).getClass();
		// 创建一个只有5列的表格
		PdfPTable table = createTable(cellList.size());
		// 添加备注,靠左，不显示边框
		table.addCell(createCell("", keyfont, Element.ALIGN_LEFT, cellList.size(), false));
		// 设置表头
		for (int i = 0; i < cellList.size(); i++) {
			table.addCell(cellList.get(i));
		}
		int fregmentSize=1000;
		int k=0;
		int i=0;
		for (T t : list) {
			int columnIndex = 0;
			Object o = null;
			for (Field field : fields) {
				PdfPCell cell = new PdfPCell();
				field.setAccessible(true);

				// 忽略标记skip的字段
				excel = field.getAnnotation(Excel.class);
				if (excel == null || excel.skip() == true) {
					continue;
				}
				Class[] groups = excel.groups();
				if (group != null && groups != null && groups.length > 0) {
					List<Class> groupList = Arrays.asList(groups);
					if (!groupList.contains(group)) {
						continue;
					}
				} else {
					continue;
				}
				o = field.get(t);
				// 如果数据为空，跳过
				if (o == null){
					cell.setPhrase(new Phrase("", textfont));
				}else{
					// 处理日期类型
					if (o instanceof Date) {
						if (edf == null) {
							cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
						}else{
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
							} else {
								if(StringUtil.isNotEmpty(map.get(field.getName()))){
									cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),map.get(field.getName())), textfont));
								}else{
									cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
								}
							}
						}
					} else if (o instanceof Double || o instanceof Float) {
						cell.setPhrase(new Phrase(((Double) field.get(t)).toString(), textfont));
					} else if (o instanceof Boolean) {
						Boolean bool = (Boolean) field.get(t);
						if (edf == null) {
							cell.setPhrase(new Phrase((bool).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((bool).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(bool.toString()
										.toLowerCase())).toString(), textfont));
							}
						}

					} else if (o instanceof Integer) {

						Integer intValue = (Integer) field.get(t);

						if (edf == null) {
							cell.setPhrase(new Phrase((intValue).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((intValue).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(intValue.toString())).toString(), textfont));
							}
						}
					} else if (o instanceof String) {
						String intValue = (String) field.get(t);
						if (edf == null) {
							cell.setPhrase(new Phrase((intValue).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((intValue).toString(), textfont));
							} else {
								if(intValue == null || map.get(intValue) == null){
									cell.setPhrase(new Phrase((intValue).toString(), textfont));
								}else{
									cell.setPhrase(new Phrase((map.get(intValue.toString())).toString(), textfont));
								}
								
							}
						}
					} else {
						cell.setPhrase(new Phrase(field.get(t).toString(), textfont));
					}
				}
				cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
				cell.setHorizontalAlignment(Element.ALIGN_CENTER);
				table.addCell(cell);
			}
			 i++;
			 if (i != 0 && i % fregmentSize == fregmentSize - 1) {
		            System.out.println("第[ " + (i + 1) + " ]行进行内存释放 " + ((k++) + 1) + " th");
		            document.add(table);
		            table.deleteBodyRows();
		            table.setSkipFirstHeader(true);
		      }

		}
		if(i<fregmentSize){
			document.add(table);
		}
		// 关闭流
		document.close();
	}
	/**
	 * 根据excel注解直接打印操作分组
	 * @param list
	 * @param edf
	 * @param group
	 * @param request
	 * @param response
	 */
	public static <T> void printPdf(List<T> list,ExcelDataFormatter edf, Class group,HttpServletRequest request,HttpServletResponse response){
		String basePath = request.getRealPath("/");
		basePath += "\\upload\\";
		File f = new File(basePath);
		if (!f.exists()) {
			f.mkdirs();
		}
		basePath += System.currentTimeMillis()
				+ new Random().nextInt(10000) + ".pdf";
		try {
			String filePath = new CreatePdf()
			.generatePDFs(basePath, list,edf, group);
			response.setContentType("application/pdf");
			String strPdfPath = new String(basePath);
			// 判断该路径下的文件是否存在
			File file = new File(strPdfPath);
			if (file.exists()) {
				DataOutputStream temps = new DataOutputStream(
						response.getOutputStream());
				DataInputStream in = new DataInputStream(
						new FileInputStream(strPdfPath));
				byte[] b = new byte[2048];
				while ((in.read(b)) != -1) {
					temps.write(b);
					temps.flush();
				}
				in.close();
				temps.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 根据excel注解直接打印操作不分组
	 * @param list
	 * @param edf
	 * @param request
	 * @param response
	 */
	public static <T> void printPdf(List<T> list,ExcelDataFormatter edf,HttpServletRequest request,HttpServletResponse response){
		String basePath = request.getRealPath("/");
		basePath += "\\upload\\";
		File f = new File(basePath);
		if (!f.exists()) {
			f.mkdirs();
		}
		basePath += System.currentTimeMillis()
				+ new Random().nextInt(10000) + ".pdf";
		try {
			String filePath = new CreatePdf()
			.generatePDFs(basePath, list,edf);
			response.setContentType("application/pdf");
			String strPdfPath = new String(basePath);
			// 判断该路径下的文件是否存在
			File file = new File(strPdfPath);
			if (file.exists()) {
				DataOutputStream temps = new DataOutputStream(
						response.getOutputStream());
				DataInputStream in = new DataInputStream(
						new FileInputStream(strPdfPath));
				byte[] b = new byte[2048];
				while ((in.read(b)) != -1) {
					temps.write(b);
					temps.flush();
				}
				in.close();
				temps.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 根据自定义excel打印
	 * @param list
	 * @param edf
	 * @param group
	 * @param request
	 * @param response
	 */
	public static <T> void printPdf(List<T> list,ExcelDataFormatter edf,Map<String,Excel> excels,HttpServletRequest request,HttpServletResponse response){
		String basePath = request.getRealPath("/");
		basePath += "\\upload\\";
		File f = new File(basePath);
		if (!f.exists()) {
			f.mkdirs();
		}
		basePath += System.currentTimeMillis()
				+ new Random().nextInt(10000) + ".pdf";
		try {
			String filePath = new CreatePdf().generatePDFs(basePath, list,edf, excels);
			response.setContentType("application/pdf");
			String strPdfPath = new String(basePath);
			// 判断该路径下的文件是否存在
			File file = new File(strPdfPath);
			if (file.exists()) {
				DataOutputStream temps = new DataOutputStream(
						response.getOutputStream());
				DataInputStream in = new DataInputStream(
						new FileInputStream(strPdfPath));
				byte[] b = new byte[2048];
				while ((in.read(b)) != -1) {
					temps.write(b);
					temps.flush();
				}
				in.close();
				temps.close();
			}
		} catch (Exception e) {
		}
	}
	
	public <T> void generatePDF(List<T> list,ExcelDataFormatter edf)
			throws Exception {
		Field[] fields = ReflectUtils.getClassFieldsAndSuperClassFields(list.get(0).getClass());
		List<PdfPCell> cellList=new ArrayList<PdfPCell>();
		Excel excel = null;
		for (Field field : fields) {
			field.setAccessible(true);
			excel = field.getAnnotation(Excel.class);
			if (excel == null || excel.skip() == true) {
				continue;
			}
			
			// 写入标题
			PdfPCell  cell = new PdfPCell();
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setPhrase(new Phrase(excel.name(), keyfont));
			cellList.add(cell);
		}
		Class classType = list.get(0).getClass();
		// 创建一个只有5列的表格
		PdfPTable table = createTable(cellList.size());
		// 添加备注,靠左，不显示边框
		table.addCell(createCell("", keyfont, Element.ALIGN_LEFT, cellList.size(), false));
		// 设置表头
		for (int i = 0; i < cellList.size(); i++) {
			table.addCell(cellList.get(i));
		}
		int fregmentSize=1000;
		int k=0;
		int i=0;
		for (T t : list) {
			int columnIndex = 0;
			Object o = null;
			for (Field field : fields) {
				PdfPCell cell = new PdfPCell();
				field.setAccessible(true);

				// 忽略标记skip的字段
				excel = field.getAnnotation(Excel.class);
				if (excel == null || excel.skip() == true) {
					continue;
				}
				
				o = field.get(t);
				// 如果数据为空，跳过
				if (o == null){
					cell.setPhrase(new Phrase("", textfont));
				}else{
					// 处理日期类型
					if (o instanceof Date) {
						if (edf == null) {
							cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
						}else{
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
							} else {
								if(StringUtil.isNotEmpty(map.get(field.getName()))){
									cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),map.get(field.getName())), textfont));
								}else{
									cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
								}
							}
						}
					} else if (o instanceof Double || o instanceof Float) {
						cell.setPhrase(new Phrase(((Double) field.get(t)).toString(), textfont));
					} else if (o instanceof Boolean) {
						Boolean bool = (Boolean) field.get(t);
						if (edf == null) {
							cell.setPhrase(new Phrase((bool).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((bool).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(bool.toString()
										.toLowerCase())).toString(), textfont));
							}
						}

					} else if (o instanceof Integer) {

						Integer intValue = (Integer) field.get(t);

						if (edf == null) {
							cell.setPhrase(new Phrase((intValue).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((intValue).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(intValue.toString())).toString(), textfont));
							}
						}
					} else if (o instanceof String) {
						String intValue = (String) field.get(t);
						if (edf == null) {
							cell.setPhrase(new Phrase((intValue).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((intValue).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(intValue.toString())).toString(), textfont));
							}
						}
					} else {
						cell.setPhrase(new Phrase(field.get(t).toString(), textfont));
					}
				}
				cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
				cell.setHorizontalAlignment(Element.ALIGN_CENTER);
				table.addCell(cell);
			}
			 i++;
			 if (i != 0 && i % fregmentSize == fregmentSize - 1) {
		            System.out.println("第[ " + (i + 1) + " ]行进行内存释放 " + ((k++) + 1) + " th");
		            document.add(table);
		            table.deleteBodyRows();
		            table.setSkipFirstHeader(true);
		      }
		}
		if(i<fregmentSize){
			document.add(table);
		}
		// 关闭流
		document.close();
	}
	
	
	public <T> void generatePDF(List<T> list,ExcelDataFormatter edf,Map<String,Excel> excels)
			throws Exception {
		Field[] fields = ReflectUtils.getClassFieldsAndSuperClassFields(list.get(0).getClass());
		List<PdfPCell> cellList=new ArrayList<PdfPCell>();
		Excel excel = null;
		for (Field field : fields) {
			field.setAccessible(true);
			excel = excels.get(field.getName());
			if (excel == null || excel.skip() == true) {
				continue;
			}
			// 写入标题
			PdfPCell  cell = new PdfPCell();
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			cell.setPhrase(new Phrase(excel.name(), keyfont));
			cellList.add(cell);
		}
		Class classType = list.get(0).getClass();
		// 创建一个只有5列的表格
		PdfPTable table = createTable(cellList.size());
		// 添加备注,靠左，不显示边框
		table.addCell(createCell("", keyfont, Element.ALIGN_LEFT, cellList.size(), false));
		// 设置表头
		for (int i = 0; i < cellList.size(); i++) {
			table.addCell(cellList.get(i));
		}
		int fregmentSize=1000;
		int k=0;
		int i=0;
		for (T t : list) {
			int columnIndex = 0;
			Object o = null;
			for (Field field : fields) {
				PdfPCell cell = new PdfPCell();
				field.setAccessible(true);

				// 忽略标记skip的字段
				excel = excels.get(field.getName());
				if (excel == null || excel.skip() == true) {
					continue;
				}
				
				o = field.get(t);
				// 如果数据为空，跳过
				if (o == null){
					cell.setPhrase(new Phrase("", textfont));
				}else{
					// 处理日期类型
					if (o instanceof Date) {
						if (edf == null) {
							cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
						}else{
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
							} else {
								if(StringUtil.isNotEmpty(map.get(field.getName()))){
									cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),map.get(field.getName())), textfont));
								}else{
									cell.setPhrase(new Phrase(DateUtils.formatDate((Date) field.get(t),"yyyy-MM-dd HH:mm:ss"), textfont));
								}
							}
						}
					} else if (o instanceof Double || o instanceof Float) {
						cell.setPhrase(new Phrase(((Double) field.get(t)).toString(), textfont));
					} else if (o instanceof Boolean) {
						Boolean bool = (Boolean) field.get(t);
						if (edf == null) {
							cell.setPhrase(new Phrase((bool).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((bool).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(bool.toString()
										.toLowerCase())).toString(), textfont));
							}
						}

					} else if (o instanceof Integer) {

						Integer intValue = (Integer) field.get(t);

						if (edf == null) {
							cell.setPhrase(new Phrase((intValue).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((intValue).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(intValue.toString())).toString(), textfont));
							}
						}
					} else if (o instanceof String) {
						String intValue = (String) field.get(t);
						if (edf == null) {
							cell.setPhrase(new Phrase((intValue).toString(), textfont));
						} else {
							Map<String, String> map = edf.get(field.getName());
							if (map == null) {
								cell.setPhrase(new Phrase((intValue).toString(), textfont));
							} else {
								cell.setPhrase(new Phrase((map.get(intValue.toString())).toString(), textfont));
							}
						}
					} else {
						cell.setPhrase(new Phrase(field.get(t).toString(), textfont));
					}
				}
				cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
				cell.setHorizontalAlignment(Element.ALIGN_CENTER);
				table.addCell(cell);
			}
			 i++;
			 if (i != 0 && i % fregmentSize == fregmentSize - 1) {
		            System.out.println("第[ " + (i + 1) + " ]行进行内存释放 " + ((k++) + 1) + " th");
		            document.add(table);
		            table.deleteBodyRows();
		            table.setSkipFirstHeader(true);
		      }
		}
		if(i<fregmentSize){
			document.add(table);
		}
		// 关闭流
		document.close();
	}
	
	public <T> void generatePDF(String[][] head, List<T> list, int colNum)
			throws Exception {
		Class classType = list.get(0).getClass();
		// 创建一个只有5列的表格
		PdfPTable table = createTable(colNum);
		// 添加备注,靠左，不显示边框
		table.addCell(createCell("", keyfont, Element.ALIGN_LEFT, colNum, false));
		// 设置表头
		for (int i = 0; i < colNum; i++) {
			table.addCell(createCell(head[i][0], keyfont, Element.ALIGN_CENTER));
		}
		if (null != list && list.size() > 0) {
			int size = list.size();
			for (int i = 0; i < size; i++) {
				T t = list.get(i);
				for (int j = 0; j < colNum; j++) {
					// 获得首字母
					String firstLetter = head[j][1].substring(0, 1)
							.toUpperCase();

					// 获得get方法,getName,getAge等
					String getMethodName = "get" + firstLetter
							+ head[j][1].substring(1);

					Method method;
					try {
						// 通过反射获得相应的get方法，用于获得相应的属性值
						method = classType.getMethod(getMethodName,
								new Class[] {});
						try {
							// 添加数据
							Object value=method.invoke(t, new Class[] {});
							if(value!=null){
								table.addCell(createCell(value.toString(),
										textfont));
							}else{
								table.addCell(createCell("",
										textfont));
							}
						} catch (IllegalArgumentException e) {
							e.printStackTrace();
						} catch (IllegalAccessException e) {
							e.printStackTrace();
						} catch (InvocationTargetException e) {
							e.printStackTrace();
						}
					} catch (SecurityException e) {
						e.printStackTrace();
					} catch (NoSuchMethodException e) {
						e.printStackTrace();
					}
				}
			}
		}
		try {
			// 将表格添加到文档中
			document.add(table);
		} catch (DocumentException e) {
			e.printStackTrace();
		}

		// 关闭流
		document.close();
	}

	/**
	 * 提供外界调用的接口，生成以head为表头，list为数据的pdf
	 * 
	 * @param head
	 *            //数据表头
	 * @param list
	 *            //数据
	 * @return //excel所在的路径
	 * @throws Exception
	 */
	public <T> String generatePDFs(String saveFilePathAndName, String[][] head,
			List<T> list) throws Exception {
		File file = new File(saveFilePathAndName);
		try {
			file.createNewFile();
		} catch (IOException e1) {
			e1.printStackTrace();
		}
		initFile(file);
		try {
			file.createNewFile(); // 生成一个pdf文件
		} catch (IOException e) {
			e.printStackTrace();
		}
		new CreatePdf(file).generatePDF(head, list, head.length);
		return saveFilePathAndName;
	}
	/**
	 * 根据Excel打印数据
	 * @param saveFilePathAndName
	 * @param list
	 * @param edf
	 * @param group
	 * @return
	 * @throws Exception
	 */
	public <T> String generatePDFs(String saveFilePathAndName,
			List<T> list,ExcelDataFormatter edf,Class group) throws Exception {
		File file = new File(saveFilePathAndName);
		try {
			file.createNewFile();
		} catch (IOException e1) {
			e1.printStackTrace();
		}
		initFile(file);
		try {
			file.createNewFile(); // 生成一个pdf文件
		} catch (IOException e) {
			e.printStackTrace();
		}
		new CreatePdf(file).generatePDF(list,edf,group);
		return saveFilePathAndName;
	}
	/**
	 * 动态配置excel打印数据
	 * @param saveFilePathAndName
	 * @param list
	 * @param edf
	 * @param excels
	 * @return
	 * @throws Exception
	 */
	public <T> String generatePDFs(String saveFilePathAndName,
			List<T> list,ExcelDataFormatter edf,Map<String,Excel> excels) throws Exception {
		File file = new File(saveFilePathAndName);
		try {
			file.createNewFile();
		} catch (IOException e1) {
			e1.printStackTrace();
		}
		initFile(file);
		try {
			file.createNewFile(); // 生成一个pdf文件
		} catch (IOException e) {
			e.printStackTrace();
		}
		new CreatePdf(file).generatePDF(list,edf,excels);
		return saveFilePathAndName;
	}
	
	/**
	 * 根据excel不分组打印数据
	 * @param saveFilePathAndName
	 * @param list
	 * @param edf
	 * @param excels
	 * @return
	 * @throws Exception
	 */
	public <T> String generatePDFs(String saveFilePathAndName,
			List<T> list,ExcelDataFormatter edf) throws Exception {
		File file = new File(saveFilePathAndName);
		try {
			file.createNewFile();
		} catch (IOException e1) {
			e1.printStackTrace();
		}
		initFile(file);
		try {
			file.createNewFile(); // 生成一个pdf文件
		} catch (IOException e) {
			e.printStackTrace();
		}
		new CreatePdf(file).generatePDF(list,edf);
		return saveFilePathAndName;
	}

}