/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysRoleMenu{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.system;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.system.SysRoleMenu;

@MyBatisDao
public interface SysRoleMenuDao extends CrudDao<SysRoleMenu>{

	/**
	 * 指定角色全部菜单权限
	 * @param roleId
	 * @return
	 */
	List<SysRoleMenu> getAllUserPermissionByRoleId(@Param(value="roleId") String roleId);

	public SysRoleMenu getEntityByRole(SysRoleMenu sysRoleMenu);
}