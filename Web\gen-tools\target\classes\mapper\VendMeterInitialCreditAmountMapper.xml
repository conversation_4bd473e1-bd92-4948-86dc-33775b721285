<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.VendMeterInitialCreditAmountMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.VendMeterInitialCreditAmount">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="Utility_ID" jdbcType="VARCHAR" property="utilityId" />
    <result column="Meter_ID" jdbcType="VARCHAR" property="meterId" />
    <result column="Init_Credit_Amount" jdbcType="DECIMAL" property="initCreditAmount" />
    <result column="Payment_Time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="State" jdbcType="INTEGER" property="state" />
    <result column="Sales_ID" jdbcType="VARCHAR" property="salesId" />
    <result column="Create_Date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="Save_DB_Date" jdbcType="TIMESTAMP" property="saveDbDate" />
    <result column="Readme" jdbcType="VARCHAR" property="readme" />
  </resultMap>
  
  <insert id="insertCredit"	parameterType="com.power7000.model.VendMeterInitialCreditAmount">
		<selectKey resultType="java.lang.String" order="BEFORE"	keyProperty="id">
					SELECT REPLACE(UUID(),'-','')
	  	</selectKey>
		insert into PPM_VEND_INITIAL_CREDIT_AMOUNT
		(id,METER_ID,INIT_CREDIT_AMOUNT,PAYMENT_TIME,STATE,SALES_ID,CREATE_DATE,README)
		values
		(#{id,jdbcType=VARCHAR},#{meterId},#{initCreditAmount},#{paymentTime},#{state},#{salesId},#{createDate},#{readme})
	</insert>
  
    <!-- 根据电表资产编号查询初始信用金额 -->
  <select id="findInitialCreditAmountByMeterMsCode" resultType="com.power7000.model.VendMeterInitialCreditAmount" parameterType="java.util.Map">
   	SELECT ica.* from PPM_VEND_INITIAL_CREDIT_AMOUNT ica where ica.Meter_ID = #{meterId,jdbcType=VARCHAR}
   	limit 1;
  </select>
  
</mapper>