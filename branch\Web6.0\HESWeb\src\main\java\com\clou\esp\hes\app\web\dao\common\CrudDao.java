package com.clou.esp.hes.app.web.dao.common;

import java.io.Serializable;
import java.util.List;

import com.clou.esp.hes.app.web.model.data.DataUserLog;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

/**
 * DAO支持类实现
 * 
 * <AUTHOR>
 * @version 2016-12-09
 * @param
 */
public interface CrudDao<T extends Serializable> {

	/**
	 * 获取单条数据
	 * 
	 * @param id
	 * @return
	 */
	public T getEntity(String id);

	/**
	 * 获取单条数据
	 * 
	 * @param entity
	 * @return
	 */
	public T get(T entity);

	/**
	 * 查询数据列表，如果需要分页，请设置分页对象，如：entity.setPage(new Page());
	 * 
	 * @param entity
	 * @return
	 */
	public List<T> getList(T entity);

	/**
	 * 查询所有数据列表
	 * 
	 * @see public List findAllList(T entity)
	 * @return
	 */
	public List<T> getAllList();

	/**
	 * 插入数据
	 * 
	 * @param entity
	 * @return
	 */
	public int insert(T entity);

	/**
	 * 更新数据
	 * 
	 * @param entity
	 * @return
	 */
	public int update(T entity);

	/**
	 * 删除数据
	 * 
	 * @param id
	 * @see public int delete(T entity)
	 * @return
	 */
	public Integer deleteById(String id);

	/**
	 * 删除数据（一般为逻辑删除，更新del_flag字段为1）
	 * 
	 * @param entity
	 * @return
	 */
	public int delete(T entity);

	/**
	 * 
	 * 分页查询
	 * 
	 * @param jqGridSearchTo
	 * @return
	 * @see
	 */
	public List<T> getForJqGrid(JqGridSearchTo jqGridSearchTo);

	/**
	 * 获取条数
	 * 
	 * @param entity
	 * @return
	 */
	public Long getCount(T entity);
	
	/**
	 * 新增data user log
	 * @Description 
	 * @param userId
	 * @param logType
	 * @param logSubType
	 * @param detail void
	 * <AUTHOR> 
	 * @Time 2018年4月4日 上午11:17:48
	 */
	void insertDataUserLog(DataUserLog log);
}