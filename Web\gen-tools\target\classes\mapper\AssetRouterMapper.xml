<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.AssetRouterMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.AssetRouter">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="communicator_id" jdbcType="VARCHAR" property="communicatorId" />
    <result column="channel_id" jdbcType="VARCHAR" property="channelId" />
    <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId" />
  </resultMap>
</mapper>