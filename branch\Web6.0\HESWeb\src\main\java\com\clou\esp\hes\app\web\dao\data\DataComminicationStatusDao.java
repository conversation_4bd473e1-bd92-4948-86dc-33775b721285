/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataComminicationStatus{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-19 07:34:34
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataComminicationStatus;

@MyBatisDao
public interface DataComminicationStatusDao extends CrudDao<DataComminicationStatus>{

	public List<DataComminicationStatus> getCommStatus(@Param(value = "sns") List<String> sns);
	public List<DataComminicationStatus> getListData(Map<String,Object> map);
	
}