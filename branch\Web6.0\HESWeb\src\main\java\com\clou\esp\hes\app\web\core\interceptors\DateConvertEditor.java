package com.clou.esp.hes.app.web.core.interceptors;

import java.beans.PropertyEditorSupport;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.springframework.util.StringUtils;

import com.power7000g.core.util.base.DateUtils;

/**
 * 
 * <AUTHOR>
 * 
 */
public class DateConvertEditor extends PropertyEditorSupport {

	public void setAsText(String text) throws IllegalArgumentException {
		if (StringUtils.hasText(text)) {
			try {
				if (DateUtils.isValidDate(text, "yyyy-MM-dd HH:mm:ss")) {
					setValue(DateUtils.parseDate(text, "yyyy-MM-dd HH:mm:ss"));
				} else if (DateUtils.isValidDate(text, "yyyy-MM-dd HH:mm")) {
					setValue(DateUtils.parseDate(text, "yyyy-MM-dd HH:mm"));
				} else if (DateUtils.isValidDate(text, "yyyy-MM-dd HH")) {
					setValue(DateUtils.parseDate(text, "yyyy-MM-dd HH"));
				} else if (DateUtils.isValidDate(text, "yyyy-MM-dd")) {
					setValue(DateUtils.parseDate(text, "yyyy-MM-dd"));
				} else if (DateUtils.isValidDate(text, "yyyy-MM")) {
					setValue(DateUtils.parseDate(text, "yyyy-MM"));
				} else if (DateUtils.isValidDate(text, "MM/dd/yyyy HH:mm:ss")) {
					setValue(DateUtils.parseDate(text, "MM/dd/yyyy HH:mm:ss"));
				} else if (DateUtils.isValidDate(text, "MM/dd/yyyy HH:mm")) {
					setValue(DateUtils.parseDate(text, "MM/dd/yyyy HH:mm"));
				} else if (DateUtils.isValidDate(text, "MM/dd/yyyy HH")) {
					setValue(DateUtils.parseDate(text, "MM/dd/yyyy HH"));
				} else if (DateUtils.isValidDate(text, "MM/dd/yyyy")) {
					setValue(DateUtils.parseDate(text, "MM/dd/yyyy"));
				} else if (DateUtils.isValidDate(text, "yyyy")) {
					setValue(DateUtils.parseDate(text, "yyyy"));
				} else {
					throw new IllegalArgumentException(
							"Could not parse date, date format is error ");
				}
			} catch (ParseException ex) {
				IllegalArgumentException iae = new IllegalArgumentException(
						"Could not parse date: " + ex.getMessage());
				iae.initCause(ex);
				throw iae;
			}
		} else {
			setValue(null);
		}
	}
}
