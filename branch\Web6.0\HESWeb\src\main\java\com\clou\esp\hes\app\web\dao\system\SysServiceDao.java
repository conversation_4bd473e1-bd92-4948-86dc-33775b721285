/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysService{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-29 08:53:20
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.system;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.system.SysService;

@MyBatisDao
public interface SysServiceDao extends CrudDao<SysService>{

	public List<SysService> getListByEntity(SysService service);
	
	public List<SysService> vaildServiceName(String name);
}