package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.power7000g.core.util.base.StringUtil;

/**
 * 
 * <AUTHOR>
 * 
 */
public class FormValidationTag extends TagSupport {
	private static final long serialVersionUID = 8360534826228271024L;

	protected String title = "";
	protected String formid = "formobj";		// 表单FORM ID
	protected String tiptype = "4";				// 校验方式
	protected String action;					// 表单提交路径
	protected String beforeSubmit;				// 提交前处理函数
	protected String beforeCheck;				// 提交校验前执行处理函数
	protected String callback;					// 这里执行回调操作;
	protected String btnsub = "btn_sub";		// 以ID为标记触发提交事件
	protected String btnreset = "btn_reset";	// 以ID为标记触发提交事件
	protected String formColumn = "";
	protected String extParamter = "";			//form表单扩展参数

	public void setBtnsub(String btnsub) {
		this.btnsub = btnsub;
	}

	public void setBtnreset(String btnreset) {
		this.btnreset = btnreset;
	}

	public void setFormid(String formid) {
		this.formid = formid;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public int doStartTag() throws JspException {
		JspWriter out = null;
		// title = MutiLangUtil.doMutiLang(title);
		StringBuffer sb = new StringBuffer();
		try {
			out = this.pageContext.getOut();
			/*sb.append("<div class=\"row\">");
			sb.append("<div class=\"col-sm-12\">");
			sb.append("<div class=\"ibox-content \">");
			sb.append("<div class=\"row\">");
			sb.append("<div class=\"portlet\">");
			sb.append("<div class=\"portlet-title\">");
			// sb.append("<div class=\"tools\">");
			// sb.append("<a href=\"javascript:;\" class=\"collapse query_form_collaspe\">");
			// sb.append("</a>");
			// sb.append("</div>");
			sb.append("</div>");
			sb.append("<div class=\"portlet-body");
			if (formColumn != null && formColumn.equals("2")) {
				sb.append("-double");
			}
			sb.append("\">");*/
			sb.append("<div class=\"col-md-12 margin_top15\"><form class=\"form-horizontal\" role=\"form\" id=\""
					+ formid + "\" name=\"" + formid
					+ "\" onsubmit=\"return false;\"");
			sb.append("action=\"" + action + "\"");
			if(StringUtil.isNotEmpty(extParamter)){
				sb.append(extParamter);
			}
			sb.append("method=\"post\">");
			sb.append("<input type=\"hidden\" id=\"btn_sub\" class=\"btn_sub\">");
			out.print(sb.toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspException {
		StringBuffer sb = new StringBuffer();
		JspWriter out = null;
		title=MutiLangUtil.doMutiLang(title);
		try {
			String webVer="ver="+ResourceUtil.getSessionattachmenttitle("hesweb.system.pageVer");
			ServletRequest request = this.pageContext.getRequest();
			String path = ((HttpServletRequest) request).getContextPath();
			String basePath = request.getScheme() + "://"
					+ request.getServerName() + ":" + request.getServerPort()
					+ path;
			out = this.pageContext.getOut();
			sb.append("</form></div>");
			/*sb.append("</div>");
			sb.append("</div>");
			sb.append("</div>");
			sb.append("</div>");
			sb.append("</div>");
			sb.append("</div>");*/
			sb.append("<script type=\"text/javascript\" src=\"" + basePath
					+ "/plug-in/jquery/jquery.min.js\"></script>");
			sb.append("<script type=\"text/javascript\" src=\"" + basePath
					+ "/plug-in/bootstrap/js/bootstrap.min.js\"></script>");
			sb.append("<script type=\"text/javascript\" src=\""
					+ basePath
					+ "/plug-in/jquery-slimscroll/jquery.slimscroll.min.js\"></script>");
			if (MutiLangUtil.getLangCode().equals("en-us")) {
				//英语
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath + "/plug-in/Validform/js/Validform_v5.3.1_min_en.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath + "/plug-in/Validform/js/Validform_Datatype_en.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath + "/plug-in/Validform/js/datatype_en.js?"+webVer+"\"></script>");
			} else if (MutiLangUtil.getLangCode().equals("rf")) {
				// 法语待翻译
			} else if(MutiLangUtil.getLangCode().equals("ru")){
				//俄罗斯语
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath	+ "/plug-in/Validform/js/Validform_v5.3.1_min_ru.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath	+ "/plug-in/Validform/js/Validform_Datatype_ru.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath	+ "/plug-in/Validform/js/datatype_ru.js?"+webVer+"\"></script>");
			} else if(MutiLangUtil.getLangCode().equals("zh-cn")){
				//中文
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath	+ "/plug-in/Validform/js/Validform_v5.3.1_min_zh-cn.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath	+ "/plug-in/Validform/js/Validform_Datatype_zh-cn.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath	+ "/plug-in/Validform/js/datatype_zh-cn.js?"+webVer+"\"></script>");
			} else {
				//英文
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath+ "/plug-in/Validform/js/Validform_v5.3.1_min_en.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\""	+ basePath	+ "/plug-in/Validform/js/Validform_Datatype_en.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath	+ "/plug-in/Validform/js/datatype_en.js?"+webVer+"\"></script>");
			}

			sb.append("<script type=\"text/javascript\">");
			sb.append("$(function() {");
			sb.append("var validForm=$(\"#" + formid + "\").Validform({");
			sb.append("tiptype : " + tiptype + ",");
			// sb.append("btnSubmit : \".layui-layer-btn0\", ");
			sb.append("btnSubmit : \"#btn_sub\", ");
			sb.append("btnReset : \"#btn_reset\",");
			// 处理表单提交校验的
			sb.append("ajaxPost : true,");
			if (StringUtil.isNotEmpty(beforeCheck)) {
				sb.append("beforeCheck:function(curform){");
				sb.append("return " + beforeCheck + "(curform);");
				sb.append("},");
			}
			sb.append("beforeSubmit:function(curform){");
			sb.append("window.parent.layer.load();");
			if (StringUtil.isNotEmpty(beforeSubmit)) {
				sb.append("return " + beforeSubmit + "(curform);");
			}
			sb.append("},");

			sb.append("callback : function(data) {");
			if (StringUtil.isNotEmpty(callback)) {
				sb.append("var index = parent.layer.getFrameIndex(window.name);");
				sb.append(callback + "(data,index);");
			} else {
				sb.append("if (data.success) {");
				sb.append("window.parent.layer.msg(data.msg, {icon : 1});");
				sb.append("window.parent.layer.closeAll('loading');");
				sb.append("var index = parent.layer.getFrameIndex(window.name);");
				sb.append("window.parent.layer.close(index);");
				sb.append("} else {");
				sb.append("window.parent.layer.closeAll('loading');");
				sb.append("window.parent.layer.msg(data.msg, {icon : 2});");
				sb.append("}");
			}
			sb.append("}");
			sb.append("});");
			sb.append("var view='" + request.getParameter("view") + "';");
			sb.append("if(view=='1'){");
			sb.append("$('input,select,textarea,a,button',$('form[name=\""
					+ formid + "\"]')).attr('disabled','disabled');");
			sb.append("}");
			sb.append("});");
			sb.append("</script>");
			out.print(sb.toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				sb.setLength(0);
				out.clearBuffer();
			} catch (Exception e2) {
			}
		}
		return EVAL_PAGE;
	}

	public void setBeforeSubmit(String beforeSubmit) {
		this.beforeSubmit = beforeSubmit;
	}

	public String getTiptype() {
		return tiptype;
	}

	public void setTiptype(String tiptype) {
		this.tiptype = tiptype;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getFormid() {
		return formid;
	}

	public String getAction() {
		return action;
	}

	public String getBeforeSubmit() {
		return beforeSubmit;
	}

	public String getBtnsub() {
		return btnsub;
	}

	public String getBtnreset() {
		return btnreset;
	}

	public String getBeforeCheck() {
		return beforeCheck;
	}

	public void setBeforeCheck(String beforeCheck) {
		this.beforeCheck = beforeCheck;
	}

	public String getCallback() {
		return callback;
	}

	public void setCallback(String callback) {
		this.callback = callback;
	}

	public String getFormColumn() {
		return formColumn;
	}

	public void setFormColumn(String formColumn) {
		this.formColumn = formColumn;
	}

	public String getExtParamter() {
		return extParamter;
	}

	public void setExtParamter(String extParamter) {
		this.extParamter = extParamter;
	}

}
