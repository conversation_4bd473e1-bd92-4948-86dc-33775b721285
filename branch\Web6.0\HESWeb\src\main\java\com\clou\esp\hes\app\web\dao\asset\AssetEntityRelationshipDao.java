package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface AssetEntityRelationshipDao extends CrudDao<AssetEntityRelationship>{

	/**
	 * 根据父实体编号、父实体类型查询出此实体挂靠的所有类型的实体，并且做分页操作
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<AssetEntityRelationship> findLinkedEntityMeterList(JqGridSearchTo jqGridSearchTo);
	
	public List<AssetMeter> findLinkedMeterList(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 根据父实体编号、父实体类型查询出此实体挂靠的所有类型的实体，并且做分页操作
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<AssetEntityRelationship> findLinkedEntityTransformerList(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 查询未绑定的独立电表（即没有绑定到变压器上的电表）集合
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<AssetMeter> findUnLinkedMeterList(JqGridSearchTo jqGridSearchTo);
	
	/**
	 * 查询未绑定的变压器列表
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<AssetTransformer> findUnLinkedTransformerList(JqGridSearchTo jqGridSearchTo);

	/**
	 * 批量插入记录
	 * @param relationships
	 * @return
	 */
	public int batchInsert(List<AssetEntityRelationship> relationships);
	
	/**
	 * 根据实体删除记录
	 * @param entity
	 */
	public void deleteRelationByEntity(AssetEntityRelationship entity);
}
