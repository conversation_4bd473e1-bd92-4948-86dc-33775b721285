package com.clou.esp.hes.app.web.core.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetCustomer;
import com.clou.esp.hes.app.web.model.asset.AssetEntityRelationship;
import com.clou.esp.hes.app.web.model.asset.AssetLine;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.clou.esp.hes.app.web.service.asset.AssetCommunicatorService;
import com.clou.esp.hes.app.web.service.asset.AssetCustomerService;
import com.clou.esp.hes.app.web.service.asset.AssetEntityRelationshipService;
import com.clou.esp.hes.app.web.service.asset.AssetLineManagementService;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.asset.AssetTransformerService;
import com.google.common.collect.Lists;

public class AssetSelectTools {

	
	public  static List<AssetMeter> getAssetByType(AssetMeterService assetMeterService,
			AssetCommunicatorService assetCommunicatorService,AssetEntityRelationshipService assetEntityRelationshipService,AssetCustomerService assetCustomerService,
			AssetLineManagementService  assetLineService,AssetTransformerService  assetTransformerService,
			String orgIds , String assetType ,String sn)
	{
		List<AssetMeter> ams =  new ArrayList();
		List<String> orgLists= Arrays.asList(orgIds.split(","));
		if("Meter".equals(assetType)){
			AssetMeter assetMeter = new AssetMeter();
			assetMeter.setOrgIdList(orgLists);
			assetMeter.setSn(sn);
			ams = assetMeterService.getList(assetMeter);
		}else if("Commnuicator".equals(assetType)){
			AssetCommunicator assetCommunicator = new AssetCommunicator();
			assetCommunicator.setSn(sn);
			List<AssetCommunicator> acs = assetCommunicatorService.getList(assetCommunicator);
		    if(acs != null && acs.size() > 0){
		    	 AssetMeter assetMeter = new AssetMeter();
				assetMeter.setOrgIdList(orgLists);
				assetMeter.setCommunicatorId(acs.get(0).getId());
				ams = assetMeterService.getList(assetMeter); ;
	
		    }
		}else if("Line".equals(assetType)){
			 AssetEntityRelationship   ship = new AssetEntityRelationship();
			 AssetLine assetLine= new AssetLine();
			 assetLine.setSn(sn);
			 assetLine = assetLineService.get(assetLine);
			 if(assetLine != null){
	        	 ship.setParentId(assetLine.getId());
			 }else{
				 return ams;
			 }
        	 ship.setParentType(3);
        	 ship.setType(1);
        	 List<AssetEntityRelationship> shipList= assetEntityRelationshipService.getList(ship);

        	 List<String> meterIds = Lists.newArrayList();
       	
       		 if(shipList!=null&&shipList.size()>0) {
       			 for(AssetEntityRelationship shipTmp:shipList) {
       				 meterIds.add(shipTmp.getId());
       			 }
       			 List<AssetMeter> ams1= assetMeterService.getByIds(meterIds);
       			  
       			  if(ams1 != null && ams1.size() > 0){
       				 for(AssetMeter assetMeter : ams1){
       					if(orgLists.contains(assetMeter.getOrgId())){
       						ams.add(assetMeter);
       					}
       				 }
       				
       			  }
       			
       		 }
		}else if("Transformer".equals(assetType)){
			 AssetEntityRelationship   ship = new AssetEntityRelationship();
			 
			 AssetTransformer assetTransformer= new AssetTransformer();
			 assetTransformer.setSn(sn);
			 assetTransformer = assetTransformerService.get(assetTransformer);
			 if(assetTransformer != null){
	        	 ship.setParentId(assetTransformer.getId());
			 }else{
				 return ams;
			 }
	       	 ship.setParentType(4);
	       	 ship.setType(1);
	       	 List<AssetEntityRelationship> shipList= assetEntityRelationshipService.getList(ship);
	       	 List<String> meterIds = Lists.newArrayList();
      		
      		 if(shipList!=null&&shipList.size()>0) {
      			 for(AssetEntityRelationship shipTmp:shipList) {
      				 meterIds.add(shipTmp.getId());
      			 }
      			List<AssetMeter> ams1= assetMeterService.getByIds(meterIds);
     			  
     			  if(ams1 != null && ams1.size() > 0){
     				 for(AssetMeter assetMeter : ams1){
     					if(orgLists.contains(assetMeter.getOrgId())){
     						ams.add(assetMeter);
     					}
     				 }
     				
     			  }
      		 }
		}else if("Customer".equals(assetType)){
			 AssetCustomer assetCustomer = new AssetCustomer();
			 assetCustomer.setSn(sn);
			 AssetCustomer customer= assetCustomerService.get(assetCustomer);
			 AssetMeter assetMeter= assetMeterService.getEntity(customer.getMeterId()); 
			 if(assetMeter != null){
				 ams.add(assetMeter);
			 }
		}

		return ams;	
	}
}
