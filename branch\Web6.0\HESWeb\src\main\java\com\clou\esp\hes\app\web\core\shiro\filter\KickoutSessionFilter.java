package com.clou.esp.hes.app.web.core.shiro.filter;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.Serializable;
import java.util.LinkedHashMap;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.WebUtils;

import com.clou.esp.hes.app.web.core.shiro.cache.VCache;
import com.clou.esp.hes.app.web.core.shiro.session.ShiroSessionRepository;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.power7000g.core.util.json.AjaxJson;

/**
 * 相同帐号登录控制
 * 
 * <AUTHOR>
 * 
 */
@SuppressWarnings({ "unchecked", "static-access" })
public class KickoutSessionFilter extends AccessControlFilter {
	// 静态注入
	static String kickoutUrl;
	// 在线用户
	final static String ONLINE_USER = KickoutSessionFilter.class
			.getCanonicalName() + "_online_user";
	// 踢出状态，true标示踢出
	final static String KICKOUT_STATUS = KickoutSessionFilter.class
			.getCanonicalName() + "_kickout_status";
	static VCache cache;

	// session获取
	static ShiroSessionRepository shiroSessionRepository;

	@Override
	protected boolean isAccessAllowed(ServletRequest request,
			ServletResponse response, Object mappedValue) throws Exception {
		// HttpServletRequest httpRequest = ((HttpServletRequest) request);
		// String url = httpRequest.getRequestURI();
		Subject subject = getSubject(request, response);
		// 如果是相关目录 or 如果没有登录 就直接return true
		// && !subject.isRemembered()
		if (!subject.isAuthenticated() && !subject.isRemembered()) {
			return Boolean.TRUE;
		}
		Session session = subject.getSession();
		Serializable sessionId = session.getId();
		/**
		 * 判断是否已经踢出 1.如果是Ajax 访问，那么给予json返回值提示。 2.如果是普通请求，直接跳转到登录页
		 */
		Boolean marker = (Boolean) session.getAttribute(KICKOUT_STATUS);
		if (null != marker && marker) {
			// shiroSessionRepository.deleteSession(sessionId);
			AjaxJson json = new AjaxJson();
			// 判断是不是Ajax请求
			if (ShiroFilterUtils.isAjax(request)) {
				json.setErrorMsg("You are already logged in elsewhere, please login again!");
				out(response, json.toString());
			}
			return Boolean.FALSE;
		}

		// 从缓存获取用户-Session信息 <UserId,SessionId>
		LinkedHashMap<String, Serializable> infoMap = cache.get(ONLINE_USER,
				LinkedHashMap.class);
		// 如果不存在，创建一个新的
		infoMap = null == infoMap ? new LinkedHashMap<String, Serializable>()
				: infoMap;
		// 获取tokenId
		String userId = TokenManager.getUserId();
		// 如果已经包含当前Session，并且是同一个用户，跳过。
		if (infoMap.containsKey(userId) && infoMap.containsValue(sessionId)) {
			// 更新存储到缓存1个小时（这个时间最好和session的有效期一致或者大于session的有效期）
			cache.setex(ONLINE_USER, infoMap, 3600);
			return Boolean.TRUE;
		}
		// 如果用户相同，Session不相同，那么就要处理了
		/**
		 * 如果用户Id相同,Session不相同 1.获取到原来的session，并且标记为踢出。 2.继续走
		 */
		if (infoMap.containsKey(userId) && !infoMap.containsValue(sessionId)) {
			Serializable oldSessionId = infoMap.get(userId);
			Session oldSession = shiroSessionRepository
					.getSession(oldSessionId);
			if (oldSession != null) {
				oldSession.setAttribute(KICKOUT_STATUS, Boolean.TRUE);
				shiroSessionRepository.saveSession(oldSession);
			}
			infoMap.put(userId, sessionId);
			cache.setex(ONLINE_USER, infoMap, 3600);
			 if (null != oldSession) {
			 // 标记session已经踢出
			 oldSession.setAttribute(KICKOUT_STATUS, Boolean.TRUE);
			 shiroSessionRepository.saveSession(oldSession);// 更新session
			 } else {
			 shiroSessionRepository.deleteSession(oldSessionId);
			 infoMap.remove(userId);
			 // 存储到缓存1个小时（这个时间最好和session的有效期一致或者大于session的有效期）
			 cache.setex(ONLINE_USER, infoMap, 3600);
			 }
			return Boolean.TRUE;
		}

		if (!infoMap.containsKey(userId) && !infoMap.containsValue(sessionId)) {
			infoMap.put(userId, sessionId);
			// 存储到缓存1个小时（这个时间最好和session的有效期一致或者大于session的有效期）
			cache.setex(ONLINE_USER, infoMap, 3600);
		}
		return Boolean.TRUE;
	}

	@Override
	protected boolean onAccessDenied(ServletRequest request,
			ServletResponse response) throws Exception {
		// 先退出
		Subject subject = getSubject(request, response);
		subject.logout();
		WebUtils.getSavedRequest(request);
		// 再重定向
		// WebUtils.issueRedirect(request, response, kickoutUrl);
		out(response,"<script type=\"text/javascript\">if(window.parent.layer){window.parent.layer.confirm('Log in overtime, please login again(kickout)!', {"
				  +" title:'Message', closeBtn: 0, btn: ['OK'],  icon: 0, skin: 'layer-ext-moon'}, function(){window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";});}else{window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";}</script>");
		return false;
	}

	private void out(ServletResponse hresponse, String str) throws IOException {
		try {
			hresponse.setCharacterEncoding("UTF-8");
			PrintWriter out = hresponse.getWriter();
			out.println(str);
			out.flush();
			out.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static void setShiroSessionRepository(
			ShiroSessionRepository shiroSessionRepository) {
		KickoutSessionFilter.shiroSessionRepository = shiroSessionRepository;
	}

	public static String getKickoutUrl() {
		return kickoutUrl;
	}

	public static void setKickoutUrl(String kickoutUrl) {
		KickoutSessionFilter.kickoutUrl = kickoutUrl;
	}

}
