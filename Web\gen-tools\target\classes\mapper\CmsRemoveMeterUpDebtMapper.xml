<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.CmsRemoveMeterUpDebtMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.CmsRemoveMeterUpDebt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="CRMUD_ID" jdbcType="VARCHAR" property="crmudId" />
    <result column="METER_ID" jdbcType="VARCHAR" property="meterId" />
    <result column="MS_CODE" jdbcType="VARCHAR" property="msCode" />
    <result column="MS_USER_NO" jdbcType="VARCHAR" property="msUserNo" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="USER_TYPE" jdbcType="VARCHAR" property="userType" />
    <result column="METER_DEBT_ID" jdbcType="VARCHAR" property="meterDebtId" />
    <result column="DEBT_TOTAL" jdbcType="DECIMAL" property="debtTotal" />
    <result column="SYSTEM_HANDLE_FLAG" jdbcType="INTEGER" property="systemHandleFlag" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  
  <select id="findVendMeterDebtByUserNo" parameterType="java.util.Map" resultType="com.power7000.model.CmsRemoveMeterUpDebt">
		select crm.* from CMS_REMOVE_METER_UP_DEBT crm
		<where>
			<if test="msUserNo !=null and msUserNo != ''">
				crm.MS_USER_NO = #{msUserNo,jdbcType=VARCHAR}
			</if>
			<if test="meterId !=null and meterId != ''">
				AND crm.METER_ID = #{meterId,jdbcType=VARCHAR}
			</if>
			<if test="userId !=null and userId != ''">
				AND crm.USER_ID = #{userId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
  
</mapper>