package com.clou.esp.hes.app.web.core.shiro.service.impl;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.filter.mgt.DefaultFilterChainManager;
import org.apache.shiro.web.filter.mgt.PathMatchingFilterChainResolver;
import org.apache.shiro.web.servlet.AbstractShiroFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;

import com.clou.esp.hes.app.web.core.shiro.service.ShiroManager;
import com.clou.esp.hes.app.web.core.shiro.service.config.INI4j;

/**
 * 动态加载权限 Service
 * 
 * <AUTHOR>
 * 
 */
public class ShiroManagerImpl implements ShiroManager {

	// 注意/r/n前不能有空格
	private static final String CRLF = "\r\n";

	@Resource
	@Autowired
	private ShiroFilterFactoryBean shiroFilterFactoryBean;
	/*@Resource
	@Autowired
	private SysOperationService sysOperationService;
	@Resource
	@Autowired
	private SysFunctionService sysFunctionService;*/

	@Override
	public String loadFilterChainDefinitions() {
		StringBuffer sb = new StringBuffer();
		sb.append(getFixedAuthRule());// 固定权限，采用读取配置文件
		return sb.toString();
	}

	/**
	 * 从配额文件获取固定权限验证规则串
	 */
	private String getFixedAuthRule() {
		String fileName = "shiro_base_auth.ini";
		ClassPathResource cp = new ClassPathResource(fileName);
		INI4j ini = null;
		try {
			ini = new INI4j(cp.getFile());
		} catch (IOException e) {
			e.printStackTrace();
		}
		String section = "base_auth";
		Set<String> keys = ini.get(section).keySet();
		StringBuffer sb = new StringBuffer();
		for (String key : keys) {
			String value = ini.get(section, key);
			sb.append(key).append(" = ").append(value).append(CRLF);
		}
		// 从数据库中获取所有需要拦截的权限
		/*if (!Globals.isDevelopment) {
			SysFunction sf = new SysFunction();
			List<SysFunction> sfList = sysFunctionService.getList(sf);
			SysOperation so = new SysOperation();
			if (sfList != null && sfList.size() > 0) {
				for (SysFunction function : sfList) {
					String url = function.getFunctionurl();
					if (url == null || url.trim().length() == 0) {
						continue;
					}
					url = "/" + url;
					url += " = kickout,simple,login,permission";
					if (!sb.toString().contains(url)) {
						sb.append(url).append(CRLF);
					}
					so.setFunctionid(function.getId());
					List<SysOperation> soList = sysOperationService.getList(so);
					if (soList != null && soList.size() > 0) {
						for (SysOperation operation : soList) {
							url = operation.getOperationUrl();
							if (url == null || url.trim().length() == 0) {
								continue;
							}
							url = "/" + url;
							url += " = kickout,simple,login,permission";
							if (!sb.toString().contains(url)) {
								sb.append(url).append(CRLF);
							}
						}
					}

				}
			}
		}*/
		sb.append("/** = kickout,simple,login,pcCodeOverdue");
		System.out.println("权限===" + sb.toString());
		return sb.toString();

	}

	// 此方法加同步锁
	@Override
	public synchronized void reCreateFilterChains() {
		// ShiroFilterFactoryBean shiroFilterFactoryBean =
		// (ShiroFilterFactoryBean)
		// SpringContextUtil.getBean("shiroFilterFactoryBean");
		AbstractShiroFilter shiroFilter = null;
		try {
			shiroFilter = (AbstractShiroFilter) shiroFilterFactoryBean
					.getObject();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(
					"get ShiroFilter from shiroFilterFactoryBean error!");
		}

		PathMatchingFilterChainResolver filterChainResolver = (PathMatchingFilterChainResolver) shiroFilter
				.getFilterChainResolver();
		DefaultFilterChainManager manager = (DefaultFilterChainManager) filterChainResolver
				.getFilterChainManager();

		// 清空老的权限控制
		manager.getFilterChains().clear();

		shiroFilterFactoryBean.getFilterChainDefinitionMap().clear();
		shiroFilterFactoryBean
				.setFilterChainDefinitions(loadFilterChainDefinitions());
		// 重新构建生成
		Map<String, String> chains = shiroFilterFactoryBean
				.getFilterChainDefinitionMap();
		for (Map.Entry<String, String> entry : chains.entrySet()) {
			String url = entry.getKey();
			String chainDefinition = entry.getValue().trim().replace(" ", "");
			manager.createChain(url, chainDefinition);
		}

	}

	public void setShiroFilterFactoryBean(
			ShiroFilterFactoryBean shiroFilterFactoryBean) {
		this.shiroFilterFactoryBean = shiroFilterFactoryBean;
	}

}
