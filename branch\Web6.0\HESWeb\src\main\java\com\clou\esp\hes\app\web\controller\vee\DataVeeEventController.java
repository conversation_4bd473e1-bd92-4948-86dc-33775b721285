/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataVeeEvent{ } 
 * 
 * 摘    要： dataVeeEvent
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-20 04:22:44
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.util.OrganizationUtils;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.google.common.collect.Lists;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.DataVeeEvent;
import com.clou.esp.hes.app.web.service.asset.AssetMeterService;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.clou.esp.hes.app.web.service.vee.DataVeeEventService;

/**
 * <AUTHOR>
 * @时间：2018-12-20 04:22:44
 * @描述：dataVeeEvent类
 */
@Controller
@RequestMapping("/dataVeeEventController")
public class DataVeeEventController extends BaseController{

 	@Resource
    private DataVeeEventService dataVeeEventService;
 	@Resource
    private AssetMeterService   assetMeterService;
 	@Resource
 	private SysOrgService 		sysOrgService;
 	
	/**
	 * 跳转到dataVeeEvent列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/dataVeeEventList");
    }

	/**
	 * 跳转到dataVeeEvent新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dataVeeEvent")
	public ModelAndView dataVeeEvent(DataVeeEvent dataVeeEvent,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dataVeeEvent.getId())){
			try {
                dataVeeEvent=dataVeeEventService.getEntity(dataVeeEvent.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dataVeeEvent", dataVeeEvent);
		}
		return new ModelAndView("/vee/dataVeeEvent");
	}


	/**
	 * dataVeeEvent查询分页
	 * @param request
	 * @param model
	 * @return
	 * @throws ParseException 
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request,
    		String startDate,String endDate,String meterId,String commId) throws ParseException {
       // TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        String[] classIds = request.getParameterValues("classIds[]");
        String[] eventIds = request.getParameterValues("eventIds[]");
        
        Date d = new Date();
        
//        SimpleDateFormat sdf1 = new SimpleDateFormat(ResourceUtil.getSessionattachmenttitle("local.date.time.formatter"));
//
//
//	    String endDate1=sdf1.format(new Date());
        
		Date sDate = StringUtil.isNotEmpty(startDate)?DateUtils.parseDate(startDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):DateUtils.parseDate(DateUtils.formatDate(d, "yyyy-MM-dd")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
 		Date eDate = StringUtil.isNotEmpty(endDate)?DateUtils.parseDate(endDate, ResourceUtil.getSessionattachmenttitle("local.date.time.formatter")):new Date();
 		
 		List<String> deviceIds=null;
 		if(StringUtils.isNotEmpty(commId)) {
 			deviceIds=Lists.newArrayList();
 			deviceIds=this.assetMeterService.getMeterIdsByCommunicatorId(commId);
 		}else	if(StringUtils.isNotEmpty(meterId)) {
 			deviceIds=Lists.newArrayList();
 			deviceIds.add(meterId);
 		}
 		
 		SysUser su = TokenManager.getToken();
		List<String> orgIdList = OrganizationUtils.assembleOrgIds(su,sysOrgService,false,0);
		
 		Map<String,Object> map = jqGridSearchTo.getMap();
 		
 		
 		map.put("orgIds", orgIdList);
 		map.put("deviceIds", deviceIds);
        map.put("classIds", classIds);
        map.put("eventIds", eventIds);
 		map.put("startDate", sDate);
 		map.put("endDate", eDate);
 		
        try {
             j=dataVeeEventService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dataVeeEvent信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DataVeeEvent dataVeeEvent,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dataVeeEventService.deleteById(dataVeeEvent.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dataVeeEvent信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DataVeeEvent dataVeeEvent,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DataVeeEvent t=new  DataVeeEvent();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dataVeeEvent.getId())){
        	t=dataVeeEventService.getEntity(dataVeeEvent.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dataVeeEvent, t);
				dataVeeEventService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dataVeeEventService.save(dataVeeEvent);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}