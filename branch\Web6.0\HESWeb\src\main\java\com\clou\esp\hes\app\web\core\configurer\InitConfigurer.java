package com.clou.esp.hes.app.web.core.configurer;

import java.util.Properties;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;

import com.power7000g.core.util.encrypt.PasswordUtil;

public class InitConfigurer extends PropertyPlaceholderConfigurer

{

	@Override
	protected void processProperties(
			ConfigurableListableBeanFactory beanFactory, Properties props)
			throws BeansException {

		String password = "";
		// 默认数据库 billing
		password = props.getProperty("jdbc.password");
		if (password != null) {
			props.setProperty("jdbc.password",
					PasswordUtil.decrypt(password, "root",// 解密密钥，root，写死
							PasswordUtil.getStaticSalt()));
		}

		// 测试数据库 test
		password = props.getProperty("jdbc.test.password");
		if (password != null) {
			props.setProperty("jdbc.test.password",
					PasswordUtil.decrypt(password, "root",// 解密密钥，root，写死
							PasswordUtil.getStaticSalt()));
		}

		super.processProperties(beanFactory, props);

	}
	public static void main(String[] args) {
		System.out.println(PasswordUtil.encrypt( "123","root", PasswordUtil.getStaticSalt()));
		System.out.println(PasswordUtil.decrypt("2441e2178c17697a8399efd1235de8fc", "root",// 解密密钥，root，写死
				PasswordUtil.getStaticSalt()));
	}
}