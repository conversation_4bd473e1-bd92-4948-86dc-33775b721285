package com.clou.esp.hes.app.web.core.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfileDi;
import com.clou.esp.hes.app.web.model.data.DataMeterEvent;
import com.clou.esp.hes.app.web.model.data.DataStatisticsEvent;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroup;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.dict.DictProfileDataItem;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.google.common.collect.Lists;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：MutiLangUtil.java 版权：Copyright by Power7000g Team 描述： 修改人：严浪
 * 修改时间：2017年3月20日 跟踪单号： 修改单号： 修改内容：
 */
public class MutiLangUtil {
	/**
	 * 初始化语言
	 */
	private static Map<String, MutiLangEntity> mule = new HashMap<String, MutiLangEntity>();
	/**
	 * 语言选择
	 */
	private static Map<String, String> suLang = new HashMap<String, String>();
	
	/**
	 * 语言包翻译键值对
	 */
	public static Map<String, String> i18nTransMap = new HashMap<String, String>();

	private static HttpServletRequest request;
	
	public static final String MENU_I18N="menu";
	public static final String DATAITEM_I18N="dictDataItem";
	public static final String DATAITEM_GROUP_I18N="dictDataItemGroup";
	public static final String DICT_I18N="dict";
	public static final String PROFILE_I18N="profile";
	
    public static HttpServletRequest getRequest() {
        return request;
    }

    public static void setRequest(HttpServletRequest request) {
        MutiLangUtil.request = request;
    }
	/**
	 * 用户选择语言
	 * 
	 * @param langCode
	 */
	public static void setUserLangCode(String langCode) {
		SysUser su = TokenManager.getToken();
		if (su != null) {
			suLang.put(su.getId(), langCode);
		}
	}

	/**
	 * 初始化语言
	 * 
	 * @param webPath
	 * @param langCode
	 * @param directoryName
	 */
	public static void setMutiLang(String webPath, String langCode,
			String directoryName) {
		String filePathSub = webPath + "\\" + directoryName
				+ "\\language.jsp.json";
		try {
			String encoding = "utf-8";
			File file = new File(filePathSub);
			Long filelength = file.length();
			byte[] filecontent = new byte[filelength.intValue()];
			FileInputStream in = new FileInputStream(file);
			in.read(filecontent);
			in.close();
			String jsonStr = new String(filecontent, encoding);
			MutiLangEntity mle = new MutiLangEntity();
			mle.setLang(langCode, jsonStr);
			mule.put(langCode, mle);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * Description: 1、根据两个key拼接… 2、… Implement: 1、lanKey内容如:{0}删除成功…
	 * 2、langArg内容如:用户 3,最终结果如: 用户删除成功
	 * 
	 * @param lanKey
	 * @param langArg
	 * @return
	 * @see
	 */
	public static String doMutiLang(String lanKey, String langArg) {
		   String language = getUserLang();
	        if (StringUtil.isEmpty(language)) {
	            language = "zh_cn";
	        }
	        MutiLangEntity me = mule.get(language.toLowerCase());
	        if (me == null) {
	            me = mule.get("en-us");
	        }
	        if (me != null) {
	            return me.getLang(lanKey, langArg);
	        }
	        else {
	            return lanKey;
	        }
	}

	
	
	/**
     * 返回用户选择语言，没有则默认英文
     * 
     * @return
     */
    private static String getUserLang() {
        SysUser su = TokenManager.getToken();
        if (su == null) {
            String language = "";
            Cookie cookies[] = request.getCookies(); // 获取Cookie中的所有内容
            Cookie sCookie = null;
            if (cookies != null) {
                for (int i = 0; i < cookies.length; i++ ) {
                    sCookie = cookies[i];
                    if (sCookie != null) {
                        if (sCookie.getName().equals("hesWebLanguage")) {
                            try {
                                language = URLDecoder.decode(sCookie.getValue(), "UTF-8");
                            }
                            catch (UnsupportedEncodingException e) {
                                e.printStackTrace();
                            }
                            // System.out.println("language:" + language);
                            request.getSession(true).setAttribute("language", language);
                        }
                    }
                }
            }
            if (StringUtil.isNotEmpty(language)) {
                return language;
            }
            return "en-us";
        }

        String langCode = suLang.get(su.getId());
        if (StringUtil.isEmpty(langCode)) return "en-us";
        return langCode;
    }

	/**
	 * Description: <br>
	 * 1、返回对应语言内容<br>
	 * 2、没有则返回传入的key<br>
	 * Implement: <br>
	 * 1、…<br>
	 * 2、…<br>
	 * 
	 * @param langCode
	 * @return
	 * @see
	 */
	public static String doMutiLang(String lanKey) {
	     String language = getUserLang();
	        if (StringUtil.isEmpty(language)) {
	            language = "zh_cn";
	        }
	        MutiLangEntity me = mule.get(language.toLowerCase());
	        if (me == null) {
	            me = mule.get("en-us");
	        }
	        return me.getLang(lanKey);
	}

	/**
	 * Description: <br>
	 * 1、返回对应语言内容<br>
	 * 2、没有则返回传入的key<br>
	 * Implement: <br>
	 * 1、…<br>
	 * 2、…<br>
	 * 
	 * @param langCode
	 * @return
	 * @see
	 */
	public static String doMutiLangByLang(String lanKey, String language) {
		if(StringUtil.isEmpty(language)){
			language="zh_cn";
		}
		MutiLangEntity me=mule.get(language.toLowerCase());
		if(me==null){
			me=mule.get("en-us");
		}
		return me.getLang(lanKey);
	}

	/**
	 * 
	 * 返回当前语言CODE 默认英文
	 * 
	 * @return
	 * @see
	 */
	public static String getLangCode() {
		SysUser su = TokenManager.getToken();
		if (su == null)
			return "en-us";
		String langCode = suLang.get(su.getId());
		if (StringUtil.isEmpty(langCode))
			return "en-us";
		return langCode;
	}

	
	public static String assemblyI18nData(String parentKey , String childrenKey) {
		String res = "";
		if(StringUtils.isNotEmpty(parentKey) && StringUtils.isNotEmpty(childrenKey)) {
			String key = parentKey + Globals.DOT + childrenKey;
			String i18nData = MutiLangUtil.doMutiLang(key);
			if(StringUtils.isNotEmpty(i18nData)&&!key.equals(i18nData)) {
				res = i18nData;
			}else {
				res = childrenKey;
			}
		}
		return res;
	}
	
	
	public static  <T> T assemblyI18nData(T obj){
		String parentKey = "";
		String key = "";
		
		if(obj == null){
			return obj;
		}
		
		if(StringUtils.isNotEmpty(obj.getClass().getName())){
			parentKey = obj.getClass().getSimpleName();
			parentKey = parentKey.substring(0, 1).toLowerCase() + parentKey.substring(1);
		}
	
		String fieldNameStr = MutiLangUtil.i18nTransMap.get(parentKey);
		 if(StringUtils.isEmpty(fieldNameStr)){
              return obj;
		 }
		
		try {

			String[] fieldNames = fieldNameStr.split(","); 
			for(String fieldName : fieldNames){
		
				key = parentKey + Globals.DOT +  BeanUtils.getProperty(obj, fieldName);
				
				String i18nData= MutiLangUtil.doMutiLang(key);
				if(StringUtils.isNotEmpty(i18nData) && !key.equals(i18nData)) {
					PropertyUtil.setProperty(obj, fieldName, i18nData);			
				}
		
			}
		} catch (IllegalAccessException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (NoSuchMethodException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		 return obj;
	}
	
	
	public static  <T> List<T> assemblyI18nData(List<T> list){
		String parentKey = "";
		if(list != null && list.size() > 0){
			if(StringUtils.isNotEmpty(list.get(0).getClass().getName())){
				parentKey = list.get(0).getClass().getSimpleName();
				parentKey = parentKey.substring(0, 1).toLowerCase() + parentKey.substring(1);
			}
		}
	
		String fieldNameStr = MutiLangUtil.i18nTransMap.get(parentKey);
		 if(StringUtils.isEmpty(fieldNameStr)){
              return list;
		 }
	
		 if(list!=null && list.size()>0) {
			String[] fieldNames = fieldNameStr.split(","); 
			for(String fieldName : fieldNames){
			
				for(T t : list) {
					String key = "";
					try {
						key = parentKey + Globals.DOT + BeanUtils.getProperty(t, fieldName);
					} catch (IllegalAccessException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					} catch (InvocationTargetException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					} catch (NoSuchMethodException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					String i18nData= MutiLangUtil.doMutiLang(key);
					if(StringUtils.isNotEmpty(i18nData) && !key.equals(i18nData)) {
						PropertyUtil.setProperty(t, fieldName, i18nData);			
					}
					
				}
			
			}
		 }
		 return list;
	}
	
	public static  <T> List<T> assemblyI18nData(String parentKey  , List<T> list  , String fieldNameStr){
	
		 if(list!=null && list.size()>0) {
			 String[] fieldNames = fieldNameStr.split(","); 
			 for(String fieldName : fieldNames){
				 for(T t : list) {
					String key = "";
					try {
						key = parentKey + Globals.DOT + BeanUtils.getProperty(t, fieldName);
					} catch (IllegalAccessException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					} catch (InvocationTargetException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					} catch (NoSuchMethodException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					String i18nData= MutiLangUtil.doMutiLang(key);
					if(StringUtils.isNotEmpty(i18nData) && !key.equals(i18nData)) {
						PropertyUtil.setProperty(t, fieldName, i18nData);			
					}
				}
			}
		 }
		 return list;
	}
	
	
	
}
