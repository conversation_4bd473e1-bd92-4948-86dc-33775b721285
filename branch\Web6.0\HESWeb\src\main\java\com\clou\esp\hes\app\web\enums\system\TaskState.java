package com.clou.esp.hes.app.web.enums.system;


/**
 * @ClassName: TaskState
 * @Description: 任务运行状态
 * <AUTHOR>
 * @date 2018年7月11日 下午5:29:02
 *
 */
public enum TaskState {

	Upcoming(0,"Upcoming"),
	Processing(1,"Processing"),
	Success(2,"Success"),
	Failed(3,"Failed"),
	Timeout(4,"Timeout"),
	Cancelled(5,"Cancelled"),
	Unknown(-1,"Unknown");
	
	private int id;
	private String state;
	
	private TaskState(int id,String state){
		this.id = id;
		this.state = state;
	}
	
	public static TaskState parseState(int id){
		for (TaskState state : values()) {
			if(id == state.getId())
				return state;
		}
		return Unknown;
	}
	
	public static TaskState parseState(String status) {
		for (TaskState state : values()) {
			if(state.getState().equalsIgnoreCase(status))
				return state;
		}
		return Unknown;
	}
	
	public int getId(){
		return id;
	}
	
	public String getState(){
		return state;
	}
}
