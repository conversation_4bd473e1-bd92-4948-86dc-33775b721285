/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEvent{ } 
 * 
 * 摘    要： dictVeeEvent
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-12-21 07:44:42
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;

import com.clou.esp.hes.app.web.model.vee.DictVeeEvent;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventService;

/**
 * <AUTHOR>
 * @时间：2018-12-21 07:44:42
 * @描述：dictVeeEvent类
 */
@Controller
@RequestMapping("/dictVeeEventController")
public class DictVeeEventController extends BaseController{

 	@Resource
    private DictVeeEventService dictVeeEventService;

	/**
	 * 跳转到dictVeeEvent列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/dictVeeEventList");
    }

	/**
	 * 跳转到dictVeeEvent新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictVeeEvent")
	public ModelAndView dictVeeEvent(DictVeeEvent dictVeeEvent,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictVeeEvent.getId())){
			try {
                dictVeeEvent=dictVeeEventService.getEntity(dictVeeEvent.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictVeeEvent", dictVeeEvent);
		}
		return new ModelAndView("/vee/dictVeeEvent");
	}


	/**
	 * dictVeeEvent查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        try {
             j=dictVeeEventService.getForJqGrid(jqGridSearchTo);
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        return j;
    }
    
    /**
     * 删除dictVeeEvent信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictVeeEvent dictVeeEvent,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictVeeEventService.deleteById(dictVeeEvent.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictVeeEvent信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictVeeEvent dictVeeEvent,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictVeeEvent t=new  DictVeeEvent();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictVeeEvent.getId())){
        	t=dictVeeEventService.getEntity(dictVeeEvent.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictVeeEvent, t);
				dictVeeEventService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dictVeeEventService.save(dictVeeEvent);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}