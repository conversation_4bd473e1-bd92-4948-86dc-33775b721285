package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.tag.vo.TreeTypeDirection;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：JqueryZtreeTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：自定义树形菜单可单选多选标签
 * 修改人：严浪
 * 修改时间：2017年3月27日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class CopyOfJqueryZtreeTag extends TagSupport {

    /**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    /**
     * 树形框id
     */
    private String id;
    /**
     * 树形框类型
     */
    private TreeTypeDirection treeType=TreeTypeDirection.Default; //默认,点选,复选
    /**
     * 请求地址
     */
    private String url;
    /**
     * 动态加载方式
     */
    private boolean isDload=false;
    /**
     * 自定义点击事件  event, treeId, treeNode
     */
    private String onClick;
    /**
     * 自定义view
     */
    private String viewStr;
    /**
     * 加载完成执行的方法
     */
    private String loadingComp;
    
    public int doEndTag() throws JspTagException {
        JspWriter out = null;
        try {
            out = this.pageContext.getOut();
            out.print(end().toString());
            out.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }finally{
            try {
                out.clear();
                out.close();
            } catch (Exception e2) {
            }
        }
        return EVAL_PAGE;
    }
    
    public StringBuffer end() {
        StringBuffer sb = new StringBuffer();
        sb.append("<ul id=\""+id+"\" class=\"ztree chosen-drop-ztree\"></ul>");
        sb.append("<script type=\"text/javascript\">");
        sb.append("var "+id+"Setting ={");
//        if(isDload){
//            sb.append("  async: {");
//            sb.append("      enable: true,");
//            sb.append("      url: "+id+"getUrlByNodeId");
//            sb.append("   },");
//        }
        sb.append("   check: {");
        sb.append("   chkboxType: { \"Y\": \"ps\", \"N\": \"ps\" },");
        if(treeType.equals(TreeTypeDirection.Check)){
            sb.append(" enable: true");
        }else if(treeType.equals(TreeTypeDirection.Radio)){
            sb.append(" enable: true,");
            sb.append(" chkStyle: \"radio\",");
            sb.append(" radioType: \"all\" ");
        }else{
            sb.append("     enable: false");
        }
        sb.append("    },");
        sb.append("   data: {");
        sb.append("     simpleData: {");
        sb.append("       enable: true");
        sb.append("     }");
        sb.append("   },");
        sb.append("   callback: {");
        if(StringUtil.isNotEmpty(onClick)){
            sb.append("     onClick: function(event,treeId,treeNode){"+(StringUtil.isNotEmpty(onClick)?onClick+"(event,treeId,treeNode);":"")+"},");
        }
        if(isDload){
            sb.append("     beforeExpand: "+id+"beforeExpand,");
            sb.append("     onAsyncSuccess: "+id+"onAsyncSuccess,");
            sb.append("     onAsyncError: "+id+"onAsyncError");
        }
        sb.append("  }");
        if(StringUtil.isNotEmpty(viewStr)){
        	sb.append(",view: "+viewStr+" ");
        }
        sb.append("  };");
        if(isDload){    
           sb.append(" var "+id+"zNodes =[{name:\"全部\", id:\"0\",isParent:true}];");
           sb.append(" function "+id+"getUrlByNodeId(treeId, treeNode) {");
           sb.append("    return \""+url+"?id=\"+treeNode.id+\"&pId=\"+treeNode.pId;");
           sb.append(" }");
          
           sb.append("function "+id+"beforeExpand(treeId, treeNode) {");

           sb.append("    if (!treeNode.isAjaxing) {");
           sb.append("        "+id+"ajaxGetNodes(treeNode, \"refresh\");");
           sb.append("    alert('fuck1');");
           sb.append("        return true;");
           sb.append("    } else {");
           sb.append("    alert('fuck2');");
           sb.append("        return false;");
           sb.append("    }");
           sb.append(" }");
           sb.append("function "+id+"onAsyncSuccess(event, treeId, treeNode, msg) {");
           sb.append("    if (!msg || msg.length == 0) {");
           sb.append("       return;");
           sb.append("   }");
           sb.append("   var "+id+"zTree = $.fn.zTree.getZTreeObj(\""+id+"\");");
           sb.append("    "+id+"zTree.updateNode(treeNode);");
           sb.append("    "+id+"zTree.selectNode(treeNode.children[0]);");
           sb.append(" }");
           sb.append("function "+id+"onAsyncError(event, treeId, treeNode, XMLHttpRequest, textStatus, errorThrown) {");
           sb.append("   var zTree = $.fn.zTree.getZTreeObj(\""+id+"\");");
       //    sb.append(" window.parent.layer.msg('Request error', {icon: 2});");
           sb.append("    zTree.updateNode(treeNode);");
           sb.append(" }");
           sb.append("function "+id+"ajaxGetNodes(treeNode, reloadType) {");
           sb.append("    var zTree = $.fn.zTree.getZTreeObj(\""+id+"\");");
           
           sb.append("   var urlVar = \""+url+"?id=\"+treeNode.id+\"&pId=\"+treeNode.pId;");
           //begin 加载ajax数据
           sb.append("   $.ajax({");
      		sb.append("       type: 'POST',");
      	  
      		sb.append("       url: urlVar,");
      		sb.append("       dataType: 'json',");
      		sb.append("       success: function(data){");
      		sb.append("       if(data.success){");
      		 //treeNode.halfCheck = false;
      		sb.append("var parentZNode = zTree.getNodeByParam(\"id\", treeNode.id, null);");//获取指定父节点

      		sb.append("newNode = zTree.addNodes(parentZNode,data.obj, false);");
      	   
      		
      		sb.append("        }else{");
      		sb.append(" window.parent.layer.msg(data.msg, {icon: 2});");
      		sb.append("        }");
      		sb.append("     },error: function (msg) {");
      	//	sb.append(" window.parent.layer.msg('Request error', {icon: 2});");
      		sb.append("      }");
      		sb.append("    });");
           
           sb.append("    if (reloadType == \"refresh\") {");
           sb.append("        zTree.updateNode(treeNode);");
           sb.append("   }");
           sb.append("   zTree.reAsyncChildNodes(treeNode, reloadType, true);");
           sb.append("}");
           sb.append("   $(document).ready(function(){");
           
           //old
//           sb.append("       $.fn.zTree.init($(\"#"+id+"\"), "+id+"Setting, "+id+"zNodes);");
           //new 
        sb.append("   $.ajax({");
   		sb.append("       type: 'POST',");
   		sb.append("       url: '"+url+"',");
   		sb.append("       dataType: 'json',");
   		sb.append("       success: function(data){");
   		sb.append("       if(data.success){");
   		sb.append("           $.fn.zTree.init($(\"#"+id+"\"), "+id+"Setting, data.obj);");
   		if(StringUtil.isNotEmpty(loadingComp)){
   			sb.append(loadingComp+"();");
   		}
   		sb.append("        }else{");
   		sb.append(" window.parent.layer.msg(data.msg, {icon: 2});");
   		sb.append("        }");
   		sb.append("     },error: function (msg) {");
   	//	sb.append(" window.parent.layer.msg('Request error', {icon: 2});");
   		sb.append("      }");
   		sb.append("    });");
        sb.append("      });");
        }else{
        	if(StringUtil.isNotEmpty(url)){
        		sb.append("   $.ajax({");
        		sb.append("       type: 'POST',");
        		sb.append("       url: '"+url+"',");
        		sb.append("       dataType: 'json',");
        		sb.append("       success: function(data){");
        		sb.append("       if(data.success){");
        		sb.append("           $.fn.zTree.init($(\"#"+id+"\"), "+id+"Setting, data.obj);");
        		if(StringUtil.isNotEmpty(loadingComp)){
        			sb.append(loadingComp+"();");
        		}
        		sb.append("        }else{");
        		sb.append(" window.parent.layer.msg(data.msg, {icon: 2});");
        		sb.append("        }");
        		sb.append("     },error: function (msg) {");
        	//	sb.append(" window.parent.layer.msg('Request error', {icon: 2});");
        		sb.append("      }");
        		sb.append("    });");
        	}
        }
        sb.append("</script>");
        
        return sb;
    }

    

    public void setId(String id) {
        this.id = id;
    }
    

    public void setTreeType(TreeTypeDirection treeType) {
        this.treeType = treeType;
    }

    public void setUrl(String url) {
    	this.url = url;
    }
    

    public void setIsDload(boolean isDload) {
        this.isDload = isDload;
    }


    public void setOnClick(String onClick) {
        this.onClick = onClick;
    }

	public boolean isDload() {
		return isDload;
	}

	public void setDload(boolean isDload) {
		this.isDload = isDload;
	}

	public String getId() {
		return id;
	}

	public TreeTypeDirection getTreeType() {
		return treeType;
	}

	public String getUrl() {
		return url;
	}

	public String getOnClick() {
		return onClick;
	}

	public String getLoadingComp() {
		return loadingComp;
	}

	public void setLoadingComp(String loadingComp) {
		this.loadingComp = loadingComp;
	}

	public String getViewStr() {
		return viewStr;
	}

	public void setViewStr(String viewStr) {
		this.viewStr = viewStr;
	}

	
    
    
    

}
