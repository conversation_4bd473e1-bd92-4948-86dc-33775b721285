package com.clou.esp.hes.app.web.enums.system;

public enum EnumDeleteFlag {
	/**
	 * 1=已删除
	 */
	DELETED("1", "已删除"),
	/**
	 * 0=未删除
	 */
	NOT_DELETE("0", "未删除");
	private String index;
	private String remark;

	private EnumDeleteFlag(String index, String remark) {
		this.remark = remark;
		this.index = index;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
