package com.clou.esp.hes.app.web.core.tag.vo;

import java.util.HashMap;
import java.util.Map;

import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;

/**
 * 文件名：JqTreeGrid.java
 * 版权：Copyright by Power7000g Team
 * 描述：树形结构列表返回
 * 修改人：严浪
 * 修改时间：2017年3月15日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class JqTreeGrid {
    /**主键编号ID*/
    private String id;
    /**树等级*/
    private int level;
    /**是否为叶子*/
    private boolean isLeaf=false;
    /**上级ID*/
    private String parent;
    /**是否展开*/
    private boolean expanded=false;
    /**其他参数*/
    private Map<String,Object> fieldMap=new HashMap<String, Object>();
    
    public int getLevel() {
        return level;
    }
    public boolean isLeaf() {
        return isLeaf;
    }
    public String getParent() {
        return parent;
    }
    public boolean isExpanded() {
        return expanded;
    }
    /**
     * 层级
     * @param level 
     * @see
     */
    public void setLevel(int level) {
        this.level = level;
    }
    /**
     * 是否为树叶子 默认为:false
     * @param isLeaf 
     * @see
     */
    public void setLeaf(boolean isLeaf) {
        this.isLeaf = isLeaf;
    }
    /**
     * 上级ID 
     * @param parent 
     * @see
     */
    public void setParent(String parent) {
        this.parent = parent;
    }
    /**
     * 是否展开默认:false
     * @param expanded 
     * @see
     */
    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
    }
    /**
     * 加入其他参数
     * @param key
     * @param obj 
     * @see
     */
    public void setMapParam(String key,Object obj){
        this.fieldMap.put(key, obj);
    }
    
    
    public String getId() {
        return id;
    }
    /**
     * 主键ID
     * @param id 
     * @see
     */
    public void setId(String id) {
        this.id = id;
    }
    /**
     * 根据参数列表取对象值到map 
     * @param field
     * @param obj 
     * @see
     */
    public void setMapParam(String field[],Object obj){
        for(String f:field){
            Object o=TagUtil.fieldNametoValues(f, obj);
            this.fieldMap.put(f, o);
        }
    }
    
    /**
     * 返回结构数据
     * @return 
     * @see
     */
    public Map<String,Object> getMapPatam(){
        fieldMap.put("id", this.id);
        fieldMap.put("isLeaf", this.isLeaf);
        fieldMap.put("level", this.level);
        fieldMap.put("parent", this.parent);
        fieldMap.put("expanded", this.expanded);
        return this.fieldMap;
    }
    
    
    
    
}
