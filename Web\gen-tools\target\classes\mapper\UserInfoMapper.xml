<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.power7000.mapper.UserInfoMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.UserInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 27 01:50:31 CST 2016.
    -->
    <id column="id" property="id" jdbcType="VARCHAR"/>
    <result column="username" property="username" jdbcType="VARCHAR"/>
    <result column="net_name" property="netName" jdbcType="VARCHAR"/>
    <result column="job" property="job" jdbcType="VARCHAR"/>
    <result column="family_native_place" property="familyNativePlace" jdbcType="VARCHAR"/>
    <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
    <result column="email" property="email" jdbcType="VARCHAR"/>
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 27 01:50:31 CST 2016.
    -->
    delete from user_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.power7000.model.UserInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 27 01:50:31 CST 2016.
    -->
    <selectKey resultType="java.lang.String" keyProperty="id" order="AFTER">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_info (username, net_name, job,
    family_native_place, mobile, email
    )
    values (#{username,jdbcType=VARCHAR}, #{netName,jdbcType=VARCHAR}, #{job,jdbcType=VARCHAR},
    #{familyNativePlace,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}
    )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.power7000.model.UserInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 27 01:50:31 CST 2016.
    -->
    update user_info
    set username = #{username,jdbcType=VARCHAR},
      net_name = #{netName,jdbcType=VARCHAR},
      job = #{job,jdbcType=VARCHAR},
      family_native_place = #{familyNativePlace,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 27 01:50:31 CST 2016.
    -->
    select id, username, net_name, job, family_native_place, mobile, email
    from user_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 27 01:50:31 CST 2016.
    -->
    select id, username, net_name, job, family_native_place, mobile, email
    from user_info
  </select>
</mapper>