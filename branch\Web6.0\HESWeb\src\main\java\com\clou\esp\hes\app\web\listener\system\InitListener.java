package com.clou.esp.hes.app.web.listener.system;

import java.io.File;

import javax.servlet.ServletContextEvent;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import redis.clients.jedis.JedisPool;
import clouesp.hes.common.logger.logger.Logger;
import clouesp.hes.common.logger.logger.LoggerAppenderType;
import clouesp.hes.common.logger.loggerquery.LoggerQuery;

import com.clou.esp.hes.app.web.core.util.Configuration;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.redis.JedisUtils;

/**
 * 系统初始化监听器,在系统启动时运行,进行一些初始化工作
 * 
 * <AUTHOR>
 * 
 */
public class InitListener implements javax.servlet.ServletContextListener {

	public void contextDestroyed(ServletContextEvent arg0) {

	}
	/*redis.port=6379
			redis.timeout=5000
			redis.host=127.0.0.1
			redis.maxIdle=100
			redis.minIdle=10
			redis.testOnBorrow=true*/
	public void contextInitialized(ServletContextEvent event) {
		// WebApplicationContext webApplicationContext =
		// WebApplicationContextUtils
		// .getWebApplicationContext(event.getServletContext());
		/** 初始化语言开始  **/
		String path=event.getServletContext().getRealPath("\\locales");
		File file=new File(path);
		if (file.isFile()) {
            System.out.println("您给定的是一个文件"); // 判断给定目录是否是一个合法的目录，如果不是，输出提示
        } else {
            File[] fileLists = file.listFiles(); // 如果是目录，获取该目录下的内容集合
            for (int i = 0; i < fileLists.length; i++) { // 循环遍历这个集合内容
                if (fileLists[i].isDirectory()) {    //判断元素是不是一个目录
                	String name=fileLists[i].getName();
                	MutiLangUtil.setMutiLang(
            				event.getServletContext().getRealPath("\\locales"), name.toLowerCase(),
            				name);
                }
            }
        }
		
		//redis初始化
		String port=ResourceUtil.getSessionattachmenttitle("redis.port");
		String timeout=ResourceUtil.getSessionattachmenttitle("redis.timeout");
		Integer timeouti=4000;
		if(StringUtil.isNotEmpty(timeout)){
			timeouti=Integer.parseInt(timeout);
		}
		Integer porti=6379;
		if(StringUtil.isNotEmpty(port)){
			porti=Integer.parseInt(port);
		}
		GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
		genericObjectPoolConfig.setMaxTotal(100);
		genericObjectPoolConfig.setMaxIdle(16);
		genericObjectPoolConfig.setMinIdle(10);
		String host=ResourceUtil.getSessionattachmenttitle("redis.host");
		JedisPool jedis=new JedisPool(genericObjectPoolConfig, host, porti, timeouti);
		JedisUtils.setJedisPool(jedis);
		//初始化log配置
		Configuration.ReadConfiguration();

        //设置服务的IP及端口范围，在Configuration文件中有读取配置生成的示例。
        LoggerQuery.getInstance().setServices(Configuration.services);
        LoggerQuery.getInstance().setMulticastGroup(Configuration.multicastgroup, Configuration.multicastport);
        //启动日志查询客户端
        LoggerQuery.getInstance().start();

        //设置写日志路径
        Logger.getInstance().setIndexPath(Configuration.indexPath);
        //输出源固定为LoggerAppenderType.Lucene
        Logger.getInstance().setAppenderType(LoggerAppenderType.Lucene);
        //启动日志
        Logger.getInstance().start();

	}
}
