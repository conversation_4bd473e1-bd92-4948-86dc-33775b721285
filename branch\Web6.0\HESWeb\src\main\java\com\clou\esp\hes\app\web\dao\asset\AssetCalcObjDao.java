package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObjMap;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;


@MyBatisDao
public interface AssetCalcObjDao extends CrudDao<AssetCalcObj> {
	
	List<AssetCalcObjMap> getCalObjMapList(AssetCalcObjMap map);
	
	public List<DictDataitem> getDictDataitems(@Param(value = "measureId")String measureId,@Param(value = "dateType")String dateType);
	
}
