/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:55
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.asset.AssetMeterDto;
import com.clou.esp.hes.app.web.model.asset.VendMeterInitialCreditAmount;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface AssetMeterDao extends CrudDao<AssetMeter> {

	public int batchGetList(List<AssetMeter> mList);
	public int batchGetListPrepare(List<AssetMeter> mList);
	
	public int batchInsert(List<AssetMeter> mList);
	
	public int batchInsertPrepaid(List<AssetMeter> mList);
	
	public int batchInsertCredit(List<VendMeterInitialCreditAmount> mList);

	public List<AssetMeter> getListByRows(@Param(value = "rows") Integer rows);

	public List<AssetMeter> getListGroupByFwVersion();
	
	public List<AssetMeter> getFwVersionGroupByModel(@Param(value = "modelTypeId")String modelTypeId);
	
	/**
	 * 获取电表详细数据，包含集中器信息
	 * @Description 
	 * <AUTHOR> 
	 * @Time 2018年5月14日 下午3:34:02
	 */
	public AssetMeter getMeterDetailInfo(@Param(value = "id") String id);
	
	/**
	 * 根据SN号获取电表数据
	 * @Description 
	 * @param sn
	 * @return AssetMeter
	 * <AUTHOR> 
	 * @Time 2018年8月22日 上午10:29:59
	 */
	public AssetMeter getEntityBySN(@Param(value = "sn") String sn);
	public AssetMeter getEntityByMac(@Param(value = "mac") String mac);
	public AssetMeter getEntityByMacPLC(@Param(value = "mac") String mac);
	
	public List<AssetMeter> getBySns(@Param(value = "sns") List<String> sns);
	public List<AssetMeter> getByMacs(@Param(value = "sns") List<String> sns);
	public List<AssetMeter> getByIds(@Param(value = "ids") List<String> ids);
	
	public void deletePrepareMeter(@Param(value = "ids") List<String> ids);
	
	public List<AssetMeter> getForJqGridAdvanced(JqGridSearchTo jqGridSearchTo) ;
	public List<AssetMeter> getForJqGridPLC(@Param(value = "sn") String sn) ;
	
	//根据集中器id查询表id
	public List<String> getMeterIdsByCommunicatorId(String communicatorId);
	
	public List<AssetMeter> getListLimitTwenty(Map<String, Object> p);
	
	public List<AssetMeter> getListOfDCU(Map<String, Object> p);
	
	public List<AssetMeter> getPreListOfDCU(Map<String, Object> p);
	
	public List<AssetMeterDto> getMeterDtoInfo(@Param(value = "id") String id);
	
	public void savePLCMeter(AssetMeter meter);
	
	public AssetMeter getEntityPLC(@Param(value = "sn") String sn);
	
	public AssetMeter getEntityPLCExtend(@Param(value = "id") String id);
	
	public long getCountPrepare(AssetMeter meter);

	public void insertCredit(VendMeterInitialCreditAmount creditAmount);
	
	public void deletePpmAssetMeter(String id);
}