/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataIntegrity{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.clou.esp.hes.app.web.model.data.DataIntegrity;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DataIntegrityDao extends CrudDao<DataIntegrity> {

	public int deleteByTv(@Param(value = "tv") String tv,
			@Param(value = "type") String type);

	public List<AssetMeter> getAllMeterIntegrityByProfileId(
			@Param(value = "profileId") String profileId,
			@Param(value = "tv") String tv);

	public int batchInsert(@Param(value = "date") String date,@Param(value = "idType") String idType,
			@Param(value = "profileId") String profileId);

	public List<AssetMeter> getAllMeterIntegrityByIdType(
			@Param(value = "idType") String idType,
			@Param(value = "tv") String tv);

	public List<Map<String, Object>> getIntegritySum(Map<String, Object> p);

	public List<Map<String, Object>> getIntegrityNameSum(Map<String, Object> p);

	public int batchSave(List<DataIntegrity> diList);

	public int updateErrorData(@Param(value = "tv") String tv);

	public List<Map<String, Object>> getDataIntegrityByMap(
			Map<String, Object> params);

	public List<Map<String, Object>> getDataIntegrityByMaps(
			Map<String, Object> params);
	
	/**
	 * 分页查询,电表
	 * @param jqGridSearchTo
	 * @return
	 * @see
	 */
	public List<DataIntegrity> getForMeterJqGrid(JqGridSearchTo jqGridSearchTo);
	/**
	 * 分页查询,集中器
	 * @param jqGridSearchTo
	 * @return
	 * @see
	 */
	public List<DataIntegrity> getForCommunicatorJqGrid(JqGridSearchTo jqGridSearchTo);
}