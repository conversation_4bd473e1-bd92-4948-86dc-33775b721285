package com.clou.esp.hes.app.web.core.tag.vo;

/**
 * 文件名：JqueryZtree.java 版权：Copyright by Power7000g Team 描述：zTree数据结构 修改人：严浪 修改时间：2017年3月27日 跟踪单号：
 * 修改单号： 修改内容：
 */
public class ZtreeNode {
    private String name; // 节点显示的文本

    private boolean checked = false; // 节点是否勾选，ztree配置启用复选框时有效

    private boolean open = false; // 节点是否展开

    private String icon; // 节点的图标

    private String iconOpen; // 节点展开式的图标
    
    private String eUrl; // 节点关联url

    private String iconClose; // 节点折叠时的图标

    private String id; // 节点的标识属性，对应的是启用简单数据格式时idKey对应的属性名，并不一定是id,如果setting中定义的idKey:"zId",那么此处就是zId

    private String pId; // 节点parentId属性，命名规则同id
    // private String children; //得到该节点所有孩子节点，直接下级，若要得到所有下属层级节点，需要自己写递归得到

    private boolean isParent = false; // 判断该节点是否是父节点，一般应用中通常需要判断只有叶子节点才能进行相关操作，或者删除时判断下面是有子节点时经常用到。

    private boolean isAjaxing1 = false;
    // getPath() //得到该节点的路径，即所有父节点，包括自己，此方法返回的是一个数组，通常用于创建类似面包屑导航的东西A-->B-->C
    public String getName() {
        return name;
    }

    public boolean isChecked() {
        return checked;
    }

    public boolean isOpen() {
        return open;
    }

    public String getIcon() {
        return icon;
    }

    public String getIconOpen() {
        return iconOpen;
    }

    public String getIconClose() {
        return iconClose;
    }

    public String getId() {
        return id;
    }

    public String getpId() {
        return pId;
    }
    
    
    public void setIsAjaxing1(boolean isAjaxing1) {
    	this.isAjaxing1 = isAjaxing1;
	}

	public boolean getIsAjaxing1() {
		return isAjaxing1;
	}

   public void setIsParent(boolean isParent) {
        this.isParent = isParent;
    }

	public boolean getIsParent() {
        return isParent;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public void setIconOpen(String iconOpen) {
        this.iconOpen = iconOpen;
    }

    public void setIconClose(String iconClose) {
        this.iconClose = iconClose;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setpId(String pId) {
        this.pId = pId;
    }

 
	public String geteUrl() {
		return eUrl;
	}

	public void seteUrl(String eUrl) {
		this.eUrl = eUrl;
	}


    
    

}
