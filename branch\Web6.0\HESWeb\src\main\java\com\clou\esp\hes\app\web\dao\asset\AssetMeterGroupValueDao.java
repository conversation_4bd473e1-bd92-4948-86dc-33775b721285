/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupValue{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-16 01:58:30
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.asset;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupValue;

@MyBatisDao
public interface AssetMeterGroupValueDao extends CrudDao<AssetMeterGroupValue>{

	/**
	 * 根据条件查询一条数据
	 * @Description 
	 * @return AssetMeterGroupValue
	 * <AUTHOR> 
	 * @Time 2018年1月31日 下午2:17:27
	 */
	AssetMeterGroupValue selectOneDate(@Param(value="groupId")String groupId, @Param(value="dataitemId")String dataitemId);
}