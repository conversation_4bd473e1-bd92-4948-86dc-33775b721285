/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataShipmentMeter{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-11-18 07:02:58
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.ImportAssetGprsMeter;
import com.clou.esp.hes.app.web.model.data.DataShipmentMeter;

@MyBatisDao
public interface DataShipmentMeterDao extends CrudDao<DataShipmentMeter>{

	public int batchInsert(List<ImportAssetGprsMeter> meters) ;
}