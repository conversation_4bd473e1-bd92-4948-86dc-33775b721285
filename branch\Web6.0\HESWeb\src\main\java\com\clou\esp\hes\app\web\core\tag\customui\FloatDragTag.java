package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.power7000g.core.util.base.StringUtil;
/**
 * 文件名：FloatDragTag.java
 * 版权：FloatDragTag
 * 描述：通用浮球
 * 修改人：严浪
 * 修改时间：2018年3月1日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FloatDragTag extends TagSupport{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	protected String id;// 组件id
	protected String title; // 组件name
	protected boolean limit=true; //是否锁定范围
	protected boolean lock=false; //是否锁定位置
	protected boolean lockX=false; //是否锁定水平位置
	protected boolean lockY=false; //是否锁定垂直位置
	protected String initX; //初始化位置X坐标
	protected String initY; //初始化位置Y坐标
	protected String onclick; //显示内容点击事件
	protected String onStart; //拖拽开始时回调函数
	protected String onMove; //拖拽时回调函数
	protected String onStop; //拖拽停止时回调函数
	
	
	public int doStartTag() throws JspTagException {
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspTagException {
		JspWriter out = null;
		try {
			out = this.pageContext.getOut();
			out.print(end().toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return EVAL_PAGE;
	}
	
	public StringBuffer end() {
		StringBuffer sb = new StringBuffer();
		ServletRequest request = this.pageContext.getRequest();
		String path = ((HttpServletRequest) request).getContextPath();
		String basePath = request.getScheme() + "://"
				+ request.getServerName() + ":" + request.getServerPort()
				+ path;
		sb.append("<script src=\"" + basePath+ "/plug-in/float-drag/float-drag.js\" type=\"text/javascript\" charset=\"utf-8\"></script>");
		sb.append("<div id=\""+id+"\" class=\"lineBox\">");
		sb.append("	<div class=\"bigCircle\"></div>");
		sb.append("	<h2 class=\"title\">");
		sb.append("		<i class=\"fa fa-mail-forward\"></i>");
		sb.append("	</h2>");
		sb.append("	<div class=\"jumpBtn\">");
		if(StringUtil.isNotEmpty(onclick)){
			sb.append("		<a onclick=\""+onclick+"();\">");
		}else{
			sb.append("		<a >");
		}
		sb.append(title);
		sb.append("		</a>");
		sb.append("	</div>");
		sb.append("</div>");
		// 再拼接js
		sb.append("<script type=\"text/javascript\">");
		sb.append("var "+id+"oBox = document.getElementById(\""+id+"\");");
		sb.append("var "+id+"oTitle = "+id+"oBox.getElementsByTagName(\"h2\")[0];");
		sb.append("var "+id+"oSpan = document.getElementsByTagName(\"span\")[0];");
		sb.append("var "+id+"oDrag = new Drag("+id+"oBox, {");
		sb.append("	handle: "+id+"oTitle,");
		sb.append("	limit: "+limit+",");
		sb.append(" lock:"+lock+",");
		sb.append("	lockX:"+lockX+",");
		sb.append("	lockY:"+lockY+",");
		if(StringUtil.isNotEmpty(initX))
			sb.append("	initX:"+initX+",");
		if(StringUtil.isNotEmpty(initY))
			sb.append("	initY:"+initY+",");
		if(StringUtil.isNotEmpty(onStart))
			sb.append("	onStart:"+onStart+",");
		if(StringUtil.isNotEmpty(onMove))
			sb.append("	onMove:"+onMove+",");
		if(StringUtil.isNotEmpty(onStop))
			sb.append("	onStop:"+onStop+",");
		sb.append("});");
		sb.append("</script>");
		return sb;
	}

	
	public String getId() {
		return id;
	}
	public String getTitle() {
		return title;
	}
	public boolean getLimit() {
		return limit;
	}
	public boolean getLock() {
		return lock;
	}
	public boolean getLockX() {
		return lockX;
	}
	public boolean getLockY() {
		return lockY;
	}
	public String getInitX() {
		return initX;
	}
	public String getInitY() {
		return initY;
	}
	public String getOnclick() {
		return onclick;
	}
	public String getOnStart() {
		return onStart;
	}
	public String getOnMove() {
		return onMove;
	}
	public String getOnStop() {
		return onStop;
	}
	public void setId(String id) {
		this.id = id;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public void setLimit(boolean limit) {
		this.limit = limit;
	}
	public void setLock(boolean lock) {
		this.lock = lock;
	}
	public void setLockX(boolean lockX) {
		this.lockX = lockX;
	}
	public void setLockY(boolean lockY) {
		this.lockY = lockY;
	}
	public void setInitX(String initX) {
		this.initX = initX;
	}
	public void setInitY(String initY) {
		this.initY = initY;
	}
	public void setOnclick(String onclick) {
		this.onclick = onclick;
	}
	public void setOnStart(String onStart) {
		this.onStart = onStart;
	}
	public void setOnMove(String onMove) {
		this.onMove = onMove;
	}
	public void setOnStop(String onStop) {
		this.onStop = onStop;
	}
	
	

}
