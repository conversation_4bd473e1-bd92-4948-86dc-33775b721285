package com.clou.esp.hes.app.web.enums.system;

public enum EnumUserState {
	/**
	 * 0=禁用
	 */
	DISABLED(0, "禁用"),
	/**
	 * 1=使用中
	 */
	USED(1, "使用中"),
	/**
	 * 2=锁住
	 */
	LOCKED(2, "锁住"), ;
	private int index;
	private String remark;

	private EnumUserState(int index, String remark) {
		this.remark = remark;
		this.index = index;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(short index) {
		this.index = index;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
