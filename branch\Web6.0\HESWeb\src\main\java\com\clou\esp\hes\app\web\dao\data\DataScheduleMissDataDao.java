/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataScheduleMissData{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-01 06:03:45
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataParameterPlan;
import com.clou.esp.hes.app.web.model.data.DataScheduleMissData;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DataScheduleMissDataDao extends CrudDao<DataScheduleMissData> {
	public int batchSave(List<DataScheduleMissData> dsmList);

	public int deleteByTv(@Param(value = "tv") String tv);
	
	public List<DataScheduleMissData> getListMissDataTv(DataScheduleMissData dataScheduleMissData);

	public List<DataScheduleMissData> getForJqGrid1(JqGridSearchTo jqGridSearchTo);
	
	public List<DataScheduleMissData> getList1(DataScheduleMissData dataScheduleMissData);
	
	public List<DataScheduleMissData> getMissDataList(DataScheduleMissData dataScheduleMissData);

}