package com.clou.esp.hes.app.web.core.tag.vo;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.StringUtils;

import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：ZtreeUtil.java
 * 版权：Copyright by Power7000g Team
 * 描述：
 * 修改人：严浪
 * 修改时间：2017年3月27日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class ZtreeUtil {
    private String nameFname="name"; // 节点显示的文本

    private String checkedFname="checked"; // 节点是否勾选，ztree配置启用复选框时有效

    private String openFname = "open"; // 节点是否展开

   
    
    private String iconFname="icon"; // 节点的图标

    private String iconOpenFname="iconOpen"; // 节点展开式的图标

    private String iconCloseFname="iconClose"; // 节点折叠时的图标

    private String idFname="id"; // 节点的标识属性，对应的是启用简单数据格式时idKey对应的属性名，并不一定是id,如果setting中定义的idKey:"zId",那么此处就是zId

    private String pIdFname="pId"; // 节点parentId属性，命名规则同id
    
    private String subsetFieldName; //得到该节点所有孩子节点，直接下级，若要得到所有下属层级节点，需要自己写递归得到

    private String isParentFname="isParent" ; // 判断该节点是否是父节点，一般应用中通常需要判断只有叶子节点才能进行相关操作，或者删除时判断下面是有子节点时经常用到。

    private String isAjaxingFname = "isAjaxing"; // 节点是否展开
    
    private String eUrl="functionUrl";
    
    
    
	public String getNameFname() {
        return nameFname;
    }

    public String getCheckedFname() {
        return checkedFname;
    }

    public String getOpenFname() {
        return openFname;
    }

    public String getIconFname() {
        return iconFname;
    }

    public String getIconOpenFname() {
        return iconOpenFname;
    }

    public String getIconCloseFname() {
        return iconCloseFname;
    }

    public String getIdFname() {
        return idFname;
    }

    public String getpIdFname() {
        return pIdFname;
    }

    
    
    public String getIsAjaxingFname() {
		return isAjaxingFname;
	}

	public void setIsAjaxingFname(String isAjaxingFname) {
		this.isAjaxingFname = isAjaxingFname;
	}

	public String getIsParentFname() {
        return isParentFname;
    }

    public void setNameFname(String nameFname) {
        this.nameFname = nameFname;
    }

    public void setCheckedFname(String checkedFname) {
        this.checkedFname = checkedFname;
    }

    public void setOpenFname(String openFname) {
        this.openFname = openFname;
    }

    public void setIconFname(String iconFname) {
        this.iconFname = iconFname;
    }

    public void setIconOpenFname(String iconOpenFname) {
        this.iconOpenFname = iconOpenFname;
    }

    public void setIconCloseFname(String iconCloseFname) {
        this.iconCloseFname = iconCloseFname;
    }

    public void setIdFname(String idFname) {
        this.idFname = idFname;
    }

    public void setpIdFname(String pIdFname) {
        this.pIdFname = pIdFname;
    }

    public void setIsParentFname(String isParentFname) {
        this.isParentFname = isParentFname;
    }
    
    

    public String getSubsetFieldName() {
        return subsetFieldName;
    }

    public void setSubsetFieldName(String subsetFieldName) {
        this.subsetFieldName = subsetFieldName;
    }
    
  

	public String geteUrl() {
		return eUrl;
	}

	public void seteUrl(String eUrl) {
		this.eUrl = eUrl;
	}

	/**
     * 返回zTree树形结构
     * @param list
     * @return 
     * @see
     */
    public List<ZtreeNode> getZtreeNodeData(List<?> list){
        List<ZtreeNode> tgList=new ArrayList<>();
        if(list!=null&&list.size()>0)
        for(Object o:list){
            ZtreeNode ztn=new ZtreeNode();
            Object id=TagUtil.fieldNametoValues(idFname, o);
            ztn.setId(id!=null?(String)id:"");
            Object name=TagUtil.fieldNametoValues(nameFname, o);
            ztn.setName(name!=null?(String)name:"");
            Object parent=TagUtil.fieldNametoValues(pIdFname, o);
            ztn.setpId(parent!=null?(String)parent:"");
            Object isParent=TagUtil.fieldNametoValues(isParentFname, o);
            ztn.setIsParent(!StringUtils.isEmpty(isParent)?(Boolean)isParent:false);
            
            Object icon=TagUtil.fieldNametoValues(iconFname, o);
            ztn.setIcon(icon!=null?(String)icon:"");
            Object iconClose=TagUtil.fieldNametoValues(iconCloseFname, o);
            ztn.setIconClose(iconClose!=null?(String)iconClose:"");
            Object iconOpen=TagUtil.fieldNametoValues(iconOpenFname, o);
            ztn.setIconOpen(iconOpen!=null?(String)iconOpen:"");

            Object isAjax=TagUtil.fieldNametoValues(isAjaxingFname, o);
            ztn.setIsAjaxing1(!StringUtils.isEmpty(isAjax)?(Boolean)isAjax:false);

            Object urlName=TagUtil.fieldNametoValues(eUrl, o);
            ztn.seteUrl(urlName!=null?(String)urlName:"");
            
            if(StringUtil.isNotEmpty(this.openFname)){
                Object expanded=TagUtil.fieldNametoValues(openFname, o);
                if(expanded!=null&&expanded instanceof Boolean){
                    ztn.setOpen((boolean)expanded);
                }
            }
            if(StringUtil.isNotEmpty(this.checkedFname)){
                Object expanded=TagUtil.fieldNametoValues(checkedFname, o);
                if(expanded!=null&&expanded instanceof Boolean){
                    ztn.setChecked((boolean)expanded);
                }
            }
            
            Object subObj=TagUtil.fieldNametoValues(StringUtil.isNotEmpty(this.subsetFieldName)?this.subsetFieldName:"list", o);
            if(subObj!=null&&subObj instanceof List){
                if(((List)subObj).size()>=0){
                    ztn.setIsParent(true);
                }
               tgList.add(ztn);
               tgList.addAll(getZtreeNodeData((List)subObj));
            }else{
            	if(ztn.getIsParent()){
            		ztn.setIsParent(true);
            	}else{
            		   ztn.setIsParent(false);
            	}
             
                tgList.add(ztn);
            }
        }
        return tgList;
    }
}
