package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：FileUploadTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：通用文件上传
 * 修改人：严浪
 * 修改时间：2017年8月31日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FileUploadTag extends TagSupport {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	protected String id; //组件ID
	protected String i18nName;//按钮显示内容
	protected boolean showFile=false; //是否展示上传文件列表
	protected String callbackName; //上传成功回调方法名称
	protected long maxSize=0; //文件最大多少kb
	protected boolean multiFile=false; //是否多文件上传
	protected String accept; //文件类型 

	public int doStartTag() throws JspTagException {
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspTagException {
		JspWriter out = null;
		try {
			out = this.pageContext.getOut();
			out.print(end().toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return EVAL_PAGE;
	}

	public StringBuffer end() {
		StringBuffer sb = new StringBuffer();
		try {
			ServletRequest request = this.pageContext.getRequest();
			String path = ((HttpServletRequest) request).getContextPath();
			String basePath = request.getScheme() + "://"
					+ request.getServerName() + ":" + request.getServerPort()
					+ path;
			
			sb.append("<div class=\"row fileupload-buttonbar\">");
			sb.append("<div class=\"col-lg-7\">");
			sb.append("      <span class=\"btn btn-success fileinput-button\" >");
			sb.append("           <i class=\"glyphicon glyphicon-plus\"></i>");
			sb.append("           <span>"+i18nName+"</span>");
			sb.append("            <input id=\""+id+"\" type=\"file\" name=\"file[]\" "+(multiFile?"multiple":"")+" accept=\""+accept+"\">");
			sb.append("        </span>");
			sb.append("   </div>");
			sb.append(" <table class=\"table table-striped files filesName\">");
			sb.append(" </table>");
			sb.append(" </div>");
			sb.append("<script type=\"text/javascript\">");
			sb.append("$('#"+id+"').fileupload({");
			sb.append("    url:'"+basePath+"/sysFileController/fileUpload.do',");
			sb.append("    dataType:'json',");
			sb.append("     autoUpload:true,");
			sb.append("    sequentialUploads: true,");
			sb.append("    add: function(e, data) {");
			sb.append("        var filename = data.files[0].name;");
			sb.append("        var fileListLenght = $('.filesName').find('tr').length;");
			if(multiFile){
			sb.append("        for (var i = 0; i < fileListLenght; i++) {");
			sb.append("            if (filename == $('.filesName').find('.name').eq(i).text()) {");
			sb.append("                window.parent.layer.msg('"+"MutiLangUtil.doMutiLang(\"sysFile.notRepeatUploads\")"+"', {");
			sb.append("					icon: 2");
			sb.append("				});");
			sb.append("               return false;");
			sb.append("           }");
			sb.append("        }");
			}
			if(maxSize>0){
			sb.append("        if(data.originalFiles[0].size && data.originalFiles[0].size > "+maxSize+") {");
			sb.append("        	 window.parent.layer.msg('"+"MutiLangUtil.doMutiLang(\"sysFile.sizeNotBe\")"+"'+bytesToSize("+maxSize+")+'！', {");
			sb.append("					icon: 2");
			sb.append("				});");
			sb.append("             return false;");
			sb.append("        }");
			}
			sb.append("        var filesList = \"filesList\" + fileListLenght;");
			if(showFile){
			sb.append("        var filesListHTML='<tbody ><tr class=\"' + filesList + '\"><td style=\"width: 50%;\" ><p class=\"name\"  data-on=\"!!file.url\">'");
			sb.append("           +'<span  class=\"ng-binding ng-scope\">' + filename + '</span>'");
			sb.append("            +'</p></td><td style=\"width: 50%;\"><p class=\"size ng-binding\">'+bytesToSize(data.originalFiles[0].size)+'</p>'");
			sb.append("            +' <div class=\"progress progress-striped active  ng-scope\" ><div class=\"progress-bar progress-bar-success\" style=\"width: 0%;\"></div></div></td></tr></tbody>';");
			}else{
			sb.append("        var filesListHTML='<tr class=\"' + filesList + '\"><td ><p class=\"size ng-binding \">'+bytesToSize(data.originalFiles[0].size)+'</p>'");
			sb.append("        	 				+'<div class=\"progress progress-striped active  ng-scope \" ><div class=\"progress-bar progress-bar-success\" style=\"width:00%;\"></div></div></td></tr>';");
			}
			sb.append("       	 $('.filesName').append(filesListHTML);");
			sb.append("      	 data.context = $(\".\" + filesList);");
			sb.append("       data.submit();");
			sb.append("     },");
		        //单个进度条
			sb.append("     progress: function(e, data) {");
			sb.append("       var progress = parseInt(data.loaded / data.total * 100, 10);");
			sb.append("        data.context.find('.progress-bar-success').css('width', progress + '%');");
			sb.append("    },");
		        //上传失败
			sb.append("    fail: function(e, data) {");
			if(showFile){
			sb.append("     	$('.filesName').find('.progress-striped').parent().parent().remove();");
			}
			sb.append("        	 window.parent.layer.msg('"+"MutiLangUtil.doMutiLang(\"sysFile.uploadFailed\")"+"'+bytesToSize("+maxSize+")+'！', {");
			sb.append("					icon: 2");
			sb.append("				});");
			sb.append("    },");
		        //上传完成
			sb.append("    done : function(e, data) {");
			sb.append("         var result=data.result;");
			sb.append("  	 if(result.success){");
			if(showFile){
			sb.append("             var filesList = \"filesList\" + $('.filesName').find('tr').length;");
			sb.append("			 $('.filesName').find('.progress-striped').parent().parent().remove();");
			sb.append("             var filesListHTML='<tbody ><tr class=\"' + filesList + '\"><td style=\"width: 40%;\" ><p class=\"name\"  data-on=\"!!file.url\">'");
			sb.append("             +'<span  class=\"ng-binding ng-scope\">' + result.file + '</span>'");
			sb.append("            +'</p></td><td style=\"width: 20%;\"><p class=\"size ng-binding\">'+bytesToSize(result.fileSize)+'</p></td><td style=\"width: 40%;\">'");
			sb.append("           +'<button type=\"button\" class=\"btn btn-warning cancel \" >'");
			sb.append("            +'<i class=\"glyphicon glyphicon-upload\"></i><span>Download</span></button>'");
			sb.append("             +'<button data-ng-controller=\"FileDestroyController\" type=\"button\" class=\"btn btn-danger destroy delete\" type=\"button\" >'");
			sb.append("             +'<i class=\"glyphicon glyphicon-trash\"></i><span>Delete</span></button></td></tr></tbody>';");
			sb.append("         	 $('.filesName').append(filesListHTML);");
			sb.append("             $(\".\"+filesList).find('.delete').click(function(){");
			sb.append("             	$.ajax({");
			sb.append("                     async:false,");
			sb.append("                    type:'post',");
			sb.append("                     url:'"+basePath+"/sysFileController/del.do',");
			sb.append("                     dataType:'html',");
			sb.append("                     data:'id=' + result.id,");
			sb.append("                     success:function(msg){");
			sb.append("                         $(\".\"+filesList).remove();");
			sb.append("                    }");
			sb.append("                });");
			sb.append("            });");
			sb.append("            $(\".\"+filesList).find('.cancel').click(function(){");
			sb.append("            	window.location=result.physicalPath;");
			sb.append("            });");
			}else{
				sb.append(" ");
				sb.append("        data.context.find('.progress-bar-success').parent().parent().remove();");
			}
			if(StringUtil.isNotEmpty(callbackName)){
				sb.append(callbackName+"(result);");
			}
			sb.append("         }else{");
			sb.append("        	 window.parent.layer.msg('"+"MutiLangUtil.doMutiLang(\"sysFile.uploadFailed\")"+"'+bytesToSize("+maxSize+")+'！', {");
			sb.append("					icon: 2");
			sb.append("				});");
			sb.append("    }}");
			sb.append("});");
			sb.append("function bytesToSize(bytes) {");
			sb.append("    if (bytes === 0) return '0 B';");
			sb.append("    var k = 1000, ");
			sb.append("        sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],");
			sb.append("        i = Math.floor(Math.log(bytes) / Math.log(k));");
			sb.append("   return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];");
			sb.append("}");
			sb.append("</script>");
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return sb;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getI18nName() {
		return i18nName;
	}

	public void setI18nName(String i18nName) {
		this.i18nName = i18nName;
	}

	public boolean getShowFile() {
		return showFile;
	}

	public void setShowFile(boolean showFile) {
		this.showFile = showFile;
	}

	public String getCallbackName() {
		return callbackName;
	}

	public void setCallbackName(String callbackName) {
		this.callbackName = callbackName;
	}

	public long getMaxSize() {
		return maxSize;
	}

	public void setMaxSize(long maxSize) {
		this.maxSize = maxSize*1000;
	}

	public boolean getMultiFile() {
		return multiFile;
	}

	public void setMultiFile(boolean multiFile) {
		this.multiFile = multiFile;
	}

	public String getAccept() {
		return accept;
	}

	public void setAccept(String accept) {
		this.accept = accept;
	}

}
