<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.LogSysOperationMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.LogSysOperation">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="Utility_ID" jdbcType="VARCHAR" property="utilityId" />
    <result column="Browser" jdbcType="VARCHAR" property="browser" />
    <result column="Log_Level" jdbcType="SMALLINT" property="logLevel" />
    <result column="OBJ_Name" jdbcType="VARCHAR" property="objName" />
    <result column="OP_IP" jdbcType="VARCHAR" property="opIp" />
    <result column="OP_Time" jdbcType="TIMESTAMP" property="opTime" />
    <result column="OP_Type" jdbcType="SMALLINT" property="opType" />
    <result column="User_Name" jdbcType="VARCHAR" property="userName" />
    <result column="Save_DB_Date" jdbcType="TIMESTAMP" property="saveDbDate" />
    <result column="Readme" jdbcType="VARCHAR" property="readme" />
    <result column="Log_Content" jdbcType="LONGVARCHAR" property="logContent" />
    <result column="Log_Content_i18n" jdbcType="LONGVARCHAR" property="logContentI18n" />
  </resultMap>
</mapper>