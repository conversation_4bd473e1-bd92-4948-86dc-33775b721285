/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataStatisticsDevice{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：赵久霆
 * 创建于：2018-09-19 07:28:24
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataStatisticsDevice;

@MyBatisDao
public interface DataStatisticsDeviceDao extends CrudDao<DataStatisticsDevice>{

	public List<Map<String, Object>> getDataStatisticsDeviceByMaps(Map<String, Object> params);

}