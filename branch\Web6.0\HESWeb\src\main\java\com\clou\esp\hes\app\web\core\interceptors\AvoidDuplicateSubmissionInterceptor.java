package com.clou.esp.hes.app.web.core.interceptors;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * 权限拦截器
 * 
 * <AUTHOR>
 * 
 */
public class AvoidDuplicateSubmissionInterceptor implements HandlerInterceptor {

	private static final Logger logger = Logger
			.getLogger(AvoidDuplicateSubmissionInterceptor.class);

	/**
	 * 在controller后拦截
	 */
	public void afterCompletion(HttpServletRequest request,
			HttpServletResponse response, Object object, Exception exception)
			throws Exception {
	}

	public void postHandle(HttpServletRequest request,
			HttpServletResponse response, Object object,
			ModelAndView modelAndView) throws Exception {

	}

	/**
	 * 在controller前拦截
	 */
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object handler) throws Exception {
		HttpSession session = request.getSession(true);
		List<String> menuUrls=(List<String>) session.getAttribute("menuUrls");
		List<String> allMenuUrls=(List<String>) session.getAttribute("allMenuUrls");
//		Map<String, String> hideTab=(Map<String, String>) session.getAttribute("hideTab");
		
		String u = request.getRequestURI();
		String url=u.substring(u.indexOf("/", 1)+1, u.length());
	//	boolean isYes=true;
		if(allMenuUrls!=null&&allMenuUrls.contains(url)){
			if(menuUrls==null||!menuUrls.contains(url)){
				response.sendRedirect(request.getContextPath()+"/systemController/noPermissions.do");
				//isYes=false;
			}
		}
		
//		if(isYes){
//			if(hideTab!=null&&hideTab.containsKey(url)){
//				String val=hideTab.get(url);
//				request.setAttribute("hideMenuTabs", val);
//			}
//		}
		
		

		/*SysUser user = TokenManager.getToken();
		if (user != null) {
			HandlerMethod handlerMethod = (HandlerMethod) handler;
			Method method = handlerMethod.getMethod();
			AvoidDuplicateSubmission annotation = method
					.getAnnotation(AvoidDuplicateSubmission.class);
			if (annotation != null) {
				boolean needSaveSession = annotation.needSaveToken();
				if (needSaveSession) {
					request.getSession(false)
							.setAttribute(
									Globals.SESSION_AVOID_DUPLICATE_TOKEN,
									TokenProcessor.getInstance().generateToken(
											request));
				}
				boolean needRemoveSession = annotation.needRemoveToken();
				if (needRemoveSession) {
					if (isRepeatSubmit(request)) {
						return false;
					}
					request.getSession(false).removeAttribute(
							Globals.SESSION_AVOID_DUPLICATE_TOKEN);
				}
			}
		}*/
		return true;
	}

	private boolean isRepeatSubmit(HttpServletRequest request) {
		/*String serverToken = (String) request.getSession(false).getAttribute(
				Globals.SESSION_AVOID_DUPLICATE_TOKEN);
		if (serverToken == null) {
			return true;
		}
		String clinetToken = request
				.getParameter(Globals.SESSION_AVOID_DUPLICATE_TOKEN);
		if (clinetToken == null) {
			return true;
		}
		if (!serverToken.equals(clinetToken)) {
			return true;
		}*/
		return false;
	}
}
