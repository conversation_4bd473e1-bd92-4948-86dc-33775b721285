package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetCalcObj;
import com.clou.esp.hes.app.web.model.asset.AssetTransformer;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: AssetTransformerDao
 * @Description: TODO
 * <AUTHOR>
 * @date 2018年9月20日 上午9:09:36
 *
 */
@MyBatisDao
public interface AssetTransformerDao extends CrudDao<AssetTransformer> {
	List<AssetCalcObj> getListByCalObj(JqGridSearchTo jqGridSearchTo);
}
