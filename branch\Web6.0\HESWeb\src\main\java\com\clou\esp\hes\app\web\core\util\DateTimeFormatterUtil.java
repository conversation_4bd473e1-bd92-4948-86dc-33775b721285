package com.clou.esp.hes.app.web.core.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.power7000g.core.util.base.DateUtils;

public class DateTimeFormatterUtil {
      public static SimpleDateFormat getSimpleDateFormat(String timeFlag){
          if("1".equals(timeFlag)){
        	return DateUtils.MMddyyyy;
          }else if("2".equals(timeFlag)){
        	return new SimpleDateFormat("yyyy/MM/dd");
          }else if("3".equals(timeFlag)){
        	return new SimpleDateFormat("dd/MM/yyyy");
          }else if("4".equals(timeFlag)){
        	return new SimpleDateFormat("dd-MM-yyyy");
          }else if("5".equals(timeFlag)){
        	return new SimpleDateFormat("yyyy-MM-dd");
          }
    	  return DateUtils.MMddyyyy;
      }
      
      public static SimpleDateFormat getSimpleDateFormat_HHmmss(String timeFlag){
          if("1".equals(timeFlag)){
        	  return new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");
          }else if("2".equals(timeFlag)){
        	return new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
          }else if("3".equals(timeFlag)){
        	return new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
          }else if("4".equals(timeFlag)){
        	return new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
          }else if("5".equals(timeFlag)){
        	return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
          }
          return new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");
      }
      
      public static SimpleDateFormat getSimpleDateFormat_DDMM(String timeFlag){
          if("1".equals(timeFlag)){
        	  return new SimpleDateFormat("MM-dd");
          }else if("2".equals(timeFlag)){
        	return new SimpleDateFormat("MM/dd");
          }else if("3".equals(timeFlag)){
        	return new SimpleDateFormat("dd/MM");
          }else if("4".equals(timeFlag)){
        	return new SimpleDateFormat("dd-MM");
          }else if("5".equals(timeFlag)){
        	return new SimpleDateFormat("MM-dd");
          }
        	return new SimpleDateFormat("MM-dd");
      }
      
      public static String getSimpleDateFormat_HHmmssStr(String value , String timeFlag){
//    		FFFF-03-01 12:01:07,FF
    	 //    0   1  2  3  4  5  6
    		//FFFF-01-01,FF
	    	String changeTimeValue ="";
	    	String[] spStr;
	  		String splitSymbolCross = "[- :,]";
  	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
	
	      if("1".equals(timeFlag)){
			spStr=value.split(splitSymbolCross);
			if(spStr.length<7){
				value+=",FF";
				spStr=value.split(splitSymbolCross);
			}
			changeTimeValue = spStr[1] +"/"+ spStr[2] +"/"+ spStr[0] +" " + spStr[3] +":" + spStr[4] +":" + spStr[5] +"," + spStr[6];
	      }else if("2".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<7){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[0] +"/"+ spStr[1] +"/"+ spStr[2] +" " + spStr[3] +":" + spStr[4] +":" + spStr[5] +"," + spStr[6];
	      }else if("3".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<7){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[2] +"/"+ spStr[1] +"/"+ spStr[0] +" " + spStr[3] +":" + spStr[4] +":" + spStr[5] +"," + spStr[6];
	      }else if("4".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<7){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[2] +"-"+ spStr[1] +"-"+ spStr[0] +" " + spStr[3] +":" + spStr[4] +":" + spStr[5] +"," + spStr[6];
	      }else if("5".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<7){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[0] +"-"+ spStr[1] +"-"+ spStr[2] +" " + spStr[3] +":" + spStr[4] +":" + spStr[5] +"," + spStr[6];
	      }
    	  return changeTimeValue;
      }
      
      public static String getSimpleDateFormatStr(String value , String timeFlag){
//  		FFFF-03-01 12:01:07,FF
  	 //    0   1  2  3  4  5  6
  		//FFFF-01-01,FF
	    	String changeTimeValue ="";
	    	String[] spStr;
	  		String splitSymbolCross = "[- :,]";
	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
	
	      if("1".equals(timeFlag)){
			spStr=value.split(splitSymbolCross);
			if(spStr.length<4){
				value+=",FF";
				spStr=value.split(splitSymbolCross);
			}
			changeTimeValue = spStr[1] +"/"+ spStr[2] +"/"+ spStr[0] +"," + spStr[3];
	      }else if("2".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<4){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[0] +"/"+ spStr[1] +"/"+ spStr[2] +"," + spStr[3];
	      }else if("3".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<4){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[2] +"/"+ spStr[1] +"/"+ spStr[0] +"," + spStr[3];
	      }else if("4".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<4){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[2] +"-"+ spStr[1] +"-"+ spStr[0] +"," + spStr[3];
	      }else if("5".equals(timeFlag)){
	    		spStr=value.split(splitSymbolCross);
	  			if(spStr.length<4){
	  				value+=",FF";
	  				spStr=value.split(splitSymbolCross);
	  			}
	  			changeTimeValue = spStr[0] +"-"+ spStr[1] +"-"+ spStr[2] +"," + spStr[3];
	      }
  	  return changeTimeValue;
      }
      
      
      
      public static String getSimpleDateFormatToYYYYMMDDHHMMSS(String value , String timeFlag){
//    		FFFF-03-01 12:01:07,FF
    	 //    0   1  2  3  4  5  6
    		//FFFF-01-01,FF
	    	String changeTimeValue ="";
	
  	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
	
	      if("1".equals(timeFlag)){
	        String[] valueArray = value.split(" ");
			changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +" " + valueArray[1];
	      }else if("2".equals(timeFlag)){
	    	   String[] valueArray = value.split(" ");
	    	   changeTimeValue = valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[2] +" " + valueArray[1];
	      }else if("3".equals(timeFlag)){
	    	   String[] valueArray = value.split(" ");
	    	   changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[0] +" " + valueArray[1];
	      }else if("4".equals(timeFlag)){
	    	   String[] valueArray = value.split(" ");
	    	   changeTimeValue = valueArray[0].split("-")[2] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[0] +" " + valueArray[1];
	      }else if("5".equals(timeFlag)){
	    	   String[] valueArray = value.split(" ");
	    	   changeTimeValue = valueArray[0].split("-")[0] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[2] +" " + valueArray[1];
	      }
    	  return changeTimeValue;
      }						
      
      public static String getSimpleDateFormatToYYYYMMDD(String value , String timeFlag){
//  		FFFF-03-01 12:01:07,FF
  	 //    0   1  2  3  4  5  6
  		//FFFF-01-01,FF
	    	String changeTimeValue ="";
	
	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
	
	      if("1".equals(timeFlag)){
	        String[] valueArray = value.split(",");
	        if(valueArray.length == 1){
	        	changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +" " + "FF";
	        }else{
	        	changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +" " + valueArray[1];
	        }
			
	      }else if("2".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[2] +" " + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[2] +" " + valueArray[1]; 
	    	   }
	    	   
	      }else if("3".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[0] +" " + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[0] +" " + valueArray[1];  
	    	   }
	    	   
	      }else if("4".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("-")[2] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[0] +" " + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("-")[2] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[0] +" " + valueArray[1]; 
	    	   }
	    	   
	      }else if("5".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("-")[0] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[2] +" " + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("-")[0] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[2] +" " + valueArray[1];  
	    	   }
	    	   
	      }
	      return changeTimeValue;
      }
      
      /**
       * 
       * @Title: getSimpleDateFormatToYYYYMMDD - （2018-09-22 baijun)
       * @Description: 处理时间方法重载
       * @param value
       * @param timeFlag
       * @param seperator
       * @return
       * @return String
       * @throws
       */
      public static String getSimpleDateFormatToYYYYMMDD(String value , String timeFlag,String seperator){
//		FFFF-03-01 12:01:07,FF
	 //    0   1  2  3  4  5  6
		//FFFF-01-01,FF
	    	String changeTimeValue ="";
	
	//1 = MM/dd/yyyy, 2 = yyyy/MM/dd, 3=dd/MM/YYYY, 4=dd-MM-yyyy,5 =yyyy-mm-dd
	
	      if("1".equals(timeFlag)){
	        String[] valueArray = value.split(",");
	        if(valueArray.length == 1){
	        	changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +seperator + "FF";
	        }else{
	        	changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +seperator + valueArray[1];
	        }
			
	      }else if("2".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[2] +seperator + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("/")[0] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[2] +seperator + valueArray[1]; 
	    	   }
	    	   
	      }else if("3".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[0] +seperator + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("/")[2] +"-"+ valueArray[0].split("/")[1] +"-"+ valueArray[0].split("/")[0] +seperator + valueArray[1];  
	    	   }
	    	   
	      }else if("4".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("-")[2] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[0] +seperator + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("-")[2] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[0] +seperator + valueArray[1]; 
	    	   }
	    	   
	      }else if("5".equals(timeFlag)){
	    	   String[] valueArray = value.split(",");
	    	   if(valueArray.length == 1){
	    		   changeTimeValue = valueArray[0].split("-")[0] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[2] +seperator + "FF";
	    	   }else{
	    		   changeTimeValue = valueArray[0].split("-")[0] +"-"+ valueArray[0].split("-")[1] +"-"+ valueArray[0].split("-")[2] +seperator + valueArray[1];  
	    	   }
	    	   
	      }
	      return changeTimeValue;
    }
      
      public static int compare_date(String DATE1, String DATE2) {
    	  DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    	  try {
	    	  Date dt1 = df.parse(DATE1);
	    	  Date dt2 = df.parse(DATE2);
    	  if (dt1.getTime() > dt2.getTime()) {
	    	//  System.out.println("dt1 在dt2前");
	    	  return 1;
    	  } else if (dt1.getTime() < dt2.getTime()) {
    		//  System.out.println("dt1在dt2后");
    		  return -1;
    	  } else {
    		  return 0;
    	  }
    	  } catch (Exception exception) {
    		  exception.printStackTrace();
    	  }
    	  	return 0;
    	  }
      
      
      public static int differentDaysByMillisecond(Date date1,Date date2)
      {
          int days = (int) ((date2.getTime() - date1.getTime()) / (1000*3600*24));
          return days;
      }
      
      
      public static List<Date> regularTime(Date starTime,Date endTime,AssetMeasurementProfile measurementGroup,List<Date> timeList) {  
  		List<Date> timeListNew = new ArrayList();
  		
  		int freq = Integer.parseInt(measurementGroup.getProfileCycle());
  	    String freqType = measurementGroup.getProfileCycleType();
  	    freqType = freqType.toLowerCase();
  	   //分钟2160条  月36条 日90条
  	    Calendar cal = Calendar.getInstance();
  	    cal.setTime(starTime);
  	    
  	    switch (freqType) {
  	    case "minutely": {
  	    	int minute = cal.get(Calendar.MINUTE);
  	    	if (minute != 0) {
  	    		minute = (minute / freq) * freq;
  	    		cal.set(Calendar.MINUTE, minute);
  	    		cal.set(Calendar.SECOND, 0);
  	    		cal.set(Calendar.MILLISECOND, 0);
  	    	}
  	    	
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
  			while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addMinutes(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  			
  	    	break;
  	    }
  	    case "hourly": {
      		cal.set(Calendar.MINUTE, 0);
      		cal.set(Calendar.SECOND, 0);
      		cal.set(Calendar.MILLISECOND, 0);
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
  			while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addHours(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  	    	break;
  	    }
  	    case "daily": {
  	    	cal.set(Calendar.HOUR_OF_DAY, 0);
      		cal.set(Calendar.MINUTE, 0);
      		cal.set(Calendar.SECOND, 0);
      		cal.set(Calendar.MILLISECOND, 0);
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
      		while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addDays(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  	    	break;
  	    }
  	    case "monthly": {
  	    	cal.set(Calendar.DAY_OF_MONTH, 1);
  	    	cal.set(Calendar.HOUR_OF_DAY, 0);
      		cal.set(Calendar.MINUTE, 0);
      		cal.set(Calendar.SECOND, 0);
      		cal.set(Calendar.MILLISECOND, 0);
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
      		while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addMonths(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  	    	break;
  	    }
      	default:
      		break;
  	    }
  	    //分钟2160条 小时1080 月36条 日90条
  	    switch (freqType) {
  	    case "minutely": {
  	    	
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 2160){
  	    			int count = timeList.size() - 2160;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    case "hourly": {
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 1080){
  	    			int count = timeList.size() - 1080;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    case "daily": {
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 90){
  	    			int count = timeList.size() - 90;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    case "monthly": {
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 36){
  	    			int count = timeList.size() - 36;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    default:
      		break;
  	    }
  	  //  timeListNew
  		return timeListNew;   
  	}
    	 
      /**
       *两段日期间的日期 
       */
      public static List<String> getDays(String startTime, String endTime,String pattern) {
          List<String> days = new ArrayList<String>();
          DateFormat dateFormat = new SimpleDateFormat(pattern);
          try {
              Date start = dateFormat.parse(startTime);
              Date end = dateFormat.parse(endTime);

              Calendar tempStart = Calendar.getInstance();
              tempStart.setTime(start);

              Calendar tempEnd = Calendar.getInstance();
              tempEnd.setTime(end);
              tempEnd.add(Calendar.DATE, +1);// 日期加1(包含结束)
              while (tempStart.before(tempEnd)) {
                  days.add(dateFormat.format(tempStart.getTime()));
                  tempStart.add(Calendar.DAY_OF_YEAR, 1);
              }
          } catch (ParseException e) {
              e.printStackTrace();
          }
          return days;
      }
      
      /**
       *两段日期间的月份 
       */
      public static List<String> getMonthlys(String startTime, String endTime,String pattern) {
    	  List<String> days = new ArrayList<String>();
    	  DateFormat dateFormat = new SimpleDateFormat(pattern);
    	  try {
    		  Date start = dateFormat.parse(startTime);
    		  Date end = dateFormat.parse(endTime);
    		  
    		  Calendar tempStart = Calendar.getInstance();
    		  tempStart.setTime(start);
    		  
    		  Calendar tempEnd = Calendar.getInstance();
    		  tempEnd.setTime(end);
    		  tempEnd.add(Calendar.MONTH, +1);// 日期加1(包含结束)
    		  while (tempStart.before(tempEnd)) {
    			  days.add(dateFormat.format(tempStart.getTime()));
    			  tempStart.add(Calendar.MONTH, 1);
    		  }
    	  } catch (ParseException e) {
    		  e.printStackTrace();
    	  }
    	  return days;
      }
      
      
      
}

