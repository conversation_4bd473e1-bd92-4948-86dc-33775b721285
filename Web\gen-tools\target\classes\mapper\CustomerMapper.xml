<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.CustomerMapper">
  
  
   <resultMap id="BaseResultMap" type="com.power7000.restful.model.CustomerModel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="SUR_NAME" jdbcType="VARCHAR" property="surname" />
    <result column="OTHER_NAME" jdbcType="VARCHAR" property="other_names" />
    <result column="ID_NO" jdbcType="VARCHAR" property="idNo" />
    <result column="ID_TYPE" jdbcType="VARCHAR" property="id_type" />
    <result column="ADDRESS_1" jdbcType="VARCHAR" property="address_1" />
    <result column="ADDRESS_2" jdbcType="VARCHAR" property="address_2" />
    <result column="ADDRESS_3" jdbcType="VARCHAR" property="address_3" />
    <result column="TELEPHONE_1" jdbcType="VARCHAR" property="telephone_1" />
    <result column="TELEPHONE_2" jdbcType="VARCHAR" property="telephone_2" />
    <result column="TELEPHONE_3" jdbcType="VARCHAR" property="telephone_3" />
    <result column="FAX" jdbcType="VARCHAR" property="fax" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="TAX_FRE_NO" jdbcType="VARCHAR" property="tax_ref_no" />
    <result column="EXIST" jdbcType="VARCHAR" property="exist" />
    <result column="IS_STORAGE" jdbcType="VARCHAR" property="isStorage" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    
  </resultMap>
  
  
  	<sql id="Base_Column_List">
		ORG_ID,CUSTOMER_ID,SUR_NAME,OTHER_NAME,ID_NO,ID_TYPE,ADDRESS_1,ADDRESS_2,ADDRESS_3,
		TELEPHONE_1,TELEPHONE_2,TELEPHONE_3,FAX,EMAIL,TAX_FRE_NO,EXIST,IS_STORAGE,ORG_ID,CREATE_DATE,UPDATE_DATE
	</sql>
  
  	<!-- 根据 isStorage 状态查询预存库用户信息 Chilam 2017-06-14 -->
	<select id="selectCustomerByIsStorage" resultMap="BaseResultMap" parameterType="java.util.Map">
		select 
		<include refid="Base_Column_List" />
		from ppm_public_cms_customer
		where IS_STORAGE = #{isStorage,jdbcType=VARCHAR}
	</select>
	
	<!-- 根据用户编号查询居民用户对象 -->
	<select id="selectCustomerByUserNo" resultMap="BaseResultMap" parameterType="java.util.Map">
		select 
		<include refid="Base_Column_List" />
		
		from ppm_public_cms_customer pur
		<where>
			<if test=" customerId !=null and customerId != ''">
				pur.CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	<update id="updateCustomer" parameterType="com.power7000.restful.model.CustomerModel">
		update ppm_public_cms_customer
		<set>
			<if test="surname != null and surname != ''">
				SUR_NAME = #{surname,jdbcType=VARCHAR},
			</if>
			<if test="other_names != null and other_names != ''">
				OTHER_NAME = #{other_names,jdbcType=VARCHAR},
			</if>
			<if test="idNo != null and idNo != ''">
				ID_NO = #{idNo,jdbcType=VARCHAR},
			</if>
			<if test="id_type != null and id_type != ''">
				ID_TYPE = #{id_type,jdbcType=VARCHAR},
			</if>
			<if test="address_1 != null and address_1 != ''">
				ADDRESS_1 = #{address_1,jdbcType=DECIMAL},
			</if>
			<if test="address_2 != null and address_2 != ''">
				ADDRESS_2 = #{address_2,jdbcType=DECIMAL},
			</if>
			<if test="address_3 != null and address_3 != ''">
				ADDRESS_3 = #{address_3,jdbcType=VARCHAR},
			</if>
			<if test="telephone_1 != null and telephone_1 != ''">
				TELEPHONE_1 = #{telephone_1,jdbcType=VARCHAR},
			</if>
			<if test="telephone_2 != null and telephone_2 != ''">
				TELEPHONE_2 = #{telephone_2,jdbcType=VARCHAR},
			</if>
			<if test="telephone_3 != null and telephone_3 != ''">
				TELEPHONE_3 = #{telephone_3,jdbcType=VARCHAR},
			</if>
			<if test="fax != null and fax != ''">
				FAX = #{fax,jdbcType=VARCHAR},
			</if>
			<if test="email != null and email != ''">
				EMAIL = #{email,jdbcType=DECIMAL},
			</if>
			<if test="tax_ref_no != null and tax_ref_no != ''">
				TAX_FRE_NO = #{tax_ref_no,jdbcType=DECIMAL},
			</if>
			<if test="exist != null and exist != ''">
				EXIST = #{exist,jdbcType=DECIMAL},
			</if>
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
			<if test="orgId != null and orgId != ''">
				ORG_ID = #{orgId,jdbcType=VARCHAR},
			</if>
				UPDATE_DATE = now()
		</set>
		where
		CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
	</update>
	
	<insert id="insertCustomer" parameterType="com.power7000.restful.model.CustomerModel">
		insert into ppm_public_cms_customer (
			CUSTOMER_ID,
			SUR_NAME,
			OTHER_NAME,
			ID_NO,
			ID_TYPE,
			ADDRESS_1,
			ADDRESS_2,
			ADDRESS_3,
			TELEPHONE_1,
			TELEPHONE_2,
			TELEPHONE_3,
			FAX,
			EMAIL,
			TAX_FRE_NO,
			EXIST,
			IS_STORAGE,
			ORG_ID,
			CREATE_DATE,
			UPDATE_DATE
		)
		values (
			#{customerId,jdbcType=VARCHAR},
			#{surname,jdbcType=VARCHAR},
			#{other_names,jdbcType=VARCHAR},
			#{idNo,jdbcType=VARCHAR}, 
			#{id_type,jdbcType=VARCHAR},
			#{address_1,jdbcType=VARCHAR},
			#{address_2,jdbcType=DECIMAL},
			#{address_3,jdbcType=DECIMAL},
			#{telephone_1,jdbcType=VARCHAR},
			#{telephone_2,jdbcType=VARCHAR}, 
			#{telephone_3,jdbcType=VARCHAR},
			#{fax,jdbcType=VARCHAR},
			#{email,jdbcType=VARCHAR},
			#{tax_ref_no,jdbcType=VARCHAR},
			#{exist,jdbcType=BOOLEAN},
			#{isStorage,jdbcType=VARCHAR},
			#{orgId,jdbcType=VARCHAR},
			now(),
			now()
		)
	</insert>
	
	
	<update id="updateCustomerDO" parameterType="com.power7000.restful.model.CustomerModel">
		update ppm_public_cms_customer
		<set>
			<if test="surname != null and surname != ''">
				SUR_NAME = #{surname,jdbcType=VARCHAR},
			</if>
			<if test="other_names != null and other_names != ''">
				OTHER_NAME = #{other_names,jdbcType=VARCHAR},
			</if>
			<if test="idNo != null and idNo != ''">
				ID_NO = #{idNo,jdbcType=VARCHAR},
			</if>
			<if test="id_type != null and id_type != ''">
				ID_TYPE = #{id_type,jdbcType=VARCHAR},
			</if>
			<if test="address_1 != null and address_1 != ''">
				ADDRESS_1 = #{address_1,jdbcType=DECIMAL},
			</if>
			<if test="address_2 != null and address_2 != ''">
				ADDRESS_2 = #{address_2,jdbcType=DECIMAL},
			</if>
			<if test="address_3 != null and address_3 != ''">
				ADDRESS_3 = #{address_3,jdbcType=VARCHAR},
			</if>
			<if test="telephone_1 != null and telephone_1 != ''">
				TELEPHONE_1 = #{telephone_1,jdbcType=VARCHAR},
			</if>
			<if test="telephone_2 != null and telephone_2 != ''">
				TELEPHONE_2 = #{telephone_2,jdbcType=VARCHAR},
			</if>
			<if test="telephone_3 != null and telephone_3 != ''">
				TELEPHONE_3 = #{telephone_3,jdbcType=VARCHAR},
			</if>
			<if test="fax != null and fax != ''">
				FAX = #{fax,jdbcType=VARCHAR},
			</if>
			<if test="email != null and email != ''">
				EMAIL = #{email,jdbcType=DECIMAL},
			</if>
			<if test="tax_ref_no != null and tax_ref_no != ''">
				TAX_FRE_NO = #{tax_ref_no,jdbcType=DECIMAL},
			</if>
			<if test="exist != null and exist != ''">
				EXIST = #{exist,jdbcType=DECIMAL},
			</if>
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
			<if test="orgId != null and orgId != ''">
				ORG_ID = #{orgId,jdbcType=VARCHAR},
			</if>
				UPDATE_DATE = now()
		</set>
		where
		CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
	</update>
	
	<insert id="insertCustomerDO" parameterType="com.power7000.restful.model.CustomerModel">
		insert into ppm_public_cms_customer (
			CUSTOMER_ID,
			SUR_NAME,
			OTHER_NAME,
			ID_NO,
			ID_TYPE,
			ADDRESS_1,
			ADDRESS_2,
			ADDRESS_3,
			TELEPHONE_1,
			TELEPHONE_2,
			TELEPHONE_3,
			FAX,
			EMAIL,
			TAX_FRE_NO,
			EXIST,
			IS_STORAGE,
			ORG_ID,
			CREATE_DATE,
			UPDATE_DATE
		)
		values (
			#{customerId,jdbcType=VARCHAR},
			#{surname,jdbcType=VARCHAR},
			#{other_names,jdbcType=VARCHAR},
			#{idNo,jdbcType=VARCHAR}, 
			#{id_type,jdbcType=VARCHAR},
			#{address_1,jdbcType=VARCHAR},
			#{address_2,jdbcType=DECIMAL},
			#{address_3,jdbcType=DECIMAL},
			#{telephone_1,jdbcType=VARCHAR},
			#{telephone_2,jdbcType=VARCHAR}, 
			#{telephone_3,jdbcType=VARCHAR},
			#{fax,jdbcType=VARCHAR},
			#{email,jdbcType=VARCHAR},
			#{tax_ref_no,jdbcType=VARCHAR},
			#{exist,jdbcType=BOOLEAN},
			#{isStorage,jdbcType=VARCHAR},
			#{orgId,jdbcType=VARCHAR},
			now(),
			now()
		)
	</insert>
	
</mapper>