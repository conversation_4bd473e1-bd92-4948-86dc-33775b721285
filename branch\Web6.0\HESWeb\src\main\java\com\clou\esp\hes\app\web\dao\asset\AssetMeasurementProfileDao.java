/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeasurementProfile{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：王佳乐
 * 创建于：2018-02-28 02:48:00
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.asset;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;

@MyBatisDao
public interface AssetMeasurementProfileDao extends CrudDao<AssetMeasurementProfile>{

	public Integer deleteByEntity(AssetMeasurementProfile entity);
	public AssetMeasurementProfile getProfileByProfileid(String profileId);
	public AssetMeasurementProfile getProfileByMgAndDataItemAndType(@Param(value="mgId") String mgId, @Param(value="dataitemId") String dataitemId, @Param(value="type") String type);
}