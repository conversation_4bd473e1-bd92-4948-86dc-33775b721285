<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.SysDataLogMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.SysDataLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="SYS_DATA_LOG_ID" jdbcType="VARCHAR" property="sysDataLogId" />
    <result column="SYS_ORG_ID" jdbcType="VARCHAR" property="sysOrgId" />
    <result column="TABLE_NAME" jdbcType="VARCHAR" property="tableName" />
    <result column="DATA_ID" jdbcType="VARCHAR" property="dataId" />
    <result column="VERSION_NUMBER" jdbcType="INTEGER" property="versionNumber" />
    <result column="OP_LOG_ID" jdbcType="VARCHAR" property="opLogId" />
    <result column="OP_TIME" jdbcType="TIMESTAMP" property="opTime" />
    <result column="OP_ID" jdbcType="VARCHAR" property="opId" />
    <result column="OP_NAME" jdbcType="VARCHAR" property="opName" />
    <result column="DATA_CONTENT" jdbcType="LONGVARCHAR" property="dataContent" />
    <result column="REMARK" jdbcType="LONGVARCHAR" property="remark" />
  </resultMap>
</mapper>