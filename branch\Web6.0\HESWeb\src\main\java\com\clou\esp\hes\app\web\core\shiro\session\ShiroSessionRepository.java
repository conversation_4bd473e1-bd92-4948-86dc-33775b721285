package com.clou.esp.hes.app.web.core.shiro.session;

import java.io.Serializable;
import java.util.Collection;

import org.apache.shiro.session.Session;

/**
 * Session操作
 * 
 * <AUTHOR>
 * 
 */
public interface ShiroSessionRepository {

	/**
	 * 存储Session
	 * 
	 * @param session
	 */
	public void saveSession(Session session);

	/**
	 * 删除session
	 * 
	 * @param sessionId
	 */
	public void deleteSession(Serializable sessionId);

	/**
	 * 获取session
	 * 
	 * @param sessionId
	 * @return
	 */
	public Session getSession(Serializable sessionId);

	/**
	 * 获取所有sessoin
	 * 
	 * @return
	 */
	public Collection<Session> getAllSessions();
}
