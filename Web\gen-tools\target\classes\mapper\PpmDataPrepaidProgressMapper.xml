<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.PpmDataPrepaidProgressMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.PpmDataPrepaidProgress">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="LAST_PAYMENT_TV" jdbcType="DATE" property="lastPaymentTv" />
    <result column="METER_COMMISSIONING_DATE" jdbcType="DATE" property="meterCommissioningDate" />
  </resultMap>
</mapper>