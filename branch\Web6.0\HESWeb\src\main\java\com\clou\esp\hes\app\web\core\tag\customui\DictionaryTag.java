package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：DictionaryTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：选择数据字典标签
 * 修改人：严浪
 * 修改时间：2017年8月2日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class DictionaryTag extends TagSupport {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	protected String type; // 展示类型：单选radio,复选check,下拉select,下拉复选multiselect
	protected String clazz = "hes_select"; // select 样式
	protected String name; // 组件name
	protected String id;// 组件id
	protected String fatherId; //联动父级组件ID
	protected String url; //查询URL
	protected String replace;// 字典对如1:男,0:女
	protected String dictionary;// 字典编码: sex
	protected String defaults; // 默认值
	protected String operClick; //操作事件
	protected String datatype;
	protected boolean beEdited=true;
	protected String selectNull="index.pleaseChoose";
	private String selectNullStr = "";
	protected String splitStr=",";
	protected String title="";

	public int doStartTag() throws JspTagException {
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspTagException {
		JspWriter out = null;
		selectNullStr = MutiLangUtil.doMutiLang(selectNull);
		try {
			out = this.pageContext.getOut();
			out.print(end().toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return EVAL_PAGE;
	}

	public StringBuffer end() {
		StringBuffer sb = new StringBuffer();
		
		// 先拼接html 单选radio,复选check,下拉select,下拉复选multiselect
		if(type.equals("radio")){
			sb.append("<div class=\"row\" id=\""+id+"_radio\"> ");
			sb.append(getKeyValue());
			sb.append("</div>");
		}else if(type.equals("check")){
			sb.append("<div class=\"\" id=\""+id+"_check\" > ");
			sb.append(getKeyValue());
			sb.append("</div>");
		}else if(type.equals("select")){
			if(!beEdited){
				sb.append("<input type=\"hidden\" value=\""+defaults+"\"  name=\""+name+"\" class=\"inputxt\" "+(StringUtil.isNotEmpty(title)?"title=\""+getTitle()+"\"":"")+"/>");
			}
			if(clazz.indexOf("multiple")>0){
				
				sb.append("<select class=\""+clazz.split("#")[0]+"\"  multiple='true' "+(beEdited?" name=\""+name+"\" ":" disabled=\"disabled\"  style=\"color: rgb(185, 179, 179);\" ")+"  id=\""+id+"\"  "+(StringUtil.isNotEmpty(operClick)?" onchange=\""+operClick+"(this.value)\" ":"")+" "+(StringUtil.isNotEmpty(datatype)?"datatype=\""+datatype+"\"":"")+" "+(StringUtil.isNotEmpty(title)?"title=\""+getTitle()+"\"":"")+" > ");
			}else{
				sb.append("<select class=\""+clazz+"\" "+(beEdited?" name=\""+name+"\" ":" disabled=\"disabled\"  style=\"color: rgb(185, 179, 179);\" ")+"  id=\""+id+"\"  "+(StringUtil.isNotEmpty(operClick)?" onchange=\""+operClick+"(this.value)\" ":"")+" "+(StringUtil.isNotEmpty(datatype)?"datatype=\""+datatype+"\"":"")+" "+(StringUtil.isNotEmpty(title)?"title=\""+getTitle()+"\"":"")+" > ");
			}
			sb.append(getKeyValue());
			sb.append("</select>");
		}else if(type.equals("multiselect")){
			if(!beEdited){
				sb.append("<input type=\"hidden\" value=\""+defaults+"\"  name=\""+name+"\" class=\"inputxt\" />");
			}
			sb.append("<select multiple=\"multiple\" class=\"hes_select\" style=\"display: block;\" "+(beEdited?" name=\""+name+"\" ":" disabled=\"disabled\"  style=\"color: rgb(185, 179, 179);\" ")+"  id=\""+id+"\"  "+" "+(StringUtil.isNotEmpty(datatype)?"datatype=\""+datatype+"\"":"")+" > ");
			sb.append(getKeyValue());
			sb.append("</select>");
		}
		// 再拼接js
		sb.append("<script type=\"text/javascript\">");
		if(StringUtil.isNotEmpty(fatherId)&&(type.equals("select")||type.equals("multiselect"))){
			if(StringUtil.isNotEmpty(url)&&StringUtil.isNotEmpty(dictionary)){
				String[] field=dictionary.split(",");
				if(field.length>=2){
					 sb.append("$(function() {");
					 sb.append("var "+fatherId+"Val=$(\"#"+fatherId+"\").val();");
					 sb.append("if("+fatherId+"Val!=null&&"+fatherId+"Val!=''){");
					 sb.append(""+name+"fun();");
					 sb.append("}");
					 sb.append(" var "+fatherId+"Sel = document.getElementById('"+fatherId+"'); ");
					 sb.append(""+fatherId+"Sel.addEventListener(\"change\","+name+"fun);");
					 sb.append("});");
					 sb.append("var "+name+"fun=function(){  ");
					 sb.append("var "+fatherId+"Val=$(\"#"+fatherId+"\").val();");
					 sb.append("   $.ajax({");
						sb.append("       type: 'POST',");
						if(url.indexOf("?")>0){
							sb.append("     url: '"+url+"&"+fatherId+"='+"+fatherId+"Val,");
						}else{
							sb.append("     url: '"+url+"?"+fatherId+"='+"+fatherId+"Val,");
						}
						sb.append("       dataType: 'json',");
						sb.append("       success: function(data){");
						sb.append("       if(data.success){");
						if(type.equals("select")){
							sb.append("$(\"#"+id+"\").html('');");
							if(StringUtil.isNotEmpty(selectNull)){
								sb.append("$(\"#"+id+"\").append('<option value=\"\"  selected=\"selected\"  >"+selectNullStr+"</option>');");
							}
						}
						sb.append("          for(var i=0;i<data.obj.length;i++){");
						sb.append( getKeyValueJs(field));
						sb.append("          }");
						sb.append("        }else{}");
						sb.append("isLoad=true;");
						sb.append("     },error: function (msg) {");
						sb.append("isLoad=true;");
						sb.append("      }");
						sb.append("    });");
					 sb.append("}");
				}
			}
		}else if(StringUtil.isNotEmpty(url)){
			if(StringUtil.isNotEmpty(dictionary)){
				String[] field=dictionary.split(",");
				if(field.length>=2){
					sb.append("$(function() {"+name+"fun();});");
					sb.append("var "+name+"fun=function(){  ");
					sb.append("   $.ajax({");
					sb.append("       type: 'POST',");
					sb.append("       url: '"+url+"',");
					sb.append("       dataType: 'json',");
					sb.append("       success: function(data){");
					sb.append("       if(data.success){");
					if(type.equals("select")){
						sb.append("$(\"#"+id+"\").html('');");
						if(StringUtil.isNotEmpty(selectNull)){
							sb.append("$(\"#"+id+"\").append('<option value=\"\"  selected=\"selected\"  >"+selectNullStr+"</option>');");
						}
					}
					sb.append("          for(var i=0;i<data.obj.length;i++){");
					sb.append( getKeyValueJs(field));
					sb.append("          }");
					sb.append("        }else{}");
					sb.append("isLoad=true;");
					sb.append("     },error: function (msg) {");
					sb.append("isLoad=true;");
					sb.append("      }");
					sb.append("    });");
					sb.append("}");
				}
			}
		}else if(StringUtil.isNotEmpty(dictionary)){
			sb.append("$(function() {"+name+"fun();});");
			sb.append("var "+name+"fun=function(){  ");
			sb.append("   $.ajax({");
			sb.append("       type: 'POST',");
			sb.append("     url: getRootPathWeb()+'/sysTypegroupController/getSelete.do?dictionary="+dictionary+"',");
			sb.append("       dataType: 'json',");
			sb.append("       success: function(data){");
			sb.append("       if(data.success){");
			if(type.equals("select")){
				sb.append("$(\"#"+id+"\").html('');");
				if(StringUtil.isNotEmpty(selectNull)){
					sb.append("$(\"#"+id+"\").append('<option value=\"\"  selected=\"selected\"  >"+selectNullStr+"</option>');");
				}
			}
			sb.append("          for(var i=0;i<data.obj.length;i++){");
			sb.append( getKeyValueJs(null));
			sb.append("          }");
			sb.append("        }else{}");
			sb.append("isLoad=true;");
			sb.append("     },error: function (msg) {");
			sb.append("isLoad=true;");
			sb.append("      }");
			sb.append("    });");
			sb.append("}");
		}
		if(type.equals("multiselect")){
			sb.append("$('#"+id+"').change(function() {");
			if(StringUtil.isNotEmpty(operClick)){
				sb.append(operClick+"($(this).val());");
			}
			sb.append("}).multipleSelect({width: '100%'});");
		}
		sb.append("</script>");
		return sb;
	}
	
	 private StringBuffer getKeyValueJs(String[] field){
		 StringBuffer sb = new StringBuffer();
		 if(field!=null){
			 if(type.equals("radio")){
				 sb.append("var f=true;");
				 sb.append("$(\"#"+id+"_radio\").append(");
				 sb.append(" '<div class=\"col-md-3\">");
				 sb.append("  <input type=\"radio\" value=\"'+data.obj[i]."+field[0]+"+'\" name=\""+name+"\"  '");
				 if(StringUtil.isNotEmpty(defaults)){
					 sb.append(" +('"+defaults+"'==data.obj[i]."+field[0]+"?' checked=\"checked\" ':'')+ ");
				 }else{
					 sb.append(" +(f?' checked=\"checked\" ':'')+ ");
				 }
				 sb.append("' class=\"pr1\"   />");
				 sb.append("  	<label for=\"male\">'+data.obj[i]."+field[1]+"+'</label> ");
				 sb.append("  </div>');");
			 }else if(type.equals("check")){
				 String[] defs=defaults.split(",");
             	 List<String> defList=Arrays.asList(defs);
				 sb.append("var f=true;");
				 sb.append("if(");
				 for(int i=0;i<defList.size();i++){
					if(i==0){
						sb.append("'"+defList.get(i)+"'==data.obj[i]."+field[0]+"");
					}else{
						sb.append("||'"+defList.get(i)+"'==data.obj[i]."+field[0]+"");
					}
				 }
				 sb.append("){");
				 sb.append("$(\"#"+id+"_check\").append(");
				 sb.append(" '<div class=\"col-md-3\">");
				 sb.append("  <input type=\"checkbox\" id=\"checkbox-"+name+"'+data.obj[i]."+field[0]+"+'\" value=\"'+data.obj[i]."+field[0]+"+'\" name=\""+name+"\"  '");
				 sb.append("  +' checked=\"checked\" ' ");
				 sb.append("  +' class=\"check_view_state\"  style=\"display: none;\"   />");
				 sb.append("  	<label for=\"checkbox-"+name+"'+data.obj[i]."+field[0]+"+'\"></label><span class=\"status\">'+data.obj[i]."+field[1]+"+'</span> ");
				 sb.append("  </div>');");
				 sb.append("}else{");
				 sb.append("$(\"#"+id+"_check\").append(");
				 sb.append(" '<div class=\"col-md-3\">");
				 sb.append("  <input type=\"checkbox\" id=\"checkbox-"+name+"'+data.obj[i]."+field[0]+"+'\" value=\"'+data.obj[i]."+field[0]+"+'\" name=\""+name+"\"  '");
				 sb.append("+' class=\"check_view_state\"  style=\"display: none;\"  />");
				 sb.append("  	<label for=\"checkbox-"+name+"'+data.obj[i]."+field[0]+"+'\"></label><span class=\"status\">'+data.obj[i]."+field[1]+"+'</span> ");
				 sb.append("  </div>');");
				 sb.append("}");
			 }else if(type.equals("select")){
				 sb.append("$(\"#"+id+"\").append(");
				 sb.append("'<option value=\"'+data.obj[i]."+field[0]+"+'\" '  ");
				 if(StringUtil.isNotEmpty(defaults)){
					 sb.append(" +('"+defaults+"'==data.obj[i]."+field[0]+"?' selected=\"selected\" ':'') ");
				 }
				 sb.append(" +' >'+data.obj[i]."+field[1]+"+'</option>');");
			 }else if(type.equals("multiselect")){
				 String[] defs=defaults.split(",");
             	 List<String> defList=Arrays.asList(defs);
             	 sb.append("if(");
				 for(int i=0;i<defList.size();i++){
					if(i==0){
						sb.append("'"+defList.get(i)+"'==data.obj[i]."+field[0]+"");
					}else{
						sb.append("||'"+defList.get(i)+"'==data.obj[i]."+field[0]+"");
					}
				 }
				 sb.append("){");
				 sb.append("$(\"#"+id+"\").append(");
				 sb.append("'<option value=\"'+data.obj[i]."+field[0]+"+'\" '  ");
				 sb.append(" +' selected=\"selected\" '  ");
				 sb.append(" +' >'+data.obj[i]."+field[1]+"+'</option>');");
				 sb.append("}else{");
				 sb.append("$(\"#"+id+"\").append(");
				 sb.append("'<option value=\"'+data.obj[i]."+field[0]+"+'\" '  ");
				 sb.append(" +' >'+data.obj[i]."+field[1]+"+'</option>');");
				 sb.append("}");
			 }
		 }else{
			 if(type.equals("radio")){
				 sb.append("var f=true;");
				 sb.append("$(\"#"+id+"_radio\").append(");
				 sb.append(" '<div class=\"col-md-3\">");
				 sb.append("  <input type=\"radio\" value=\"'+data.obj[i].typecode+'\" name=\""+name+"\"  '");
				 if(StringUtil.isNotEmpty(defaults)){
					 sb.append(" +('"+defaults+"'==data.obj[i].typecode?' checked=\"checked\" ':'')+ ");
				 }else{
					 sb.append(" +(f?' checked=\"checked\" ':'')+ ");
				 }
				 sb.append("' class=\"pr1\"   />");
				 sb.append("  	<label for=\"male\">'+data.obj[i].typename+'</label> ");
				 sb.append("  </div>');");
			 }else if(type.equals("check")){
				 String[] defs=defaults.split(",");
             	 List<String> defList=Arrays.asList(defs);
				 sb.append("var f=true;");
				 sb.append("if(");
				 for(int i=0;i<defList.size();i++){
					if(i==0){
						sb.append("'"+defList.get(i)+"'==data.obj[i].typecode");
					}else{
						sb.append("||'"+defList.get(i)+"'==data.obj[i].typecode");
					}
				 }
				 sb.append("){");
				 sb.append("$(\"#"+id+"_check\").append(");
				 sb.append(" '<div class=\"col-md-3\">");
				 sb.append("  <input type=\"checkbox\" id=\"checkbox-"+name+"'+data.obj[i].typecode+'\" value=\"'+data.obj[i].typecode+'\" name=\""+name+"\"  '");
				 sb.append("  +' checked=\"checked\" ' ");
				 sb.append("  +' class=\"check_view_state\"  style=\"display: none;\"  />");
				 sb.append("  	<label for=\"checkbox-"+name+"'+data.obj[i].typecode+'\"></label><span class=\"status\">'+data.obj[i].typename+'</span> ");
				 sb.append("  </div>');");
				 sb.append("}else{");
				 sb.append("$(\"#"+id+"_check\").append(");
				 sb.append(" '<div class=\"col-md-3\">");
				 sb.append("  <input type=\"checkbox\" id=\"checkbox-"+name+"'+data.obj[i].typecode+'\" value=\"'+data.obj[i].typecode+'\" name=\""+name+"\"  '");
				 sb.append("+' class=\"check_view_state\"  style=\"display: none;\"   />");
				 sb.append("  	<label for=\"checkbox-"+name+"'+data.obj[i].typecode+'\"></label><span class=\"status\">'+data.obj[i].typename+'</span> ");
				 sb.append("  </div>');");
				 sb.append("}");
			 }else if(type.equals("select")){
				 sb.append("$(\"#"+id+"\").append(");
				 sb.append("'<option value=\"'+data.obj[i].typecode+'\" '  ");
				 if(StringUtil.isNotEmpty(defaults)){
					 sb.append(" +('"+defaults+"'==data.obj[i].typecode?' selected=\"selected\" ':'') ");
				 }
				 sb.append(" +' >'+data.obj[i].typename+'</option>');");
			 }else if(type.equals("multiselect")){
				 String[] defs=defaults.split(",");
             	 List<String> defList=Arrays.asList(defs);
             	 sb.append("if(");
				 for(int i=0;i<defList.size();i++){
					if(i==0){
						sb.append("'"+defList.get(i)+"'==data.obj[i].typecode");
					}else{
						sb.append("||'"+defList.get(i)+"'==data.obj[i].typecode");
					}
				 }
				 sb.append("){");
				 sb.append("$(\"#"+id+"\").append(");
				 sb.append("'<option value=\"'+data.obj[i].typecode+'\" '  ");
				 sb.append(" +' selected=\"selected\" '  ");
				 sb.append(" +' >'+data.obj[i].typename+'</option>');");
				 sb.append("}else{");
				 sb.append("$(\"#"+id+"\").append(");
				 sb.append("'<option value=\"'+data.obj[i].typecode+'\" '  ");
				 sb.append(" +' >'+data.obj[i].typename+'</option>');");
				 sb.append("}");
			 }
		 }
		 return sb;
	 }

	 private StringBuffer getKeyValue(){
		 StringBuffer sb = new StringBuffer();
		 if(type.equals("select")){
			 if(StringUtil.isNotEmpty(selectNull)){
					sb.append("<option value=\"\"  >"+selectNullStr+"</option>");
				}
		 }
		 if(StringUtil.isNotEmpty(replace)){
	            String[] reps=replace.split(splitStr);
	            boolean f=true;
	            for(String rep:reps){
	                String key=rep.substring(0,rep.indexOf(":"));
	                String value=rep.substring(rep.indexOf(":")+1, rep.length());
	                value=MutiLangUtil.doMutiLang(value);
	                if(type.equals("radio")){
	                	sb.append(" <div class=\"col-md-3\">");
	                	sb.append("  <input type=\"radio\" value=\""+key+"\" "+(beEdited?"":" disabled=\"disabled\" ")+" name=\""+name+"\" "+(StringUtil.isNotEmpty(operClick)?" onclick=\""+operClick+"(this.value)\" ":"")+"  ");
	                	if(StringUtil.isNotEmpty(defaults)&&defaults.equals(key)){
	                		sb.append(" checked=\"checked\" ");
	                		f=false;
	                	}else{
	                		if(f){
	                			sb.append(" checked=\"checked\" ");
	                			f=false;
	                		}
	                	}
	                	sb.append(" class=\"pr1\"   />");
	                	sb.append("  	<label for=\"male\">"+value+"</label> ");
	                	sb.append("  </div>");
	        		}else if(type.equals("check")){
	        			String checkBoxClick="var "+name+"obj=document.getElementsByName('"+name+"');"
	        					+"var "+name+"Ids='';"
	        				+"for(var i=0; i<"+name+"obj.length; i++){ "
	        				+"if("+name+"obj[i].checked){if(i==0){"+name+"Ids+="+name+"obj[i].value;}else{"+name+"Ids+=','+"+name+"obj[i].value;}}}";
	        			if(StringUtil.isNotEmpty(operClick)){
	        				checkBoxClick+=""+operClick+"("+name+"Ids);";
	        			}
	        			sb.append(" <div class=\"col-md-4\" style=\"display:inline-block;\">");
	                	sb.append("  <input type=\"checkbox\" id=\"checkbox-"+name+key+"\" value=\""+key+"\" "+(beEdited?"":" disabled=\"disabled\" ")+" name=\""+name+"\" "+(StringUtil.isNotEmpty(operClick)?"onclick=\""+checkBoxClick+"\"":"")+"  ");
	                	String[] defs=defaults.split(",");
	                	List<String> defList=Arrays.asList(defs);
	                	if(StringUtil.isNotEmpty(defaults)&&defList.contains(key)){
	                		sb.append(" checked=\"checked\" ");
	                	}
	                	sb.append(" class=\"check_view_state\"  style=\"display: none;\" />");
	                	sb.append("  	<label for=\"checkbox-"+name+key+"\"></label><span class=\"status\">"+value+"</span> ");
	                	sb.append("  </div>");
	        		}else if(type.equals("select")){
	        			sb.append("<option value=\""+key+"\"  ");
	        			if(StringUtil.isNotEmpty(defaults)&&defaults.equals(key)){
	                		sb.append(" selected=\"selected\" ");
	                	}
	        			sb.append(" >"+value+"</option>");
	        		}else if(type.equals("multiselect")){
	        			String[] defs=defaults.split(",");
	                	List<String> defList=Arrays.asList(defs);
	        			sb.append("<option value=\""+key+"\"  ");
	        			if(StringUtil.isNotEmpty(defaults)&&defList.contains(key)){
	        				sb.append(" selected=\"selected\" ");
	        			}
	        			sb.append(" >"+value+"</option>");
	        		}
	            }
	           
	        }else if(type.equals("select")&&StringUtil.isNotEmpty(defaults)){
	        	sb.append("<option value=\""+defaults+"\"  ");
            	sb.append(" selected=\"selected\" ");
    			sb.append(" ></option>");
	        }
		 return sb;
	 }

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getReplace() {
		return replace;
	}

	public void setReplace(String replace) {
		this.replace = replace;
	}

	public String getDictionary() {
		return dictionary;
	}

	public void setDictionary(String dictionary) {
		this.dictionary = dictionary;
	}

	public String getDefaults() {
		return defaults;
	}

	public void setDefaults(String defaults) {
		this.defaults = defaults;
	}

	public String getOperClick() {
		return operClick;
	}

	public void setOperClick(String operClick) {
		this.operClick = operClick;
	}

	public boolean isBeEdited() {
		return beEdited;
	}

	public void setBeEdited(boolean beEdited) {
		this.beEdited = beEdited;
	}

	public String getFatherId() {
		return fatherId;
	}

	public void setFatherId(String fatherId) {
		this.fatherId = fatherId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDatatype() {
		return datatype;
	}

	public void setDatatype(String datatype) {
		this.datatype = datatype;
	}

	public String getSelectNull() {
		return selectNull;
	}

	public void setSelectNull(String selectNull) {
		this.selectNull = selectNull;
	}

	public String getSelectNullStr() {
		return selectNullStr;
	}

	public void setSelectNullStr(String selectNullStr) {
		this.selectNullStr = selectNullStr;
	}

	public String getSplitStr() {
		return splitStr;
	}

	public void setSplitStr(String splitStr) {
		this.splitStr = splitStr;
	}

	public String getClazz() {
		return clazz;
	}

	public void setClazz(String clazz) {
		this.clazz = clazz;
	}

	public String getTitle() {
		return MutiLangUtil.doMutiLang(title);
	}

	public void setTitle(String title) {
		this.title = title;
	}

	
}
