<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.AccountWalletMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.AccountWallet">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="user_open_id" jdbcType="VARCHAR" property="userOpenId" />
    <result column="user_amount" jdbcType="DECIMAL" property="userAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_password" jdbcType="VARCHAR" property="payPassword" />
    <result column="is_open" jdbcType="INTEGER" property="isOpen" />
    <result column="check_key" jdbcType="VARCHAR" property="checkKey" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
</mapper>