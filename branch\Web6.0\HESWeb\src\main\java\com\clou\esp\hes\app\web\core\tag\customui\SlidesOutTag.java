package com.clou.esp.hes.app.web.core.tag.customui;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.tagext.Tag;
import javax.servlet.jsp.tagext.TagSupport;

/**
 * 文件名：SlidesOutTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：左滑展示框
 * 修改人：严浪
 * 修改时间：2017年9月20日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class SlidesOutTag extends TagSupport {
	/**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    protected String title;
    protected String exp;//判断链接是否显示的表达式
    protected String langArg;//按钮
    protected String icon;//样式
    protected String url; //展示URL
    protected String proportion="5"; //宽度比例
	 public int doStartTag() throws JspTagException {
	        return EVAL_PAGE;
	    }
	 public int doEndTag() throws JspTagException {
        Tag t = findAncestorWithClass(this, FindListTag.class);
        FindListTag parent = (FindListTag) t;
        parent.setSlidesOut(title,  exp,  langArg,  icon, url, proportion);
        return EVAL_PAGE;
    }
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getExp() {
		return exp;
	}
	public void setExp(String exp) {
		this.exp = exp;
	}
	public String getLangArg() {
		return langArg;
	}
	public void setLangArg(String langArg) {
		this.langArg = langArg;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getProportion() {
		return proportion;
	}
	public void setProportion(String proportion) {
		this.proportion = proportion;
	}
	 
	
	 
}
