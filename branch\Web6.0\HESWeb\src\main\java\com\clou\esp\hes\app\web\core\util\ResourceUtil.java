package com.clou.esp.hes.app.web.core.util;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.cxf.common.util.StringUtils;

import com.clou.esp.hes.app.web.model.system.SysUser;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.log.LogUtil;

/**
 * 项目参数工具类
 * 
 */
public class ResourceUtil {
	private static ResourceBundle bundle = null;

	static {
		try {
			bundle = new PropertyResourceBundle(new BufferedInputStream(
					new FileInputStream(
							System.getenv("POWER7000G_WEB_CONFIG_HOME")
									+ "/init-web.properties")));
			
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取session定义名称
	 * 
	 * @return
	 */
	public static final String getSessionattachmenttitle(String sessionName) {
		//如果profileId属性为空，放入10001
		if("profileId".equals(sessionName)) {
			String profileId="10001";
			try {
				profileId=bundle.getString("profileId");
				if(StringUtils.isEmpty(profileId)) {
					profileId="10001";
				}
			}catch(Exception ex) {
			}
			return profileId;
		}else {
			return bundle.getString(sessionName);
		}
		
	}
	
	/**
	 * 获取uci回调地址
	 * @return
	 */
	public static final String getUciBasePath(HttpServletRequest request) {
		String bathPath = null;
		try {
			bathPath = ResourceUtil.getSessionattachmenttitle("hes.uci.back.url");
		} catch (Exception ex) {
			String path = request.getContextPath();
			bathPath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() +  path;
		}
		return bathPath;
	}

	/**
	 * 获取配置文件参数
	 * 
	 * @param name
	 * @return
	 */
	public static final String getConfigByName(String name) {
		return bundle.getString(name);
	}

	/**
	 * 获得请求路径
	 * 
	 * @param request
	 * @return
	 */
	public static String getRequestPath(HttpServletRequest request) {
		String requestPath = request.getRequestURI() + "?"
				+ request.getQueryString();
		if (requestPath.indexOf("&") > -1) {// 去掉其他参数
			requestPath = requestPath.substring(0, requestPath.indexOf("&"));
		}
		requestPath = requestPath
				.substring(request.getContextPath().length() + 1);// 去掉项目路径
		return requestPath;
	}

	public static String getSysPath() {
		String path = Thread.currentThread().getContextClassLoader()
				.getResource("").toString();
		String temp = path.replaceFirst("file:/", "").replaceFirst(
				"WEB-INF/classes/", "");
		String separator = System.getProperty("file.separator");
		String resultPath = temp.replaceAll("/", separator + separator)
				.replaceAll("%20", " ");
		return resultPath;
	}

	/**
	 * 获取项目根目录
	 * 
	 * @return
	 */
	public static String getPorjectPath() {
		String nowpath; // 当前tomcat的bin目录的路径 如
						// D:\java\software\apache-tomcat-6.0.14\bin
		String tempdir;
		nowpath = System.getProperty("user.dir");
		tempdir = nowpath.replace("bin", "webapps"); // 把bin 文件夹变到 webapps文件里面
		tempdir += "\\"; // 拼成D:\java\software\apache-tomcat-6.0.14\webapps\sz_pro
		return tempdir;
	}

	public static String getClassPath() {
		String path = Thread.currentThread().getContextClassLoader()
				.getResource("").toString();
		String temp = path.replaceFirst("file:/", "");
		String separator = System.getProperty("file.separator");
		String resultPath = temp.replaceAll("/", separator + separator);
		return resultPath;
	}

	public static String getSystempPath() {
		return System.getProperty("java.io.tmpdir");
	}

	public static String getSeparator() {
		return System.getProperty("file.separator");
	}

	public static String getParameter(String field) {
		HttpServletRequest request = ContextHolderUtils.getRequest();
		return request.getParameter(field);
	}

	// ---author:jg_xugj----start-----date:20151226--------for：#814
	// 【数据权限】扩展支持写表达式，通过session取值
	/**
	 * 获取用户session 中的变量
	 * 
	 * @param key
	 *            Session 中的值
	 * @return
	 */
	private static String getSessionData(String key) {
		// ${myVar}%
		// 得到${} 后面的值
		String moshi = "";
		if (key.indexOf("}") != -1) {
			moshi = key.substring(key.indexOf("}") + 1);
		}
		String returnValue = null;
		// ---author:jg_xugj----start-----date:20151226--------for：修改bug
		// 1、key.contains("${") 应改为 key.contains("#{") 2、StringUtil.isEmpty(key)
		// 判断 不为空
		if (key.contains("#{")) {
			key = key.substring(2, key.indexOf("}"));
		}
		// 从session中取得值
		if (!StringUtil.isEmpty(key)) {
			HttpSession session = ContextHolderUtils.getSession();
			returnValue = (String) session.getAttribute(key);
		}

		// 结果加上${} 后面的值
		if (returnValue != null) {
			returnValue = returnValue + moshi;
		}
		return returnValue;
	}

	public static final SysUser getSessionUser(HttpServletRequest request) {
		SysUser su = (SysUser) request.getSession(true).getAttribute(
				Globals.SESSION_LOGIN_SYS_USER);
		return su;
	}

	public static void main(String[] args) {
		LogUtil.info(getPorjectPath());
		LogUtil.info(getSysPath());
	}

	// public static boolean isFuzzySearch(){
	// return "1".equals(bundle.getString("fuzzySearch"));
	// }

}
