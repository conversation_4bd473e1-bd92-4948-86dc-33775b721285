/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictMeterDataStorageTable{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.dict;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.dict.DictMeterDataStorageTable;
import com.clou.esp.hes.app.web.model.report.MeterDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport;
import com.clou.esp.hes.app.web.model.report.MissDataReport1;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DictMeterDataStorageTableDao extends CrudDao<DictMeterDataStorageTable>{
	/**
	 * 查询动态数据报表
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<MeterDataReport> getForJqGridMeter(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridMeterOrg(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridMeterOnlyShenyu(JqGridSearchTo jqGridSearchTo);
	public  List<MeterDataReport> getForJqGridMeterOnlyShenyuExcel(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridMeterExcel(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridMeterMonthShenyu(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridMeterMonthShenyuExcel(JqGridSearchTo jqGridSearchTo);

	/**
	 * 根据对象查询动态数据项
	 * @return
	 */
	public List<MeterDataReport> getMeterDataList(MeterDataReport meterDataReport);

	/**
	 * 根据对象查询动态数据项
	 * @return
	 */
	public List<MeterDataReport> getMeterDataListOrg(MeterDataReport meterDataReport);

	/**
	 * 根据对象查询动态数据项
	 * @return
	 */
	public List<MeterDataReport> getMeterDataListByCommnuicator(MeterDataReport meterDataReport);
	/**
	 * 查询动态数据报表
	 * @param jqGridSearchTo only Sn
	 * @return
	 */
	public List<MeterDataReport> getForJqGridMeterOnlySn(JqGridSearchTo jqGridSearchTo);
	/**
	 * 查询动态数据报表
	 * @param jqGridSearchTo by 集中器
	 * @return
	 */
	public List<MeterDataReport> getForJqGridCommnuicator(JqGridSearchTo jqGridSearchTo);
	public List<MeterDataReport> getForJqGridCommnuicatorExcel(JqGridSearchTo jqGridSearchTo);

	/**
	 * 查询动态数据报表
	 * @param jqGridSearchTo only Sn
	 * @return
	 */
	public List<MissDataReport> getMissDataList(MissDataReport missDataReport);
	
	/**
	 * 查询动态数据报表
	 * @param jqGridSearchTo only Sn
	 * @return
	 */
	public List<MissDataReport1> getMissDataList1(MissDataReport missDataReport);

	public List<Map<String, Object>> getMeterDateList(Map<String, Object> params);
	public List<MeterDataReport> getMeterDateListExcel(Map<String, Object> params);
	public List<Map<String, Object>> getEnergyDataDailyByMaps(Map<String, Object> params);
	
	public  Long    getCountTableName(@Param(value="tableName")String tableName);
	
	public void 	createTable(@Param (value="tableName")String tableName);
	
	public void 	deleteTable(@Param (value="tableName")String tableName);
	
	public void		addTableColumn(@Param (value="tableName")String tableName,@Param (value="columns")String columns);
	
	public void		deleteTableColumn(@Param (value="tableName")String tableName,@Param (value="columns")String columns);
}