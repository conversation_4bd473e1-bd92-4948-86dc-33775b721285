<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.TariffMapper">
  <resultMap id="BaseResultMap" type="com.power7000.restful.model.TariffModel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="TARIFF_ID" jdbcType="VARCHAR" property="tariffId" />
    <result column="TARIFF_CODE" jdbcType="VARCHAR" property="tariffCode" />
    <result column="TARIFF_GROPE" jdbcType="VARCHAR" property="tariffGroup" />
    <result column="IS_STORAGE" jdbcType="VARCHAR" property="isStorage" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  
  	<sql id="Base_Column_List">
		TARIFF_ID,TARIFF_CODE,TARIFF_GROPE,IS_STORAGE,CREATE_DATE,UPDATE_DATE
	</sql>
	
	<!-- 根据 tariff_code 更新数据 -->
	<update id="updateByNotNull" parameterType="java.util.Map">
		update ppm_public_cms_tariff
		<set>
			<if test="tariffGroup != null and tariffGroup != ''">
				TARIFF_GROPE = #{tariffGroup,jdbcType=VARCHAR},
			</if>
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
			UPDATE_DATE = now()
		</set>
		where TARIFF_CODE = #{tariffCode,jdbcType=VARCHAR}
	</update>
	<!-- 根据用户ID修改数据 -->
	<update id="updateTariff" parameterType="com.power7000.restful.model.TariffModel">
		update ppm_public_cms_tariff
		<set>
			<if test="tariffCode != null and tariffCode != ''">
				TARIFF_CODE = #{tariffCode,jdbcType=VARCHAR},
			</if>
			<if test="tariffGroup != null and tariffGroup != ''">
				TARIFF_GROPE = #{tariffGroup,jdbcType=VARCHAR},
			</if>
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
			UPDATE_DATE = now()
		</set>
		where TARIFF_ID = #{tariffId,jdbcType=VARCHAR}
	</update>
	<!-- 根据用户编号查询居民用户对象 -->
	<select id="selectTariffByUserNo" resultMap="BaseResultMap" parameterType="java.util.Map">
		select * from ppm_public_cms_tariff
		<where>
			1=1
			<if test="tariffCode != null and tariffCode != ''">
				and TARIFF_CODE = #{tariffCode,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	<!-- 根据费率ID查询Tariff 信息 -->
	<select id="selectTariffByTariffId" resultMap="BaseResultMap" parameterType="java.util.Map">
		select * from ppm_public_cms_tariff
		where TARIFF_ID = #{tariffId , jdbcType=VARCHAR}
	</select>
	<!-- 获取最新插入的tariff_id -->
	<select id="selectTariffId" resultMap="BaseResultMap" parameterType="java.util.Map">
		select max(TARIFF_ID) as TARIFF_ID from ppm_public_cms_tariff 
	</select>
	
	<insert id="insertTariff" parameterType="com.power7000.restful.model.TariffModel">
  
       <selectKey resultType="java.lang.String" order="BEFORE"
			keyProperty="tariffId">
			
				SELECT REPLACE(UUID(),'-','')
			
		</selectKey>
        
		insert into ppm_public_cms_tariff (
			TARIFF_ID,
			TARIFF_CODE,
			TARIFF_GROPE,
			IS_STORAGE,
			CREATE_DATE,
			UPDATE_DATE
		)
		values (
		#{tariffId,jdbcType=VARCHAR},
		#{tariffCode,jdbcType=VARCHAR},
		#{tariffGroup,jdbcType=VARCHAR},
		#{isStorage,jdbcType=VARCHAR},
		now(),
		now()
		)
	</insert>
</mapper>