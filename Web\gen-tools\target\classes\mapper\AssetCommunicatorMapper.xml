<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.AssetCommunicatorMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.AssetCommunicator">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="UTILITY_ID" jdbcType="VARCHAR" property="utilityId" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="NETWORK_IP" jdbcType="VARCHAR" property="networkIp" />
    <result column="NETWORK_PORT" jdbcType="INTEGER" property="networkPort" />
    <result column="PASS" jdbcType="VARCHAR" property="pass" />
    <result column="DEVICE_TYPE" jdbcType="INTEGER" property="deviceType" />
    <result column="MAC" jdbcType="VARCHAR" property="mac" />
    <result column="COM_TYPE" jdbcType="VARCHAR" property="comType" />
    <result column="FW_VERSION" jdbcType="VARCHAR" property="fwVersion" />
    <result column="REMOVE_FLAG" jdbcType="INTEGER" property="removeFlag" />
    <result column="SIM_NUM" jdbcType="VARCHAR" property="simNum" />
    <result column="IS_ENCRYPT" jdbcType="VARCHAR" property="isEncrypt" />
    <result column="AUTH_TYPE" jdbcType="VARCHAR" property="authType" />
    <result column="HLS_AK" jdbcType="VARCHAR" property="hlsAk" />
    <result column="HLS_EK" jdbcType="VARCHAR" property="hlsEk" />
  </resultMap>
</mapper>