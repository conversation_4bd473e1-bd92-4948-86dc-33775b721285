/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitemGroupMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.dict;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;
import com.clou.esp.hes.app.web.model.dict.DictDataitemGroupMap;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DictDataitemGroupMapDao extends CrudDao<DictDataitemGroupMap>{
	
	/**
	 * 根据ID查询groupName
	 * @return
	 * Wangjiale
	 * 2018年1月21日 下午4:45:24
	 */
	public DictDataitemGroupMap selectParamterTypeNameByItemId(@Param("itemId")String itemId);
	
	public  List<DictDataitem> unBindForJqGrid(JqGridSearchTo jqGridSearchTo) ;

	public DictDataitemGroupMap getItemGroupMapInfo(DictDataitemGroupMap itemInfo);
}