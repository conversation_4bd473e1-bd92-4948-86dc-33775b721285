/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataFwuJob{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-12-29 08:37:48
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataFwuJob;
import com.clou.esp.hes.app.web.model.data.DataFwuPlan;
import com.clou.esp.hes.app.web.model.data.TempJob;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DataFwuJobDao extends CrudDao<DataFwuJob> {

	public int batchInsert(List<DataFwuJob> dfjList);

	public List<DataFwuJob> getListGroupByField(DataFwuJob dfj);

	public List<DataFwuJob> getForJqGrids(JqGridSearchTo jqGridSearchTo);
	
	public int batchInsertJob(TempJob job);
	
	public void batchInsert_cretePlanAgainJob(DataFwuPlan plan);
	
	public void cancelByPlanId(String planId);

	public List<DataFwuJob> getListByJq(JqGridSearchTo jqGridSearchTo);
}