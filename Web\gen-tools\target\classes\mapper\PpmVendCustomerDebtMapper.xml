<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.PpmVendCustomerDebtMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.PpmVendCustomerDebt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="DEBT_DATE" jdbcType="DATE" property="debtDate" />
    <result column="DEBT_TOTAL" jdbcType="DECIMAL" property="debtTotal" />
    <result column="DEBT_PERIOD" jdbcType="INTEGER" property="debtPeriod" />
    <result column="PAYED_PERIOD" jdbcType="INTEGER" property="payedPeriod" />
    <result column="LEFT_PERIOD" jdbcType="INTEGER" property="leftPeriod" />
    <result column="MONEY_PERIOD" jdbcType="DECIMAL" property="moneyPeriod" />
    <result column="DEBT_MONEY" jdbcType="INTEGER" property="debtMoney" />
    <result column="PAYED_TOTAL" jdbcType="INTEGER" property="payedTotal" />
    <result column="DIVIDE_REMAINDER" jdbcType="DECIMAL" property="divideRemainder" />
    <result column="DEBT_TYPE" jdbcType="INTEGER" property="debtType" />
    <result column="RECORD_STATE" jdbcType="INTEGER" property="recordState" />
    <result column="DEBT_COLLECT_MONTH" jdbcType="INTEGER" property="debtCollectMonth" />
    <result column="DEBT_COLLECT_DATE" jdbcType="DATE" property="debtCollectDate" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="README" jdbcType="VARCHAR" property="readme" />
    <result column="DEBT_SOURCE" jdbcType="VARCHAR" property="debtSource" />
     
  </resultMap>
</mapper>