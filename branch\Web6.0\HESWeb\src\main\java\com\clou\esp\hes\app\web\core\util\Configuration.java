/*
 * 文件名：Defines.java 版权：Copyright by Power7000 Team 描述： 
 * 修改人：严浪 修改时间：2017年11月3日 跟踪单号： 修改单号： 修改内容：
 */

package com.clou.esp.hes.app.web.core.util;

import java.util.ArrayList;
import java.util.List;

import com.power7000g.core.util.base.StringUtil;

public final class Configuration {
    public static String indexPath = "E:\\log\\";
    public static List<String> services = new ArrayList<String>();
    public static String multicastgroup;
    public static int multicastport;
    public static void ReadConfiguration() {

        try {       
        	if(StringUtil.isNotEmpty(ResourceUtil.getSessionattachmenttitle("logger.indexPath"))){
        		indexPath =ResourceUtil.getSessionattachmenttitle("logger.indexPath");
        	}
        	int sercount=0;
        	if(StringUtil.isNotEmpty(ResourceUtil.getSessionattachmenttitle("logger.indexPath"))){
        		 sercount = Integer.parseInt(ResourceUtil.getSessionattachmenttitle("logger.sercount"));
        	}
            for(int i = 0; i < sercount; i++){
                String service = ResourceUtil.getSessionattachmenttitle("logger.service"+(i + 1));
                if(StringUtil.isNotEmpty(service)){
                	services.add(service);
                }
            }
            if(StringUtil.isNotEmpty(ResourceUtil.getSessionattachmenttitle("logger.multicastgroup"))){
            	multicastgroup = ResourceUtil.getSessionattachmenttitle("logger.multicastgroup");
            }
            if(StringUtil.isNotEmpty(ResourceUtil.getSessionattachmenttitle("logger.multicastport"))){
            	multicastport = Integer.parseInt(ResourceUtil.getSessionattachmenttitle("logger.multicastport"));
            }
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }    
}
