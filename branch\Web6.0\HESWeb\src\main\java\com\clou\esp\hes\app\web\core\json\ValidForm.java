package com.clou.esp.hes.app.web.core.json;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;


/**
 * $.ajax后需要接受的JSON
 * 
 * <AUTHOR> 
 */
public class ValidForm {

	private String status ="y";// 是否成功
	private String info = MutiLangUtil.doMutiLang("system.validSucc");//提示信息
	
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getInfo() {
		return info;
	}
	public void setInfo(String info) {
		this.info = info;
	}
	
}
