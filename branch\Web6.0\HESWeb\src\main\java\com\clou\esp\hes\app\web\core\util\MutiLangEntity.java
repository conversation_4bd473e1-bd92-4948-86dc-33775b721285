package com.clou.esp.hes.app.web.core.util;

import org.apache.commons.lang.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * 文件名：MutiLangEntity.java 版权：Copyright by Power7000g Team 描述： 修改人：严浪
 * 修改时间：2017年3月1日 跟踪单号： 修改单号： 修改内容：
 */
public class MutiLangEntity {
	/** 语言 */
	private java.lang.String langCode;
	/** 内容json */
	private JSONObject langJson;


	public MutiLangEntity() {
	}


	public java.lang.String getLangCode() {
		return langCode;
	}

	public JSONObject getLangJson() {
		return langJson;
	}

	/**
	 * 
	 * 添加语言及内容
	 * 
	 * @param langCode
	 *            String
	 * @param langJson
	 *            Object
	 * @see
	 */
	public void setLang(java.lang.String langCode, Object object) {
		this.langCode = langCode;
		this.langJson = JSON.parseObject(object.toString());
	}

	/**
	 * 
	 * Description: <br>
	 * 1、返回对应语言内容<br>
	 * 2、没有则返回传入的key<br>
	 * Implement: <br>
	 * 1、…<br>
	 * 2、…<br>
	 * 
	 * @param langCode
	 * @return
	 * @see
	 */
	public String getLang(java.lang.String key) {
		String lang = key;
		if (key == null) {
			return "";
		}
		if(!key.contains(".")) {
			return key;
		}
		String[] keys = key.split("\\.");
		JSONObject json = langJson;
		try {
			for (int i = 0; i < keys.length && json != null; i++) {
				if (i != keys.length - 1) {
					json = json.getJSONObject(keys[i]);
				} else {
					lang = json.getString(keys[i]);
				}
			}
			if (keys.length == 0 && json != null) {
				lang = json.getString(key);
			}
		} catch (Exception e) {
			return lang;
		}
		if (lang == null) {
			lang = key;
		}
		return lang;
	}

	/**
	 * 
	 * Description: <br>
	 * 1、根据两个key拼接…<br>
	 * 2、…<br>
	 * Implement: <br>
	 * 1、lanKey内容如:{0}删除成功…<br>
	 * 2、langArg内容如:用户 <br>
	 * 3,最终结果如: 用户删除成功
	 * 
	 * @param lanKey
	 * @param langArg
	 * @return
	 * @see
	 */
	public String getLang(String lanKey, String langArg) {
		String langContext;
		if (StringUtils.isEmpty(langArg)) {
			langContext = getLang(lanKey);
		} else {
			String[] argArray = langArg.split(",");
			langContext = getLang(lanKey);

			for (int i = 0; i < argArray.length; i++) {
				String langKeyArg = argArray[i].trim();
				String langKeyContext = getLang(langKeyArg);
				langContext = StringUtils.replace(langContext, "{" + i + "}",
						langKeyContext);
			}
		}
		return langContext;
	}

	public static void main(String[] args) {
		/*MutiLangEntity mle = MutiLangEntity.getMutiLangEntity();
		mle.setLang(
				"zh_CN",
				"{\"test\":{\"user\":\"林俊杰\",\"content\":\"{0}唱歌比{1}唱得好!\",\"tuser\":\"我\"},\"sss\":\"{0}你好帅\",\"user\":\"刘德华\"}");
		System.out.println(mle.getLang("test.content", "test.tuser,test.user"));*/
	}

}
