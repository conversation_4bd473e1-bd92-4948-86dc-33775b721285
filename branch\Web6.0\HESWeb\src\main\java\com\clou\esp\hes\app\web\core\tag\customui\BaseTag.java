package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.core.util.ResourceUtil;
import com.power7000g.core.util.oConvertUtils;

/**
 * 
 * <AUTHOR>
 * 
 */
public class BaseTag extends TagSupport {
	private static final long serialVersionUID = 1L;
	protected String type = "default";// 加载类型

	protected String cssTheme;

	public String getCssTheme() {
		return cssTheme;
	}

	public void setCssTheme(String cssTheme) {
		this.cssTheme = cssTheme;
	}

	public void setType(String type) {
		this.type = type;
	}

	public int doStartTag() throws JspException {
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspException {
		JspWriter out = null;
		StringBuffer sb = new StringBuffer();
		String types[] = type.split(",");
		try {
			out = this.pageContext.getOut();
			String webVer="ver="+ResourceUtil.getSessionattachmenttitle("hesweb.system.pageVer");
			ServletRequest request = this.pageContext.getRequest();
			String path = ((HttpServletRequest) request).getContextPath();
			String basePath = request.getScheme() + "://"
					+ request.getServerName() + ":" + request.getServerPort()
					+ path;
			//jquery min
			if (oConvertUtils.isIn("jquery", types)) {
				sb.append("<script type=\"text/javascript\" src=\"" + basePath+ "/plug-in/jquery/jquery.min.js?"+webVer+"\"></script>");
			}
			//通用
			sb.append("<script src=\"" + basePath+ "/theme/common/js/common.js?"+webVer+"\" type=\"text/javascript\" charset=\"utf-8\"></script>");
			//通用日期处理
			sb.append("<script src=\"" + basePath+ "/theme/common/js/data_util.js?"+webVer+"\" type=\"text/javascript\" charset=\"utf-8\"></script>");
			//校验
			if(oConvertUtils.isIn("validform", types)){
				sb.append("<link rel=\"stylesheet\" href=\""+ basePath+ "/plug-in/Validform/css/style.css?"+webVer+"\" type=\"text/css\" media=\"all\" />");
				if (MutiLangUtil.getLangCode().equals("en-us")) {
					sb.append("<script type=\"text/javascript\" src=\""
							+ basePath
							+ "/plug-in/Validform/js/Validform_v5.3.1_min_en.js?"+webVer+"\"></script>");
					sb.append("<script type=\"text/javascript\" src=\""
							+ basePath
							+ "/plug-in/Validform/js/Validform_Datatype_en.js?"+webVer+"\"></script>");
					sb.append("<script type=\"text/javascript\" src=\"" + basePath
							+ "/plug-in/Validform/js/datatype_en.js?"+webVer+"\"></script>");
				} else if (MutiLangUtil.getLangCode().equals("rf")) {
					// 法语待翻译
				} else {
					sb.append("<script type=\"text/javascript\" src=\""
							+ basePath
							+ "/plug-in/Validform/js/Validform_v5.3.1_min_zh-cn.js?"+webVer+"\"></script>");
					sb.append("<script type=\"text/javascript\" src=\""
							+ basePath
							+ "/plug-in/Validform/js/Validform_Datatype_zh-cn.js?"+webVer+"\"></script>");
					sb.append("<script type=\"text/javascript\" src=\"" + basePath
							+ "/plug-in/Validform/js/datatype_zh-cn.js?"+webVer+"\"></script>");
				}
			}
			//jquery 背景插件
			if (oConvertUtils.isIn("backstretch", types)) {
				sb.append("<script type=\"text/javascript\" src=\"" + basePath+ "/plug-in/backstretch/jquery.backstretch.min.js?"+webVer+"\"></script>");
			}
			//layer弹层组件
			if (oConvertUtils.isIn("layer", types)) {
				sb.append("<script src=\"" + basePath+ "/plug-in/layer/layer.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\""+ basePath+ "/plug-in/tools/customui/js/customcurd.js?"+webVer+"\"></script>");
			}
			//Bootstrap前端框架
			if (oConvertUtils.isIn("bootstrap", types)) {
			//	sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/bootstrap/css/bootstrap.min.css\" />");
				sb.append("<script type=\"text/javascript\" src=\""+ basePath+ "/plug-in/bootstrap/js/bootstrap.min.js?"+webVer+"\"></script>");
			}
			//form需要的
			if (oConvertUtils.isIn("form-css", types)) {
				sb.append("<link rel=\"stylesheet\" href=\""+ basePath+ "/plug-in/Validform/css/style.css?"+webVer+"\" type=\"text/css\" media=\"all\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/font-awesome/css/font-awesome.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/common.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/basic.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/simple-line-icons/simple-line-icons.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/bootstrap/css/hes_bootstrap.css?"+webVer+"\" />");
			}
			//jqgrid 分页
			if (oConvertUtils.isIn("jqgrid", types)) {
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/font-awesome/css/font-awesome.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/common.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/basic.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/simple-line-icons/simple-line-icons.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/bootstrap/css/hes_bootstrap.css?"+webVer+"\" />");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/jqgrid/ui.jqgridffe4.css?"+webVer+"\" />");
				sb.append("<script src=\"" + basePath+ "/plug-in/jqgrid/i18n/grid.locale-"+ MutiLangUtil.getLangCode() + ".js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\""+ basePath+ "/plug-in/jqgrid/jquery.jqGrid.minffe4.js?"+webVer+"\"></script>");
			}
			//tools 自定义弹出层
			if (oConvertUtils.isIn("tools", types)) {
				sb.append("<script type=\"text/javascript\" src=\""+ basePath+ "/plug-in/tools/customui/js/customcurd.js?"+webVer+"\"></script>");
			}
			if (oConvertUtils.isIn("ztree", types)) {
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/zTree/css/zTreeStyle.css?"+webVer+"\">");
				sb.append("<script src=\""+ basePath+ "/plug-in/zTree/js/jquery.ztree.all-3.5.min.js?"+webVer+"\"></script>");
			}
			//echarts 图形报表插件
			if(oConvertUtils.isIn("echarts", types)){
				sb.append("<script src=\""+ basePath+ "/plug-in/echarts/echarts.min.js?"+webVer+"\" type=\"text/javascript\" charset=\"utf-8\"></script>");
			}
			//日期输入插件
			if (oConvertUtils.isIn("my97DatePicker", types)) {
				sb.append("<script src=\""+ basePath+ "/plug-in/My97DatePicker/WdatePicker.js?"+webVer+"\"></script>");
			}
			
			//pushlet页面推送js
			if (oConvertUtils.isIn("pushlet", types)) {
				sb.append("<script src=\""+ basePath+ "/plug-in/pushlet/ajax-pushlet-client.js?"+webVer+"\"></script>");
				sb.append("<script src=\""+ basePath+ "/plug-in/pushlet/pushlet-data.js?"+webVer+"\"></script>");
			}
			//多选multiple-select
			if (oConvertUtils.isIn("multiselect", types)) {
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/bootstrap/bootstrap_multiselect/multiple-select.css?"+webVer+"\">");
				sb.append("<script src=\""+ basePath+ "/plug-in/bootstrap/bootstrap_multiselect/multiple-select.js?"+webVer+"\"></script>");
			}
			
			//i18n
			if (oConvertUtils.isIn("i18n", types)) {
				sb.append("<script type=\"text/javascript\" src=\"" + basePath+ "/plug-in/i18n/cookie.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath+ "/plug-in/i18n/i18next.min.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath+ "/plug-in/i18n/moment-with-locales.js?"+webVer+"\"></script>");
				sb.append("<script type=\"text/javascript\" src=\"" + basePath+ "/plug-in/i18n/translation.js?"+webVer+"\"></script>");
			}
			//select2
			if (oConvertUtils.isIn("select2", types)) {
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/select2/css/select2.css?"+webVer+"\">");
				sb.append("<script src=\""+ basePath+ "/plug-in/select2/js/select2.js?"+webVer+"\"></script>");
			}
			
			//select(支持多选)
			if (oConvertUtils.isIn("bootstrap-select", types)) {
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/plug-in/bootstrap/bootstrap_select/bootstrap-select.css?"+webVer+"\">");
				sb.append("<script src=\""+ basePath+ "/plug-in/bootstrap/bootstrap_select/bootstrap-select.js?"+webVer+"\"></script>");
			}
			
			// 移动端
			if (oConvertUtils.isIn("mobile", types)) {
				
				sb.append("<script src=\""+ basePath+ "/theme/js/mobile/Libs/jquery.min.js?"+webVer+"\"></script>");
				sb.append("<script src=\""+ basePath+ "/theme/js/mobile/Libs/jquery.json.js?"+webVer+"\"></script>");
				sb.append("<script src=\""+ basePath+ "/theme/js/mobile/Libs/swiper-3.3.1.jquery.min.js?"+webVer+"\"></script>");
				sb.append("<script src=\""+ basePath+ "/theme/js/mobile/common.js?"+webVer+"\"></script>");
				sb.append("<script src=\""+ basePath+ "/theme/js/mobile/yanzheng.js?"+webVer+"\"></script>");
				sb.append("<script src=\""+ basePath+ "/theme/js/mobile/echarts.min.js?"+webVer+"\"></script>");
				
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/mobile/reset.css?"+webVer+"\">");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/mobile/font.css?"+webVer+"\">");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/mobile/swiper-3.3.1.min.css?"+webVer+"\">");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/mobile/common.css?"+webVer+"\">");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/mobile/ModularForm.css?"+webVer+"\">");
				sb.append("<link rel=\"stylesheet\" type=\"text/css\" href=\""+ basePath+ "/theme/css/mobile/reset.css?"+webVer+"\">");
			}
			
			out.print(sb.toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (out != null) {
				try {

					out.clearBuffer();
					sb.setLength(0);

				} catch (Exception e) {
					e.printStackTrace();
				}
			}

		}
		return EVAL_PAGE;
	}

}
