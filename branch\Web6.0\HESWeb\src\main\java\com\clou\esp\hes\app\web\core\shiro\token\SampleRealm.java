package com.clou.esp.hes.app.web.core.shiro.token;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.enums.system.EnumDeleteFlag;
import com.clou.esp.hes.app.web.enums.system.EnumUserState;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.system.SysUtility;
import com.clou.esp.hes.app.web.service.system.SysMenuService;
import com.clou.esp.hes.app.web.service.system.SysRoleService;
import com.clou.esp.hes.app.web.service.system.SysUserService;
import com.clou.esp.hes.app.web.service.system.SysUtilityService;
import com.power7000g.core.util.encrypt.PasswordUtil;

/**
 * shiro 认证 + 授权 重写
 * 
 * <AUTHOR>
 * 
 */
public class SampleRealm extends AuthorizingRealm {
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysUtilityService sysUtilityService;
	@Autowired
	private SysRoleService sysRoleService;
	@Autowired
	private SysMenuService sysMenuService;

	public SampleRealm() {
		super();
	}

	/**
	 * 认证信息，主要针对用户登录，
	 */
	protected AuthenticationInfo doGetAuthenticationInfo(
			AuthenticationToken authcToken) throws AuthenticationException {
		ShiroToken token = (ShiroToken) authcToken;
		SysUser user = new SysUser();
		user.setUsername(token.getUsername());
		user.setDeleteFlag(Integer.parseInt(EnumDeleteFlag.NOT_DELETE.getIndex()));
		SysUser su = sysUserService.get(user);
		if (su == null) {
			throw new AccountException("The user account doesn’t exist!");
		}
		String pswd = token.getPswd();
		String password = su.getPassword();
		Date date = new Date();
		Timestamp time = new Timestamp(date.getTime());
	//	System.out.println(PasswordUtil.decrypt(password, token.getUsername(), su.getSignature()));
		System.out.println(PasswordUtil.encrypt(pswd, token.getUsername(),
				su.getSignature()));
		if (!password.equals(PasswordUtil.encrypt(pswd, token.getUsername(),
				su.getSignature()))) {// 密码错误要做业务处理
			throw new AccountException("User name or password is incorrect!");
		}
		Integer userState = su.getUserState();
		if (userState == EnumUserState.DISABLED.getIndex()) {
			throw new AccountException("The user account is disabled!");
		} else if (userState == EnumUserState.LOCKED.getIndex()) {
			throw new AccountException("The user account is locked!");
		} else if (userState == EnumUserState.USED.getIndex()) {

		} else {
			throw new AccountException("Data error!");
		}
		SysUtility st = sysUtilityService.getEntity(su.getUtilityId());
		if (st != null) {
			if (st.getState() != 1) {
				throw new AccountException("The utility account is disabled!");
			}
		}
		
		if(su.getLastLoginTime() == null){
			throw new AccountException(MutiLangUtil.doMutiLang("login.firstLoginRevisePwd"));
		}
		
		su.setLastLoginTime(time);
		
	
		
		sysUserService.update(su);
		user.setId(su.getId());
		return new SimpleAuthenticationInfo(su, pswd, getName());
	}

	/**
	 * 授权
	 */
	@Override
	protected AuthorizationInfo doGetAuthorizationInfo(
			PrincipalCollection principals) {
		SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
		SysUser su = TokenManager.getToken();
		Set<String> roles = new HashSet<String>();
		roles.add(su.getRoleId());
		info.setRoles(roles);
		Set<String> permissions = null;
		if(su.getUserType()==EnumUserType.SUPER_USER.getIndex()){
			permissions=sysMenuService.getPermissionByRoleId(null);
		}else{
			permissions=sysMenuService.getPermissionByRoleId(su.getRoleId());
		}
		info.setStringPermissions(permissions);
		return info;
	}

	/**
	 * 清空当前用户权限信息
	 */
	public void clearCachedAuthorizationInfo() {
		PrincipalCollection principalCollection = SecurityUtils.getSubject()
				.getPrincipals();
		SimplePrincipalCollection principals = new SimplePrincipalCollection(
				principalCollection, getName());
		super.clearCachedAuthorizationInfo(principals);
	}

	/**
	 * 指定principalCollection 清除
	 */
	public void clearCachedAuthorizationInfo(
			PrincipalCollection principalCollection) {
		SimplePrincipalCollection principals = new SimplePrincipalCollection(
				principalCollection, getName());
		super.clearCachedAuthorizationInfo(principals);
	}
}
