<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.DictDeviceModelMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.DictDeviceModel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="MANUFACTURER_ID" jdbcType="VARCHAR" property="manufacturerId" />
    <result column="PROTOCOL_ID" jdbcType="VARCHAR" property="protocolId" />
    <result column="INTRODUCTION" jdbcType="VARCHAR" property="introduction" />
    <result column="DEVICE_TYPE_ICON" jdbcType="VARCHAR" property="deviceTypeIcon" />
    <result column="DEVICE_TYPE" jdbcType="INTEGER" property="deviceType" />
    <result column="INIT_CREDIT_AMOUNT" jdbcType="INTEGER" property="initCreditAmount" />
  </resultMap>
</mapper>