/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEventParam{ } 
 * 
 * 摘    要： dictVeeEventParam
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-06 02:51:20
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.DictVeeEventParam;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventParamService;

/**
 * <AUTHOR>
 * @时间：2019-03-06 02:51:20
 * @描述：dictVeeEventParam类
 */
@Controller
@RequestMapping("/dictVeeEventParamController")
public class DictVeeEventParamController extends BaseController{

 	@Resource
    private DictVeeEventParamService dictVeeEventParamService;

	/**
	 * 跳转到dictVeeEventParam列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/dictVeeEventParamList");
    }

	/**
	 * 跳转到dictVeeEventParam新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictVeeEventParam")
	public ModelAndView dictVeeEventParam(DictVeeEventParam dictVeeEventParam,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictVeeEventParam.getId())){
			try {
                dictVeeEventParam=dictVeeEventParamService.getEntity(dictVeeEventParam.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictVeeEventParam", dictVeeEventParam);
		}
		return new ModelAndView("/vee/dictVeeEventParam");
	}


	/**
	 * dictVeeEventParam查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        JqGridResponseTo j=null;
        String eventId = request.getParameter("eventId");
        jqGridSearchTo.put("eventId", eventId);
        String editFlag = request.getParameter("editFlag");
        if(!StringUtils.isEmpty(editFlag) || "1".equals(editFlag)){
        	String ruleId = request.getParameter("ruleId");
        	jqGridSearchTo.put("ruleId", ruleId);
        	j=dictVeeEventParamService.getForJqGrid1(jqGridSearchTo);
        }else{
        	 try {
                 j=dictVeeEventParamService.getForJqGrid(jqGridSearchTo);
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
       
        return j;
    }
    
    /**
     * 删除dictVeeEventParam信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictVeeEventParam dictVeeEventParam,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictVeeEventParamService.deleteById(dictVeeEventParam.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictVeeEventParam信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictVeeEventParam dictVeeEventParam,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictVeeEventParam t=new  DictVeeEventParam();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictVeeEventParam.getId())){
        	t=dictVeeEventParamService.getEntity(dictVeeEventParam.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictVeeEventParam, t);
				dictVeeEventParamService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dictVeeEventParamService.save(dictVeeEventParam);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}