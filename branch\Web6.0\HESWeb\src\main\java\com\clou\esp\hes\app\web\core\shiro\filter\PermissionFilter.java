package com.clou.esp.hes.app.web.core.shiro.filter;

import java.io.PrintWriter;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.StringUtils;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.WebUtils;

import com.power7000g.core.util.json.AjaxJson;

/**
 * 权限校验 Filter
 * 
 * <AUTHOR>
 * 
 */
public class PermissionFilter extends AccessControlFilter {
	@Override
	protected boolean isAccessAllowed(ServletRequest request,
			ServletResponse response, Object mappedValue) throws Exception {
		// 先判断带参数的权限判断
		Subject subject = getSubject(request, response);
		if (null != mappedValue) {
			String[] arra = (String[]) mappedValue;
			for (String permission : arra) {
				if (subject.isPermitted(permission)) {
					return Boolean.TRUE;
				}
			}
		}
		HttpServletRequest httpRequest = ((HttpServletRequest) request);
		/**
		 * 此处是改版后，为了兼容项目不需要部署到root下，也可以正常运行，但是权限没设置目前必须到root 的URI， 原因：如果你把这个项目叫
		 * ShiroDemo，那么路径就是 /ShiroDemo/xxxx.shtml
		 * ，那另外一个人使用，又叫Shiro_Demo,那么就要这么控制/Shiro_Demo/xxxx.shtml 理解了吗？
		 * 所以这里替换了一下，使用根目录开始的URI
		 */
		String uri = httpRequest.getRequestURI();// 获取URI
		String basePath = httpRequest.getContextPath();// 获取basePath
		if (null != uri && uri.startsWith(basePath)) {
			uri = uri.replace(basePath, "");
		}
		if (subject.isPermitted(uri)) {
			return Boolean.TRUE;
		}
		if (ShiroFilterUtils.isAjax(request)) {
			AjaxJson json = new AjaxJson();
			json.setErrorMsg(" Please login!");
			ShiroFilterUtils.out(response, json);
		}
		return Boolean.FALSE;
	}

	@Override
	protected boolean onAccessDenied(ServletRequest request,
			ServletResponse response) throws Exception {

		Subject subject = getSubject(request, response);
		if (null == subject.getPrincipal()) {// 表示没有登录，重定向到登录页面
			saveRequest(request);
			// WebUtils.issueRedirect(request,
			// response,ShiroFilterUtils.LOGIN_URL);
			toUNAUTHORIZED(request, response,
					((HttpServletRequest) request).getContextPath()
							+ ShiroFilterUtils.LOGIN_URL);
		} else {
			if (StringUtils.hasText(ShiroFilterUtils.UNAUTHORIZED)) {// 如果有未授权页面跳转过去
				WebUtils.issueRedirect(request, response,
						ShiroFilterUtils.UNAUTHORIZED);
			} else {// 否则返回401未授权状态码
				WebUtils.toHttp(response).sendError(
						HttpServletResponse.SC_UNAUTHORIZED);
			}
		}
		return Boolean.FALSE;
	}

	private void toUNAUTHORIZED(ServletRequest request,
			ServletResponse response, String url) {
		PrintWriter out = null;
		try {
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.println("<script type=\"text/javascript\">if(window.parent.layer){window.parent.layer.confirm('Log in overtime, please login again!', {"
					  +" title:'Message', closeBtn: 0, btn: ['OK'],  icon: 0, skin: 'layer-ext-moon'}, function(){window.top.location.href=\""+ url + "\";});}else{window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";}</script>");
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.flush();
				out.close();
			}
		}
	}
}
