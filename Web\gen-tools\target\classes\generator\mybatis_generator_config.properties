#数据库连接
jdbc.driverClass = com.mysql.jdbc.Driver
jdbc.url =jdbc\:mysql\://192.168.0.247\:6100/cms?useUnicode\=true&characterEncoding\=utf8
jdbc.user=root
jdbc.password=123

#c3p0
jdbc.maxPoolSize=50
jdbc.minPoolSize=10
jdbc.maxStatements=100
jdbc.testConnection=true

# 通用mapper
mapper.plugin = tk.mybatis.mapper.generator.MapperPlugin
mapper.Mapper = tk.mybatis.mapper.common.Mapper

targetResourcesProject  = D:/\workspacetwo/\ppm-trans6.0/\src/\main
targetJavaProject  = D:/\workspacetwo/\ppm-trans6.0/\src/\main/\java
targetModelPackage = com.clou.ppm.transfer.model.ghana
targetXMLPackage = resources.mapper
targetMapperPackage = com.clou.ppm.transfer.mapper

tableName = ppm_customer_tariff_map
domainObjectName = PpmCustomerTariffMap