/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEventParam{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-06 02:51:20
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.vee;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.vee.DictVeeEventDataitem;
import com.clou.esp.hes.app.web.model.vee.DictVeeEventParam;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DictVeeEventParamDao extends CrudDao<DictVeeEventParam>{
	public List<DictVeeEventParam>  getForJqGrid1(JqGridSearchTo jqGridSearchTo);

}