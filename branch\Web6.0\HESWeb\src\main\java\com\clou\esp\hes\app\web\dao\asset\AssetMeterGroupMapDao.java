/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetMeterGroupMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2018-01-25 07:22:09
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetMeterGroupMap;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface AssetMeterGroupMapDao extends CrudDao<AssetMeterGroupMap>{
	
	public int copyReferenceUpdate(AssetMeterGroupMap assMeterGroupMap);

	/**
	 * 电表组升级，根据查询条件获取电表List(jqgrid)
	 * @param JqGridSearchTo
	 * @return String
	 */
	List<AssetMeterGroupMap> getPlanMeterList_MGU_jqgrid(JqGridSearchTo jqGridSearchTo);
	/**
	 * 电表组升级，根据查询条件获取电表List(实体)
	 * @param JqGridSearchTo
	 * @return String
	 */
	List<AssetMeterGroupMap> getPlanMeterList_MGU_pojo(AssetMeterGroupMap assMeterGroupMap);
	
	/**
	 * meter group upgrade根据查询条件获取job list信息
	 * @param JqGridSearchTo
	 * @return String
	 */
	List<AssetMeterGroupMap> getPlanJobList_MGU(JqGridSearchTo jqGridSearchTo);
	
	//Meter Group Upgrade tab1 导出meter list / job list
	List<AssetMeterGroupMap> getPlanMeterList_export(AssetMeterGroupMap assMeterGroupMap);
	List<AssetMeterGroupMap> getPlanJobList_export(AssetMeterGroupMap assMeterGroupMap);
	
	public void deleteByMeterAndType(@Param(value="id")String meterId,@Param(value="type")String type);
	
	public long countMeterByGroupId(String meterGroupId);
	
	public int batchInsert(List<AssetMeterGroupMap> mList);
}