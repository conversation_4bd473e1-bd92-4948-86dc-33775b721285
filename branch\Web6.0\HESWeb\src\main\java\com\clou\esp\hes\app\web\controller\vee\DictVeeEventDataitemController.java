/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictVeeEventDataitem{ } 
 * 
 * 摘    要： dictVeeEventDataitem
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2019-03-04 09:38:47
 * 最后修改时间：
 * 
*********************************************************************************************************/
package com.clou.esp.hes.app.web.controller.vee;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.controller.core.BaseController;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.validation.ValidGroup1;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.bean.MyBeanUtils;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.model.vee.AssetVeeRule;
import com.clou.esp.hes.app.web.model.vee.DictVeeEvent;
import com.clou.esp.hes.app.web.model.vee.DictVeeEventDataitem;
import com.clou.esp.hes.app.web.service.vee.AssetVeeRuleService;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventDataitemService;
import com.clou.esp.hes.app.web.service.vee.DictVeeEventService;

/**
 * <AUTHOR>
 * @时间：2019-03-04 09:38:47
 * @描述：dictVeeEventDataitem类
 */
@Controller
@RequestMapping("/dictVeeEventDataitemController")
public class DictVeeEventDataitemController extends BaseController{

 	@Resource
    private DictVeeEventDataitemService dictVeeEventDataitemService;
 	@Resource
    private DictVeeEventService dictVeeEventService;
 	@Resource
    private AssetVeeRuleService assetVeeRuleService;
	/**
	 * 跳转到dictVeeEventDataitem列表页面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "list")
    public ModelAndView list(HttpServletRequest request, Model model) {
        return new ModelAndView("/vee/dictVeeEventDataitemList");
    }

	/**
	 * 跳转到dictVeeEventDataitem新增界面
	 * 
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value  = "dictVeeEventDataitem")
	public ModelAndView dictVeeEventDataitem(DictVeeEventDataitem dictVeeEventDataitem,HttpServletRequest request, Model model) {
		if(StringUtil.isNotEmpty(dictVeeEventDataitem.getId())){
			try {
                dictVeeEventDataitem=dictVeeEventDataitemService.getEntity(dictVeeEventDataitem.getId());
            }
            catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
			model.addAttribute("dictVeeEventDataitem", dictVeeEventDataitem);
		}
		return new ModelAndView("/vee/dictVeeEventDataitem");
	}


	/**
	 * dictVeeEventDataitem查询分页
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "datagrid")
    @ResponseBody
    public JqGridResponseTo datagrid(JqGridSearchTo jqGridSearchTo,HttpServletRequest request) {
        TagUtil.setFieldValue(jqGridSearchTo, request); //取传递的参数到JqGridSearchTo
        String eventId = request.getParameter("eventId");
        
        jqGridSearchTo.put("eventId", eventId);
        JqGridResponseTo j=null;
        String editFlag = request.getParameter("editFlag");
        if(!StringUtils.isEmpty(editFlag) || "1".equals(editFlag)){
        	String ruleId = request.getParameter("ruleId");
        	jqGridSearchTo.put("ruleId", ruleId);
        	j=dictVeeEventDataitemService.getForJqGrid1(jqGridSearchTo);
        }else{

	        try {
	             j=dictVeeEventDataitemService.getForJqGrid(jqGridSearchTo);
	        }
	        catch (Exception e) {
	            e.printStackTrace();
	        }
    	
        }
        return j;
    }
	
	/**
	 * 精确查找设备
	 * @param deviceSn
	 * @param deviceType
	 * @param request
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "getEventDesc")
	@ResponseBody
	public AjaxJson getAssetDevice(String eventId,
			HttpServletRequest request, Model model) {
		AjaxJson j = new AjaxJson();
		DictVeeEvent dictVeeEvent = dictVeeEventService.getEntity(eventId);
		j.setObj(dictVeeEvent.getDescr());
		return j;
	}
	
    
    /**
     * 删除dictVeeEventDataitem信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "del")
    @ResponseBody
    public AjaxJson del(DictVeeEventDataitem dictVeeEventDataitem,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        try {
            SysUser su=TokenManager.getToken();
            if(dictVeeEventDataitemService.deleteById(dictVeeEventDataitem.getId())>0){
                j.setMsg("Successfully deleted");
            }else{
                j.setSuccess(false);
                j.setMsg("Failed to delete");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
    
    /**
     * 保存dictVeeEventDataitem信息
     * 
     * @param id
     * @return
     */
    @RequestMapping(value = "save")
    @ResponseBody
    public AjaxJson save(@Validated(value = { ValidGroup1.class })DictVeeEventDataitem dictVeeEventDataitem,BindingResult bindingResult,HttpServletRequest request) {
        AjaxJson j=new AjaxJson();
        DictVeeEventDataitem t=new  DictVeeEventDataitem();
        try {
       SysUser su=TokenManager.getToken();
        if(StringUtil.isNotEmpty(dictVeeEventDataitem.getId())){
        	t=dictVeeEventDataitemService.getEntity(dictVeeEventDataitem.getId());
			MyBeanUtils.copyBeanNotNull2Bean(dictVeeEventDataitem, t);
				dictVeeEventDataitemService.update(t);
				j.setMsg("Successfully modified");
				
			}else{
	            dictVeeEventDataitemService.save(dictVeeEventDataitem);
	            j.setMsg("Added successfully");
	          
			}
        }catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
             j.setMsg("Abnormal operation");
        }
        return j;
    }
	
	
	
}