package com.clou.esp.hes.app.web.core.tag.customui.util;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.clou.esp.hes.app.web.core.tag.vo.ExpUtl;
import com.clou.esp.hes.app.web.core.tag.vo.FindListExp;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.service.common.CommonService;
import com.power7000g.core.util.ReflectHelper;
import com.power7000g.core.util.oConvertUtils;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;
import com.power7000g.core.util.json.AjaxJson;

/**
 * 类描述：标签工具类
 * 
 * @author: 刘志超
 */
public class TagUtil<T extends Serializable> {

	/**
	 * 控件类型：easyui 返回datagrid JSON数据
	 * 
	 * @param response
	 * @param dataGrid
	 */
	public static void datagrid(HttpServletResponse response, String json) {
		response.setContentType("application/json");
		response.setHeader("Cache-Control", "no-store");
		PrintWriter pw = null;
		try {
			pw = response.getWriter();
			pw.write(json);
			pw.flush();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				pw.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * <b>Summary: </b> getFiled(获得实体Bean中所有属性)
	 * 
	 * @param objClass
	 * @return
	 * @throws ClassNotFoundException
	 */
	public static Field[] getFiled(Class<?> objClass)
			throws ClassNotFoundException {
		Field[] field = null;
		if (objClass != null) {
			Class<?> class1 = Class.forName(objClass.getName());
			field = class1.getDeclaredFields();// 这里便是获得实体Bean中所有属性的方法
			return field;
		} else {
			return field;
		}
	}

	/**
	 * 
	 * 获取对象内对应字段的值
	 * 
	 * @param fields
	 */
	public static Object fieldNametoValues(String FiledName, Object o) {
		Object value = "";
		String fieldName = "";
		String childFieldName = null;
		ReflectHelper reflectHelper = new ReflectHelper(o);
		if (FiledName.indexOf("_") == -1) {
			if (FiledName.indexOf(".") == -1) {
				fieldName = FiledName;
			} else {
				fieldName = FiledName.substring(0, FiledName.indexOf("."));// 外键字段引用名
				childFieldName = FiledName
						.substring(FiledName.indexOf(".") + 1);// 外键字段名
			}
		} else {
			fieldName = FiledName.substring(0, FiledName.indexOf("_"));// 外键字段引用名
			childFieldName = FiledName.substring(FiledName.indexOf("_") + 1);// 外键字段名
		}
		if (o instanceof Map) {
			value = ((Map) o).get(fieldName);
		} else {

			value = reflectHelper.getMethodValue(fieldName);
			if (value == null)
				value = "";
		}
		if (value != ""
				&& value != null
				&& (FiledName.indexOf("_") != -1 || FiledName.indexOf(".") != -1)) {

			if (value instanceof List) {
				Object tempValue = "";
				for (Object listValue : (List) value) {
					tempValue = tempValue.toString()
							+ fieldNametoValues(childFieldName, listValue)
							+ ",";
				}
				value = tempValue;
			} else {
				value = fieldNametoValues(childFieldName, value);
			}

		}
		if (value != "" && value != null && value instanceof java.util.Date) {
			value = DateUtils.date2Str((java.util.Date) value,
					DateUtils.datetimeFormat);
		}
		if (value != "" && value != null && value instanceof String) {

			value = converunicode(value.toString());
		}
		return value;
	}

	/**
	 * 给指定对象属性赋值
	 * 
	 * @param FiledName
	 *            字段名称
	 * @param o
	 *            对象
	 * @param value
	 *            值
	 * @return
	 */
	public static boolean setFieldNametoValues(String FiledName, Object o,
			Object value) {
		boolean setTf = true;
		ReflectHelper reflectHelper = new ReflectHelper(o);
		if (o instanceof Map) {
			((Map) o).put(FiledName, value);
		} else {
			setTf = reflectHelper.setMethodValue(FiledName, value);
		}

		return setTf;
	}

	static Object converunicode(String jsonValue) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < jsonValue.length(); i++) {
			char c = jsonValue.charAt(i);
			switch (c) {

			// case '\"':
			// sb.append("\\\"");
			// break;
			case '\'':
				sb.append("\\\'");
				break;
			case '\\':
				sb.append("\\\\");
				break;
			// case '/':
			// sb.append("\\/");
			// break;

			case '\b':
				sb.append("\\b");
				break;
			case '\f':
				sb.append("\\f");
				break;
			case '\n':
				sb.append("\\n");
				break;
			case '\r':
				sb.append("\\r");
				break;
			case '\t':
				sb.append("\\t");
				break;
			default:
				sb.append(c);
			}
		}
		return sb.toString();
	}

	/**
	 * 对象转数组
	 * 
	 * @param fields
	 * @param o
	 * @param stack
	 * @return
	 * @throws Exception
	 */
	protected static Object[] field2Values(String[] fields, Object o)
			throws Exception {
		Object[] values = new Object[fields.length];
		for (int i = 0; i < fields.length; i++) {
			String fieldName = fields[i].toString();
			values[i] = fieldNametoValues(fieldName, o);
		}
		return values;
	}

	/**
	 * 循环LIST对象拼接EASYUI格式的JSON数据
	 * 
	 * @param fields
	 * @param total
	 * @param list
	 */
	private static String listtojson(String[] fields, int total, List<?> list,
			String[] footers) throws Exception {
		// Object[] values = new Object[fields.length];
		StringBuffer jsonTemp = new StringBuffer();
		jsonTemp.append("{\"total\":" + total + ",\"rows\":[");
		int i;
		String fieldName;
		for (int j = 0; j < list.size(); ++j) {
			jsonTemp.append("{\"state\":\"closed\",");
			Object fieldValue = null;
			for (i = 0; i < fields.length; ++i) {
				fieldName = fields[i].toString();
				if (list.get(j) instanceof Map)
					fieldValue = ((Map<?, ?>) list.get(j)).get(fieldName);
				else {
					fieldValue = fieldNametoValues(fieldName, list.get(j));
				}
				jsonTemp.append("\"" + fieldName + "\"" + ":\""
						+ String.valueOf(fieldValue).replace("\"", "\\\"")
						+ "\"");
				if (i != fields.length - 1) {
					jsonTemp.append(",");
				}
			}
			if (j != list.size() - 1)
				jsonTemp.append("},");
			else {
				jsonTemp.append("}");
			}
		}
		jsonTemp.append("]");
		if (footers != null && footers.length > 0) {
			jsonTemp.append(",");
			jsonTemp.append("\"footer\":[");
			jsonTemp.append("{");
			// jsonTemp.append("\"name\":\"合计\",");
			for (i = 0; i < footers.length; i++) {
				String footerFiled = footers[i].split(":")[0];
				Object value = null;
				if (footers[i].split(":").length == 2)
					value = footers[i].split(":")[1];
				else {
					value = getTotalValue(footerFiled, list);
				}
				if (i == 0) {
					jsonTemp.append("\"" + footerFiled + "\":\"" + value + "\"");
				} else {
					jsonTemp.append(",\"" + footerFiled + "\":\"" + value
							+ "\"");
				}
			}
			jsonTemp.append("}");
			jsonTemp.append("]");
		}
		jsonTemp.append("}");
		return jsonTemp.toString();
	}

	/**
	 * 计算指定列的合计
	 * 
	 * @param filed
	 *            字段名
	 * @param list
	 *            列表数据
	 * @return
	 */
	private static Object getTotalValue(String fieldName, List list) {
		Double sum = 0D;
		try {
			for (int j = 0; j < list.size(); j++) {
				Double v = 0d;
				String vstr = String.valueOf(fieldNametoValues(fieldName,
						list.get(j)));
				if (!StringUtil.isEmpty(vstr)) {
					v = Double.valueOf(vstr);
				} else {

				}
				sum += v;
			}
		} catch (Exception e) {
			return "";
		}
		return sum;
	}

	/**
	 * 循环LIST对象拼接DATATABLE格式的JSON数据
	 * 
	 * @param fields
	 * @param total
	 * @param list
	 */
	public static String datatable(String field, JqGridResponseTo jgridRdata)
			throws Exception {
		String[] fields = field.split(",");
		Object[] values = new Object[fields.length];
		StringBuffer jsonTemp = new StringBuffer();
		jsonTemp.append("{\"total\":" + jgridRdata.getTotal() + ",\"records\":"
				+ jgridRdata.getRecords() + ",\"page\":" + jgridRdata.getPage()
				+ ",\"rows\":[");
		for (int j = 0; j < jgridRdata.getRows().size(); j++) {
			jsonTemp.append("{");
			for (int i = 0; i < fields.length; i++) {
				String fieldName = fields[i].toString();
				values[i] = fieldNametoValues(fieldName, jgridRdata.getRows()
						.get(j));
				jsonTemp.append("\"" + fieldName + "\"" + ":\"" + values[i]
						+ "\"");
				if (i != fields.length - 1) {
					jsonTemp.append(",");
				}
			}
			if (j != jgridRdata.getRows().size() - 1) {
				jsonTemp.append("},");
			} else {
				jsonTemp.append("}");
			}
		}
		jsonTemp.append("]}");
		return jsonTemp.toString();
	}

	/**
	 * 实体列表取指定字段列表,并且去掉多级
	 * 
	 * @param field
	 * @param jgridRdata
	 * @return
	 * @see
	 */
	public static List getListSpecifyFile(String field, List rows) {
		List list = new ArrayList<>();
		String[] fields = field.split(",");
		Object[] values = new Object[fields.length];
		for (int j = 0; j < rows.size(); j++) {
			Map<String, Object> m = new HashMap<String, Object>();
			for (int i = 0; i < fields.length; i++) {
				String fieldName = fields[i].toString();
				values[i] = fieldNametoValues(fieldName, rows.get(j));
				m.put(fieldName, values[i]);
			}
			list.add(m);
		}
		return list;
	}

	/**
	 * 
	 * 根据字段名称取传递的参数值,到JqGridSearchTo json里面
	 * 
	 * @param JqGridSearchTo
	 * @param request
	 * @see
	 */
	public static void setFieldValue(JqGridSearchTo jgt,
			HttpServletRequest request) {
		String orgId = (String) request
				.getAttribute(Globals.REQUESST_DATA_AUTH_ORG_ID);
		jgt.getMap().put("orgid", orgId);
		String ventorId = (String) request
				.getAttribute(Globals.REQUESST_DATA_AUTH_VENDOR_ID);
		jgt.getMap().put("vendorId", ventorId);
		jgt.getMap().put("orgId", orgId);
		if (StringUtil.isNotEmpty(jgt.getSearchField())
				&& StringUtil.isNotEmpty(jgt.getSearchString())) {
			String[] fields = jgt.getSearchField().split(",");
			String[] queryMode = jgt.getSearchString().split(",");
			if (fields != null && queryMode != null
					&& fields.length == queryMode.length) {
				for (int i = 0; i < fields.length; i++) {
					if ("group".equals(queryMode[i])) {
						Object startValue = request.getParameter(fields[i]
								+ "_start");
						if (StringUtil.isNotEmpty(startValue)) {
							jgt.put(fields[i] + "_start", startValue);
						}
						Object endValue = request.getParameter(fields[i]
								+ "_end");
						if (StringUtil.isNotEmpty(endValue)) {
							jgt.put(fields[i] + "_end", endValue);
						}
					} else {
						Object v = request.getParameter(fields[i]);
						if (StringUtil.isNotEmpty(v)) {
							jgt.put(fields[i], v);
						}
					}
				}
			}
		}
	}

	/**
	 * 获取指定字段类型 <b>Summary: </b> getColumnType(请用一句话描述这个方法的作用)
	 * 
	 * @param fileName
	 * @param fields
	 * @return
	 */
	public static String getColumnType(String fileName, Field[] fields) {
		String type = "";
		if (fields.length > 0) {
			for (int i = 0; i < fields.length; i++) {
				String name = fields[i].getName(); // 获取属性的名字
				String filedType = fields[i].getGenericType().toString(); // 获取属性的类型
				if (fileName.equals(name)) {
					if (filedType.equals("class java.lang.Integer")) {
						filedType = "int";
						type = filedType;
					} else if (filedType.equals("class java.lang.Short")) {
						filedType = "short";
						type = filedType;
					} else if (filedType.equals("class java.lang.Double")) {
						filedType = "double";
						type = filedType;
					} else if (filedType.equals("class java.util.Date")) {
						filedType = "date";
						type = filedType;
					} else if (filedType.equals("class java.lang.String")) {
						filedType = "string";
						type = filedType;
					} else if (filedType.equals("class java.sql.Timestamp")) {
						filedType = "Timestamp";
						type = filedType;
					} else if (filedType.equals("class java.lang.Character")) {
						filedType = "character";
						type = filedType;
					} else if (filedType.equals("class java.lang.Boolean")) {
						filedType = "boolean";
						type = filedType;
					} else if (filedType.equals("class java.lang.Long")) {
						filedType = "long";
						type = filedType;
					}

				}
			}
		}
		return type;
	}

	/**
	 * 
	 * <b>Summary: </b> getSortColumnIndex(获取指定字段索引)
	 * 
	 * @param fileName
	 * @param fieldString
	 * @return
	 */
	protected static String getSortColumnIndex(String fileName,
			String[] fieldString) {
		String index = "";
		if (fieldString.length > 0) {
			for (int i = 0; i < fieldString.length; i++) {
				if (fileName.equals(fieldString[i])) {
					int j = i + 1;
					index = oConvertUtils.getString(j);
				}
			}
		}
		return index;

	}

	/**
	 * 获取自定义函数名
	 * 
	 * @param functionname
	 * @return
	 */
	public static String getFunction(String functionname) {
		int index = functionname.indexOf("(");
		if (index == -1) {
			return functionname;
		} else {
			return functionname.substring(0, functionname.indexOf("("));
		}
	}

	/**
	 * 获取自定义函数的参数
	 * 
	 * @param functionname
	 * @return
	 */
	public static String getFunParams(String functionname) {
		int index = functionname.indexOf("(");
		String param = "";
		if (index != -1) {
			String testparam = functionname.substring(
					functionname.indexOf("(") + 1, functionname.length() - 1);
			if (StringUtil.isNotEmpty(testparam)) {
				String[] params = testparam.split(",");
				for (String string : params) {
					param += (string.indexOf("{") != -1) ? ("'\"+"
							+ string.substring(1, string.length() - 1) + "+\"',")
							: ("'\"+rec." + string + "+\"',");
				}
			}
		}
		param += "'\"+index+\"'";// 传出行索引号参数
		return param;
	}

	public static String getJson(List fields, List datas) {
		if (datas != null && datas.size() > 0) {
			StringBuffer sb = new StringBuffer();
			sb.append("{\"total\":\"" + datas.size() + "\",\"rows\":[");
			for (int i = 0; i < datas.size(); i++) {
				Object[] values = (Object[]) datas.get(i);
				sb.append("{");
				for (int j = 0; j < values.length; j++) {
					sb.append("\"" + fields.get(j) + "\":\""
							+ (values[j] == null ? "" : values[j]) + "\""
							+ (j == values.length - 1 ? "" : ","));
				}
				sb.append("}" + (i == datas.size() - 1 ? "" : ","));
			}
			sb.append("]}");

			return sb.toString();
		} else {
			return "{\"total\":\"0\",\"rows\":[]}";
		}
	}

	public static String getJsonByMap(List fields,
			List<Map<String, Object>> datas) {
		if (datas != null && datas.size() > 0) {
			StringBuffer sb = new StringBuffer();
			sb.append("{\"total\":\"" + datas.size() + "\",\"rows\":[");
			for (int i = 0; i < datas.size(); i++) {
				Map<String, Object> values = (Map<String, Object>) datas.get(i);
				sb.append("{");
				// for(int j=0;j<values.size();){
				int j = 0;
				for (Object value : values.values()) {
					sb.append("\"" + fields.get(j) + "\":\""
							+ (value == null ? "" : value) + "\""
							+ (j == values.size() - 1 ? "" : ","));
					j++;
				}
				// sb.append("\""+fields.get(j)+"\":\""+(values.get(j)==null?"":values.get(j))+"\""+(j==values.size()-1?"":","));
				// }

				sb.append("}" + (i == datas.size() - 1 ? "" : ","));
			}

			sb.append("]}");

			return sb.toString();
		} else {
			return "{\"total\":\"0\",\"rows\":[]}";
		}
	}

	public static ExpUtl getFindListExp(String exp) {
		ExpUtl eu = new ExpUtl();
		List<FindListExp> list = new ArrayList<FindListExp>();
		List<String> jud = new ArrayList<String>();
		String[] expStrs = exp.split("[&|]");
		for (String expStr : expStrs) {
			String[] v = expStr.split("[#]");
			if (v.length >= 3) {
				FindListExp e = new FindListExp(v[0], v[1], v[2]);
				list.add(e);
			}
		}
		eu.setExpList(list);
		int i = exp.indexOf("&");
		int j = exp.indexOf("|");
		while (i != -1 || j != -1) {
			if (i >= 0 && i < j) {
				jud.add("&&");
				i = exp.indexOf("&", i + 1);
			} else if (j >= 0 && j < i) {
				jud.add("||");
				j = exp.indexOf("|", j + 1);
			} else if (i > 0) {
				jud.add("&&");
				i = exp.indexOf("&", i + 1);
			} else if (j > 0) {
				jud.add("||");
				j = exp.indexOf("|", j + 1);
			}

		}
		eu.setJud(jud);
		return eu;
	}

	/**
	 * 判断指定字段是否唯一
	 * 
	 * @param objClass
	 * @param verify
	 * @param service
	 * @return
	 */
	public static AjaxJson VerificationIsUnique(String id, Serializable obj,
			Map<String, Object> verify, CommonService service) {
		AjaxJson j = new AjaxJson();
		try {
			Set keySet = verify.keySet();
			for (Object keyName : keySet) {
				setFieldNametoValues("" + keyName, obj, verify.get(keyName));
			}
			List<Object> list = service.getList(obj);
			if (StringUtil.isNotEmpty(id)) {
				for (Object o : list) {
					String tId = (String) fieldNametoValues("id", o);
					if (!id.equals(tId)) {
						j.setSuccess(false);
						break;
					}
				}
			} else {
				if (list.size() > 0) {
					j.setSuccess(false);
				}
			}
		} catch (Exception e) {
			j.setSuccess(false);
		}
		return j;
	}
}
