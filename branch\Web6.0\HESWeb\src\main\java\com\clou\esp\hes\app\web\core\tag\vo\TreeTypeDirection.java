package com.clou.esp.hes.app.web.core.tag.vo;

/**
 * 文件名：TreeTypeDirection.java
 * 版权：Copyright by Power7000g Team
 * 描述：树形框类型
 * 修改人：严浪
 * 修改时间：2017年3月27日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public enum TreeTypeDirection {
    Default,Radio,Check;
    
    public static TreeTypeDirection getTreeType(String type){
    	if(type.equals("Radio")){
    		return TreeTypeDirection.Radio;
    	}else if(type.equals("Check")){
    		return TreeTypeDirection.Check;
    	}else{
    		return TreeTypeDirection.Default;
    	}
    }
}
