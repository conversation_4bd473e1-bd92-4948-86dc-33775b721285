/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class SysMenu{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-10-27 08:02:17
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.system;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.dict.DictMenu;
import com.clou.esp.hes.app.web.model.system.SysRole;
import com.clou.esp.hes.app.web.model.system.SysRoleMenu;

@MyBatisDao
public interface SysMenuDao extends CrudDao<DictMenu>{

	/**
	 * 根据用户编号获取用户所有菜单权限
	 * @param id
	 * @param string
	 * @return
	 */
	List<DictMenu> getMenusByUserId(@Param(value="userId")String userId,@Param(value="parentmenuid") String parentmenuid);

	/**
	 * 根据role id获取所有菜单
	 * @param id
	 * @return
	 */
	public List<DictMenu> getMenuListByRole(SysRole entity);
}