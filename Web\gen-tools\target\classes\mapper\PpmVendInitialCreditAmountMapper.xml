<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.PpmVendInitialCreditAmountMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.PpmVendInitialCreditAmount">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="METER_ID" jdbcType="VARCHAR" property="meterId" />
    <result column="INIT_CREDIT_AMOUNT" jdbcType="INTEGER" property="initCreditAmount" />
    <result column="PAYMENT_TIME" jdbcType="DATE" property="paymentTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="SALES_ID" jdbcType="VARCHAR" property="salesId" />
    <result column="CREATE_DATE" jdbcType="DATE" property="createDate" />
    <result column="README" jdbcType="VARCHAR" property="readme" />
  </resultMap>
</mapper>