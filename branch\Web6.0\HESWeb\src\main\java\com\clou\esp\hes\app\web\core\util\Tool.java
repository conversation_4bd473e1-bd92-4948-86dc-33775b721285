package com.clou.esp.hes.app.web.core.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import ch.iec.tc57._2011.meterdefineconfig_.Arrays;
import ch.iec.tc57._2011.meterdefineconfig_.Structures;
import ch.iec.tc57._2011.meterdefineconfig_.Values;
import clouesp.hes.core.uci.soap.custom.webservice.common.BaudRateEnum;
import clouesp.hes.core.uci.soap.custom.webservice.common.BaudType;
import clouesp.hes.core.uci.soap.custom.webservice.common.ComTypeEnum;
import clouesp.hes.core.uci.soap.custom.webservice.common.DcuDlmsIdEnum;
import clouesp.hes.core.uci.soap.custom.webservice.common.VipEnum;
import clouesp.hes.core.uci.soap.custom.webservice.common.WiringMethodEnum;

import com.clou.esp.hes.app.web.model.asset.DcuConfiguration;


/**
 * @ClassName: Tool
 * @Description: 辅助工具类
 * <AUTHOR>
 * @date 2018年7月11日 下午5:43:42
 *
 */
public enum Tool {
	
	Instance;
	
	/**
	 * 
	 * @Title: convertMeterUploadSet
	 * @Description: 转换参数
	 * @param params 参数
	 * @return
	 * @return List<DcuConfiguration>
	 * @throws
	 */
	public List<DcuConfiguration> convertMeterUploadSet(String[] params){
		if(null == params){
			return null;
		}
		
		List<DcuConfiguration> dcuConfigurations = new ArrayList<DcuConfiguration>();
		for (String string : params) {
			DcuConfiguration dcuConfig = new DcuConfiguration();
			String[] values = string.split(",");
			dcuConfig.setId(values[0]);
			dcuConfig.setMeterSn(values[1]);
			dcuConfig.setPointNum(Integer.parseInt(values[2]));
			dcuConfig.setLogicalName(values[3]);
			dcuConfig.setBaudRate(Integer.parseInt(values[4]));
			dcuConfig.setComNum(Integer.parseInt(values[5]));
			dcuConfig.setProtocol(values[6]);
			dcuConfig.setCommunicatorAddress(values[7]);

			dcuConfigurations.add(dcuConfig);
		}

		return dcuConfigurations;
	}
	
	
	/**
	 * 
	 * @Title: createMeterUploadSetIds
	 * @Description: 获取电表参数设置 id
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public List<String> createMeterUploadSetIds(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> ids = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			ids.add(values[0]);
		}

		return ids;
	}
	
	/**
	 * 
	 * @Title: createMeterUploadSetPns
	 * @Description: TODO
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public List<String> createMeterUploadSetPns(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> pns = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			pns.add(values[2]);
		}

		return pns;
	}
	
	
	/**
	 * 
	 * @Title: getMeterUploadSetPns300
	 * @Description: TODO
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public List<String> getMeterUploadSetPns300(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> pns = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			pns.add(values[0]);
		}

		return pns;
	}
	
	
	/**
	 * 
	 * @Title: createMeterUploadSetPns 300
	 * @Description: TODO
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public List<String> createMeterUploadSetPns300(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> pns = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			pns.add(values[2]);
		}

		return pns;
	}
	
	public List<String> createMeterUploadSetSnsDlms(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> pns = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			pns.add(values[1]);
		}

		return pns;
	}
	
	public List<String> createMeterUploadSetLogicDlms(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> pns = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			pns.add(values[1]);
		}

		return pns;
	}
	
	/**
	 * 
	 * @Title: createMeterUploadSetMeters
	 * @Description: 获取电表 sn
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public List<String> createMeterUploadSetMeters(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> meters = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			meters.add(values[1]);
		}

		return meters;
	}
	
	/**
	 * 
	 * @Title: createMeterUploadSetMeters
	 * @Description: 获取电表 LOGIC NAME
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public List<String> createMeterUploadSetMetersDlms(String[] params) {
		if(null == params){
			return null;
		}
		
		List<String> meters = new ArrayList<String>();
		for (String string : params) {
			String[] values = string.split(",");
			meters.add(values[1]);
		}

		return meters;
	}
	
	
	/**
	 * 
	 * @Title: createMeterUploadSetMeters
	 * @Description: 获取电表 LOGIC NAME
	 * @param params
	 * @return
	 * @return List<String>
	 * @throws
	 */
	public Arrays createEventLogDlms(String[] params) {
		Arrays arrays = new Arrays();
		Set<String> hashSet = new HashSet<String>();
		
		
		if(params != null){
			for (String string : params) {
				hashSet.add(string);
			}
			
			Structures structure = new Structures();
		
			for (String string : hashSet) {

				Values values = new Values();
				values.setValue(string +"");
				structure.getValues().add(values);
			}
		
			arrays.getStructures().add(structure);
		}

		return arrays;
	}
	
	/**
	 * 
	 * @Title: createMeterUploadSetDevices
	 * @Description: 获得id,sn
	 * @param params
	 * @return
	 * @return HashMap<String,String>
	 * @throws
	 */
	public HashMap<String,String> createMeterUploadSetDevices(String[] params) {
		if(null == params){
			return null;
		}
		HashMap<String,String> values = new HashMap<String,String>();
		for(String string : params){
			String[] strings = string.split(",");
			values.put(strings[0], strings[1]);
		}
		
		return values;
	}
	
	/**
	 * 
	 * @Title: createMeterUploadSet
	 * @Description: 创建电表档案下发参数 (每一条档案是一个独立的结构体)
	 * @param params
	 * @return
	 * @return Arrays
	 * @throws
	 */
	public Arrays createMeterUploadSet(String[] params){
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		value.setValue(params.length+"");
		array.getValues().add(value);
		
		for (String string : params) {
			Structures structure = new Structures();
			String[] values = string.split(",");
			
			// 电能表/交流采样装置序号
			value = new Values();
			value.setValue(values[2]);
			structure.getValues().add(value);
			
			// 测量点
			value = new Values();
			value.setValue(values[2]);
			structure.getValues().add(value);
			
			// 端口
			value = new Values();
			value.setValue(values[5]);
			structure.getValues().add(value);
			
			// 波特率
			value = new Values();
			value.setValue(BaudType.parseBaud(values[4]).getIndex()+"");
			structure.getValues().add(value);
			
			// 协议
			value = new Values();
//			value.setValue(values[6]);
			value.setValue("30");// 先写死
			structure.getValues().add(value);
			
			// 通讯地址
			value = new Values();
			value.setValue(values[7]);
			structure.getValues().add(value);
			
			// 通信密码
			value = new Values();
			value.setValue("000000000000");
			structure.getValues().add(value);
			
			// 电能费率个数
			value = new Values();
			value.setValue("4");
			structure.getValues().add(value);
			
			// 有功电能示值小数位个数
			value = new Values();
			value.setValue("0");
			structure.getValues().add(value);
			
			// 有功电能示值整数位个数
			value = new Values();
			value.setValue("0");
			structure.getValues().add(value);
			
			// 所属采集器通信地址
			value = new Values();
			value.setValue("000000");
			structure.getValues().add(value);
			
			// 小类号
			value = new Values();
			value.setValue("1");
			structure.getValues().add(value);
						
		    // 大类号
			value = new Values();
			value.setValue("3");
			structure.getValues().add(value);
			
			array.getStructures().add(structure);
		}

		return array;
	}
	
	
	/**
	 * 
	 * @Title: createMeterUploadSet dlms
	 * @Description: 创建电表档案下发参数 (每一条档案是一个独立的结构体)
	 * @param params
	 * @return
	 * @return Arrays
	 * @throws
	 */
	public Arrays createMeterUploadDlmsSet(String[] params,boolean newMeterFlag){
		
		//var string = rowData.id+","+rowData.meterSn+","+rowData.logicalName+","+rowData.comType
		//+","+rowData.baudRate+","+rowData.protocol+","+rowData.wiringMethod;
		
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		value.setValue(params.length+"");
		array.getValues().add(value);
		
		for (String string : params) {
			Structures structure = new Structures();
			String[] values = string.split(",");
			
			// sns
			value = new Values();
			value.setValue(values[1]);
			structure.getValues().add(value);
			
			// logic name
			value = new Values();
			value.setValue(values[2]);
			structure.getValues().add(value);
	
			// portno 即 comType
			value = new Values();
			value.setValue(values[3]+"");
			structure.getValues().add(value);
			
			// 波特率
			value = new Values();
			value.setValue(BaudRateEnum.parseBaudRate(values[4]+"").getIndex()+"");
			structure.getValues().add(value);
			
			// 协议
			value = new Values();
//			value.setValue(values[6]);
			//todo 写死3 
			value.setValue("3");
			structure.getValues().add(value);
			
			// Data bit length: unsigned
			//Data transmission start bit: unsigned
			//	Data transmission stop bit: unsigned
			//Data transmission check bit: unsigned
			//	Data transparent meter tariff: unsigned -- 4
			value = new Values();
			value.setValue("8");
			structure.getValues().add(value);
			
			value = new Values();
			value.setValue("0");
			structure.getValues().add(value);
			
			value = new Values();
			value.setValue("1");
			structure.getValues().add(value);
			
			value = new Values();
			value.setValue("0");
			structure.getValues().add(value);
	
			value = new Values();
			value.setValue("4");
			structure.getValues().add(value);
			
			// wiring method
			value = new Values();
			value.setValue(values[6]+"");
			structure.getValues().add(value);
			
			
			// communication client 0
			//RS485 Logical Address 0
			//Collector Address  0
			//Communication Encryption values[11]
			//AARQ Authentication Level 0 取数据库加密类型？
			//LLS Key values[7]
			//Authentication-key values[8]
			//broadcast-key  values[10]
			//Dedicate-key  0
			//Encryption-key values[9]
			
			//Master-key 0
			//key-meter flag values[12]
			
			if(newMeterFlag){
				
				value = new Values();
				value.setValue("0");
				structure.getValues().add(value);
				//rs485LogicalAddress
				value = new Values();
				value.setValue(values[7]+"");
				structure.getValues().add(value);
				
				value = new Values();
				value.setValue("0");
				structure.getValues().add(value);
				
				
				// Encryption Mode
				value = new Values();
				value.setValue(values[12]+"");
				structure.getValues().add(value);
				//auth type
				if(!StringUtils.isEmpty(values[14])){
					if("0".equals(values[14]) || "1".equals(values[14])){
						value = new Values();
						value.setValue("1");
						structure.getValues().add(value);
					}else{
						value = new Values();
						value.setValue("0");
						structure.getValues().add(value);
					}
				}else{
					value = new Values();
					value.setValue("0");
					structure.getValues().add(value);
				}
				
				// lls password
				value = new Values();
				value.setValue(values[8]+"");
				structure.getValues().add(value);
				
				// AK
				value = new Values();
				value.setValue(values[9]+"");
				structure.getValues().add(value);
				

				// Broadcast Key
				value = new Values();
				value.setValue(values[11]+"");
				structure.getValues().add(value);
			
 
				value = new Values();
				value.setValue("0");
				structure.getValues().add(value);
				
				// EK
				value = new Values();
				value.setValue(values[10]+"");
				structure.getValues().add(value);
				

				value = new Values();
				value.setValue("0");
				structure.getValues().add(value);
				
				
				// KEY FLAG
				value = new Values();
				value.setValue(values[13]+"");
				structure.getValues().add(value);
				
			}
			
			array.getStructures().add(structure);
		}

		return array;
	}
	
	
		public Arrays deleteMeterUploadDlmsSet(String logicName){
			
			//var string = rowData.id+","+rowData.meterSn+","+rowData.logicalName+","+rowData.comType
			//+","+rowData.baudRate+","+rowData.protocol+","+rowData.wiringMethod;
			
			if(null == logicName){
				return null;
			}
			
			Arrays array = new Arrays();
			
			// 本次电能表/交流采样装置配置数量n
			Values value = new Values();
			value.setValue(logicName);
			array.getValues().add(value);
		
			
			return array;
		}
	
	
	/**
	 * 
	 * @Title: createMeterUploadSet 300
	 * @Description: 创建电表档案下发参数 (每一条档案是一个独立的结构体)
	 * @param params
	 * @return
	 * @return Arrays
	 * @throws
	 */
	public Arrays createMeterUploadSet300(String[] params){
		
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		value.setValue(params.length+"");
		array.getValues().add(value);
		
		for (String string : params) {
			Structures structure = new Structures();
			String[] values = string.split(",");

			// pointNum
			value = new Values();
			value.setValue(values[2]);
			structure.getValues().add(value);
	
			// status 1有效
			value = new Values();
			value.setValue(1+"");
			structure.getValues().add(value);
			
			// meterProperties
			value = new Values();
			value.setValue(values[4]+"");
			structure.getValues().add(value);
			
			// logicalName
			value = new Values();
			value.setValue(values[5]+"");
			structure.getValues().add(value);
			
			// protocol 默认1 DT/T645-2007
			value = new Values();
			value.setValue(1+"");
			structure.getValues().add(value);
			
			// meterType
			value = new Values();
			value.setValue(values[7]+"");
			structure.getValues().add(value);
			
			// totalDivType
			value = new Values();
			value.setValue(values[8]+"");
			structure.getValues().add(value);
			
			// isVip
			value = new Values();
			value.setValue(VipEnum.parseVip(values[9]).getIndex()+"");
			structure.getValues().add(value);
			
			// feeRateCount
			value = new Values();
			value.setValue(values[10]+"");
			structure.getValues().add(value);
			
			// collectorAddress
			value = new Values();
			value.setValue(values[11]+"");
			structure.getValues().add(value);
			
			// comNum
			value = new Values();
			value.setValue(values[12]+"");
			structure.getValues().add(value);
			
			// baudRate
			value = new Values();
			value.setValue(values[13]+"");
			structure.getValues().add(value);
			
			// stopFlag
			value = new Values();
			value.setValue(values[14]+"");
			structure.getValues().add(value);
			
			// ct
			value = new Values();
			value.setValue(values[15]+"");
			structure.getValues().add(value);
			
			// pt
			value = new Values();
			value.setValue(values[16]+"");
			structure.getValues().add(value);
				
			array.getStructures().add(structure);
		}

		return array;
	}
	
	
	public Arrays createImportantUploadSet(String[] params){
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		value.setValue(params.length+"");
		array.getValues().add(value);
		
		Structures structure = new Structures();
		for (String string : params) {
			String[] values = string.split(",");
			// 电能表/交流采样装置序号
			value = new Values();
			value.setValue(values[2]);
			structure.getValues().add(value);
		}
		
		array.getStructures().add(structure);
		return array;
	}
	
	public Arrays createImportantUploadSetDlms(String[] params){
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		
		// 本次电能表/交流采样装置配置数量n
	
	
		for (String string : params) {
			String[] values = string.split(",");
			// sn
			Values value = new Values();
			value.setValue(values[3]);
			array.getValues().add(value);
			
		}
		return array;
	}
	
	/**
	 * 
	 * @Title: createMeterUploadSet
	 * @Description: 创建电表档案召测参数 (每一条档案的PN是一个独立的结构体)
	 * @param params
	 * @return
	 * @return Arrays
	 * @throws
	 */
	public Arrays createMeterUploadSetPnsRead(String[] params){
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		for (String string : params) {
			String[] values = string.split(",");
			// 电能表/交流采样装置序号
			value = new Values();
			value.setValue(values[2]);			
			array.getValues().add(value);			
		}
		return array;
	}	
	
	
	/**
	 * 
	 * @Title: createMeterUploadSet 300
	 * @Description: 创建电表档案召测参数 (每一条档案的PN是一个独立的结构体)
	 * @param params
	 * @return
	 * @return Arrays
	 * @throws
	 */
	public Arrays createMeterUploadSetPnsRead300(String[] params){
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		// 本次电能表/交流采样装置配置数量n
		
		for (String string : params) {
			Values value = new Values();
			
			String[] values = string.split(",");
			
			// 电能表/交流采样装置序号
			value = new Values();
			value.setValue(values[1]);			
			array.getValues().add(value);		
	
		}
		return array;
	}	
	
	public Arrays createMeterUploadSetSnsDlmsRead(String[] params){
		if(null == params){
			return null;
		}
		
		Arrays array = new Arrays();
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		for (String string : params) {
			String[] values = string.split(",");
			// 电能表/交流采样装置序号
			value = new Values();
			value.setValue(values[1]);			
			array.getValues().add(value);			
		}
		return array;
	}	
	
	/**
	 * 
	 * @Title: createTerminalIpPortApn
	 * @Description: 封装集中器 ip port apn 参数
	 * @param ip
	 * @param port
	 * @param ipBak
	 * @param portBak
	 * @param apn
	 * @return
	 * @return Arrays 符合 iec 标准参数集合，下发到 uci
	 * @throws
	 */
	public Arrays createTerminalIpPortApn(String ip,int port,String ipBak,int portBak,String apn) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(ip);
		arrays.getValues().add(values);
		
		values = new Values();
		values.setValue(port +"");
		arrays.getValues().add(values);
		
		values = new Values();
		values.setValue(ipBak);
		arrays.getValues().add(values);
		
		values = new Values();
		values.setValue(portBak +"");
		arrays.getValues().add(values);
		
		values = new Values();
		values.setValue(apn);
		arrays.getValues().add(values);
		
		return arrays;
	}
	
	
	/**
	 * 
	 * @Title: createTerminalIpPortApn dlms
	 * @Description: 封装集中器 ip port apn 参数
	 * @param ip
	 * @param port
	 * @param ipBak
	 * @param portBak
	 * @param apn
	 * @return
	 * @return Arrays 符合 iec 标准参数集合，下发到 uci
	 * @throws
	 */
	public Arrays createTerminalIpPortDlms(String ip,int port,String ipBak,int portBak) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(ip);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(port +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(ipBak);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(portBak +"");
		structure.getValues().add(values);

		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	
	public Arrays createHesIpAndPortModule(String ip,int port,String ipBak,int portBak) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(ip);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(port +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(ipBak);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(portBak +"");
		structure.getValues().add(values);

		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	public Arrays createFtpIpAndPortModule(String ip,int port,String userName,String password) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(ip);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(port +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(userName);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(password +"");
		structure.getValues().add(values);

		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	public Arrays createGprsDlms(String apn,String userName,String password,String dialedNumber) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(apn);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(userName +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(password);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(dialedNumber +"");
		structure.getValues().add(values);

		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	

	public Arrays createUserAndPasswordModule(String userName,String password) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		
		values.setValue(userName +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(password);
		structure.getValues().add(values);
		
		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	public Arrays createVpnDial(boolean isCheckedConnected,String ipAddressVpn,String vpnPort,String vpnUserName
			,String vpnPassword) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(isCheckedConnected?"1":"0");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(ipAddressVpn +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(vpnPort);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(vpnUserName +"");
		structure.getValues().add(values);

		values = new Values();
		values.setValue(vpnPassword +"");
		structure.getValues().add(values);
		
		arrays.getStructures().add(structure);
		
		return arrays;
	}
	

	public Arrays createSearchMeterSwitch(boolean isSwitched,String txtStartedTime32,String txtDelayTime32,boolean isActiveCollect
			,boolean isPassiveCollect) {
		Arrays arrays = new Arrays();

		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(isSwitched?"1":"0");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(txtStartedTime32 +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(txtDelayTime32);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(isActiveCollect?"1":"0");
		structure.getValues().add(values);

		values = new Values();
		values.setValue(isPassiveCollect?"1":"0");
		structure.getValues().add(values);
		
		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	public Arrays createConcentratorIpAddress(String terminalIp,String subnetMask,String gatewayMask) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values = new Values();
		values.setValue(terminalIp);
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(subnetMask +"");
		structure.getValues().add(values);
		
		values = new Values();
		values.setValue(gatewayMask);
		structure.getValues().add(values);
		

		arrays.getStructures().add(structure);
		
		return arrays;
	}
	
	public Arrays createClockDlms(String clock) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(clock);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	public Arrays createVersionInfoDlms(String versionInfo) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(versionInfo);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	
	public Arrays createApnModule(String apn) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(apn);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	public Arrays createModule(int value) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(value+"");
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	public Arrays createEncryptionModeDlms(String encryptionMode) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(encryptionMode);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	
	public Arrays createHeartbeatCycleDlms(String heartbeatCycle) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(heartbeatCycle);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	public Arrays createEnergyProfileReadingCycleDlms(String energyProfileReadingCycle) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(energyProfileReadingCycle);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	
	public Arrays createPLCChannelSendTimesDlms(String PLCChannelSendTimes) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(PLCChannelSendTimes);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	
	public Arrays createPlcChannelTimeoutDlms(String plcChannelTimeout) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(plcChannelTimeout);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	public Arrays createPlcChannelTimeoutDlms2(String plcChannelTimeout,String timingPeriod) {
		Arrays arrays = new Arrays();
		
		Structures structure = new Structures();
		
		Values values1 = new Values();
		values1.setValue(plcChannelTimeout);
		values1.setType("string");
		
		Values values2 = new Values();
		values2.setValue(timingPeriod);
		values2.setType("string");
		
		
		structure.getValues().add(values1);
		structure.getValues().add(values2);
		
		arrays.getStructures().add(structure);
	
		return arrays;
	}
	
	
	public Arrays createEncryptionKeyDlms(String encryptionKey) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(encryptionKey);
		arrays.getValues().add(values);
	
		return arrays;
	}
	
	
	
	/**
	 * 
	 * @Title: createResetType
	 * @Description: 封装 resetType 参数
	 * @param resetType
	 * @return
	 * @return Arrays
	 * @throws
	 */
	public Arrays createResetType(int resetType) {
		Arrays arrays = new Arrays();
		Values values = new Values();
		values.setValue(resetType +"");
		arrays.getValues().add(values);
		
		return arrays;
	}
	

	public Arrays createMeterUploadSet(String requestInfo){
	
		
		Arrays array = new Arrays();
		
		// 本次电能表/交流采样装置配置数量n
		Values value = new Values();
		value.setValue("1");
		array.getValues().add(value);
		
	
		Structures structure = new Structures();
	
		// input buffer
		value = new Values();
		value.setValue(requestInfo);
		structure.getValues().add(value);
		
		array.getStructures().add(structure);
		
		return array;
	}
}
