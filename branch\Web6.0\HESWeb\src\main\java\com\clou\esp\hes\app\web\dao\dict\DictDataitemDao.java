/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDataitem{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-22 03:30:03
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.dict;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.dict.DictDataitem;

@MyBatisDao
public interface DictDataitemDao extends CrudDao<DictDataitem> {

	public List<DictDataitem> getDictDataitemByGroupId(@Param(value = "groupId") String groupId);
	public List<DictDataitem> getMeterDataEventExportList(DictDataitem temp);
	public List<DictDataitem> getListByIds(@Param("dataItemIds") List<String> dataItemIds);
	public String getDataitemBySubId(@Param("subId")String subId, @Param("protoId")String protoId);
}