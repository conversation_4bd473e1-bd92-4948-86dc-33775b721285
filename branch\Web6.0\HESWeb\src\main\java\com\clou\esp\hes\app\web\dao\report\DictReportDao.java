/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictReport{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2018-05-22 04:14:25
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.report;

import java.util.List;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataCalcObj;
import com.clou.esp.hes.app.web.model.report.BillingReport;
import com.clou.esp.hes.app.web.model.report.DictReport;
import com.clou.esp.hes.app.web.model.report.LineLossDetailReport;
import com.clou.esp.hes.app.web.model.report.LineLossReport;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DictReportDao extends CrudDao<DictReport>{

	List<LineLossReport> findLineLossSingleJqGrid(JqGridSearchTo jqGridSearchTo);
	List<LineLossReport> findLineLossOrganizationJqGrid(JqGridSearchTo jqGridSearchTo);
	List<BillingReport> findBillingOrganizationJqGrid(JqGridSearchTo jqGridSearchTo);
	List<BillingReport> exportBillingOrganizationJqGrid(JqGridSearchTo jqGridSearchTo);
	List<LineLossDetailReport> findLineLossDetailJqGrid(JqGridSearchTo jqGridSearchTo);
	List<DataCalcObj> getDataCalcObj(JqGridSearchTo jqGridSearchTo);
	List<BillingReport> findBillingOrganizationMobile(JqGridSearchTo jqGridSearchTo);
	
}