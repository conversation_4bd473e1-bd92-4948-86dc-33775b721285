package com.clou.esp.hes.app.web.core.shiro.filter;

import java.io.PrintWriter;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.WebUtils;

import com.clou.esp.hes.app.web.core.shiro.session.CustomSessionManager;
import com.clou.esp.hes.app.web.core.shiro.session.SessionStatus;
import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.power7000g.core.util.json.AjaxJson;

/**
 * 判断是否踢出
 * 
 * <AUTHOR>
 * 
 */
public class SimpleAuthFilter extends AccessControlFilter {

	@Override
	protected boolean isAccessAllowed(ServletRequest request,
			ServletResponse response, Object mappedValue) throws Exception {
		// HttpServletRequest httpRequest = ((HttpServletRequest) request);
		// String url = httpRequest.getRequestURI();
		// if (url.startsWith("/open/")) {
		// return Boolean.TRUE;
		// }
		
		HttpServletRequest httpServletRequest = (HttpServletRequest)request;
		boolean isCookieExitsFlag = true;
	
		SysUser token = TokenManager.getToken();
		if(token != null){
			return Boolean.TRUE;
		}
		
		if(token == null){
			boolean exitFlag = true;
			int i = 0;
			while(exitFlag && i < 2){
				i++;
				Thread.sleep(10);
				token = TokenManager.getToken();
				
				if(httpServletRequest.getRequestURI().contains("getCurrentEventSum") || httpServletRequest.getRequestURI().contains("pushlet")
						 || httpServletRequest.getRequestURI().contains("interfaces")){
					return Boolean.TRUE;
				}
				
				if(token != null){
					exitFlag = false;
				}
			}
		}
		
	
		if(isCookieExitsFlag){
			if(httpServletRequest.getCookies() == null){
				return Boolean.TRUE;
			}
			Subject subject = getSubject(request, response);
			Session session = subject.getSession();
			AjaxJson json = new AjaxJson();
			SessionStatus sessionStatus = (SessionStatus) session
					.getAttribute(CustomSessionManager.SESSION_STATUS);
			if (null != sessionStatus && !sessionStatus.isOnlineStatus()) {
				// 判断是不是Ajax请求
				if (ShiroFilterUtils.isAjax(request)) {
					json.setErrorMsg("You are already logged in elsewhere, please login again!");
					out(request, response, json.toString());
				}
				return Boolean.FALSE;
			}
		}
		
		
		return Boolean.TRUE;
	}

	@Override
	protected boolean onAccessDenied(ServletRequest request,
			ServletResponse response) throws Exception {

		// 先退出
		Subject subject = getSubject(request, response);
		subject.logout();
		/**
		 * 保存Request，用来保存当前Request，然后登录后可以跳转到当前浏览的页面。 比如：
		 * 我要访问一个URL地址，/admin/index
		 * .html，这个页面是要登录。然后要跳转到登录页面，但是登录后要跳转回来到/admin/index.html这个地址，怎么办？
		 * 传统的解决方法是变成/user/login.shtml?redirectUrl=/admin/index.html。
		 * shiro的解决办法不是这样的。需要：<code>WebUtils.getSavedRequest(request);</code>
		 * 然后：{@link UserLoginController.submitLogin(...)}中的
		 * <code>String url = WebUtils.getSavedRequest(request).getRequestUrl();</code>
		 * 如果还有问题，请咨询我。
		 */
		WebUtils.saveRequest(request);
		// 再重定向
		// WebUtils.issueRedirect(request, response, "/open/kickedOut.shtml");
		out(request,response,"<script type=\"text/javascript\">if(window.parent.layer){window.parent.layer.confirm('Log in overtime, please login again!', {"
				  +" title:'Message', closeBtn: 0, btn: ['OK'],  icon: 0, skin: 'layer-ext-moon'}, function(){window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";});}else{window.top.location.href=\""+ ((HttpServletRequest) request).getContextPath()+ ShiroFilterUtils.LOGIN_URL + "\";}</script>");
		return false;
	}

	private void out(ServletRequest request, ServletResponse response,
			String str) {
		PrintWriter out = null;
		try {
			response.setCharacterEncoding("UTF-8");
			out = response.getWriter();
			out.println(str);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != out) {
				out.flush();
				out.close();
			}
		}
	}

}
