/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterEvent{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 01:56:05
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataMeterEvent;

@MyBatisDao
public interface DataMeterEventDao extends CrudDao<DataMeterEvent> {

	public int batchInsert(@Param(value = "sql") String sql,@Param(value = "osql") String osql);

	public int batchSave(List<DataMeterEvent> eList);

	public Long getCountByOrgIds(Map<String, Object> params);
	
	public List<DataMeterEvent>   getTodayEventByDeviceId(@Param(value = "deviceId") String deviceId,@Param(value = "protocolId") String protocolId);

	public List<String> getTodayEventByDeviceIds(@Param(value = "deviceIds") List<String> deviceIds);
}