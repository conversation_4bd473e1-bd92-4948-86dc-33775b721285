/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetCommunicator{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-06 08:04:54
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.asset;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.asset.AssetCommunicator;
import com.clou.esp.hes.app.web.model.asset.AssetMeter;
import com.power7000g.core.util.jqgrid.JqGridResponseTo;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface AssetCommunicatorDao extends CrudDao<AssetCommunicator> {

	public int batchInsert(List<AssetCommunicator> cList);

	public List<AssetCommunicator> getListGroupByFwVersion();
	
	public List<AssetCommunicator> getListLimitTwenty(Map<String, Object> p);
	
	public List<AssetCommunicator> getFwVersionGroupByModel(@Param(value = "modelTypeId")String modelTypeId);
	
	public List<AssetCommunicator> getListNoGprs();
	
	public List<Map<String, Object>> getSnNameByMaps(Map<String, Object> params);
	
	public List<AssetCommunicator> getForJqGridAdvanced(JqGridSearchTo jqGridSearchTo) ;
	
	public List<AssetCommunicator> getBySns(@Param(value = "sns")List<String> sns);

	public void relatePreMeterToDCU(AssetCommunicator dcu);
	
	public void updateRelateDCU(AssetCommunicator dcu);	
}