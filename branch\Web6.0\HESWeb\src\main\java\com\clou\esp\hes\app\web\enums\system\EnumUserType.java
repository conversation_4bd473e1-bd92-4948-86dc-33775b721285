package com.clou.esp.hes.app.web.enums.system;

public enum EnumUserType {
	/**
	 * 1=超级用户（科陆）；
	 */
	SUPER_USER(1, "超级用户"),
	/**
	 * 2=系统用户(租户约束，客户超级管理员
	 */
	SYSTEM_USER(2, "系统用户"),
	/**
	 * 3=普通用户(客户的，带权限的)
	 */
	COMMON_USER(3, "普通用户"), ;
	private int index;
	private String remark;

	private EnumUserType(int index, String remark) {
		this.remark = remark;
		this.index = index;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
