package com.clou.esp.hes.app.web.core.util;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

import javax.swing.filechooser.FileSystemView;

public class FileByteProcessUtil {
	/**
	 * 
	 * 获得指定文件的byte数组
	 */
	private static byte[] getBytes(String filePath) {

		byte[] buffer = null;

		try {

			File file = new File(filePath);

			FileInputStream fis = new FileInputStream(file);

			ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);

			byte[] b = new byte[1000];

			int n;

			while ((n = fis.read(b)) != -1) {

				bos.write(b, 0, n);

			}

			fis.close();

			bos.close();

			buffer = bos.toByteArray();

		} catch (FileNotFoundException e) {

			e.printStackTrace();

		} catch (IOException e) {

			e.printStackTrace();

		}

		return buffer;

	}

	/**
	 * 
	 * 根据byte数组，生成文件
	 */

	public static void getFile(byte[] bfile, String filePath, String fileName) {

		BufferedOutputStream bos = null;

		FileOutputStream fos = null;

		File file = null;

		try {

			File dir = new File(filePath);

			if (!dir.exists() && dir.isDirectory()) {// 判断文件目录是否存在

				dir.mkdirs();

			}

			file = new File(filePath + "\\" + fileName);

			fos = new FileOutputStream(file);

			bos = new BufferedOutputStream(fos);

			bos.write(bfile);

		} catch (Exception e) {

			e.printStackTrace();

		} finally {

			if (bos != null) {

				try {

					bos.close();

				} catch (IOException e1) {

					e1.printStackTrace();

				}

			}

			if (fos != null) {

				try {

					fos.close();

				} catch (IOException e1) {

					e1.printStackTrace();

				}

			}

		}

	}
	
	/**
	   * 得到系统U盘根目录
	   */
	  public static String findURootPath(){
	    FileSystemView sys = FileSystemView.getFileSystemView();
	    //循环盘符
	    File[] files = File.listRoots(); 
	    for(File file:files){
	      //得到系统中存在的C:\,D:\,E:\,F:\,H:\
	      System.out.println("系统中存在的"+file.getPath());
	    }
	    File file = null;
	    String path = null;
	    for(int i = 0; i < files.length; i++) { 
	      //得到文字命名形式的盘符系统 (C:)、软件 (D:)、公司文档 (E:)、测试U盘 (H:)
	      System.out.println("得到文字命名形式的盘符"+sys.getSystemDisplayName(files[i]));
	      if(sys.getSystemDisplayName(files[i]).contains("测试U盘")){
	        file = files[i];
	        break;
	      }
	    }
	    if(file!=null){
	      path = file.getPath();
	    }
	    return path;
	  }

	public static void main(String[] args) throws Exception {
		// encrypt("E:/buzhihuo.jpg","123");
		// decrypt("E:/buzhihuo.jpg","E:/buzhihuo1.jpg",3);

		//getFile(getBytes("E:/123.txt"), "E:/ttttt", "fuck.txt");
		findURootPath();
	}
}
