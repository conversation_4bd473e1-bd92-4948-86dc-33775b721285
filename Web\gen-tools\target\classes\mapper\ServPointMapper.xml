<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.ServPointMapper">
  <resultMap id="BaseResultMap" type="com.power7000.restful.model.ServPointModel">
    <!--
      WARNING - @mbg.generated
    -->        
    <result column="SERVICE_POINT_ID" jdbcType="VARCHAR" property="servPointId" />
    <result column="ADDRESS_1" jdbcType="VARCHAR" property="address1" />
    <result column="ADDRESS_2" jdbcType="VARCHAR" property="address2" />
    <result column="ADDRESS_3" jdbcType="VARCHAR" property="address3" />
    <result column="GEO_CODE" jdbcType="VARCHAR" property="geoCode" />
    <result column="METER_SERIAL_NO" jdbcType="VARCHAR" property="meterSerialNo" />
    <result column="TARIFF_ID" jdbcType="VARCHAR" property="tariffId" />
    <result column="BLOCK_FLAG" jdbcType="BIT" property="blockFlag" />
    <result column="BLOCK_REASON" jdbcType="VARCHAR" property="blockReason" />
    <result column="EXIST" jdbcType="VARCHAR" property="exist" />
    <result column="IS_STORAGE" jdbcType="VARCHAR" property="isStorage" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="DIGITAL_ADDRESS" jdbcType="VARCHAR" property="digitalAddress" />
  </resultMap>
  
  	<sql id="Base_Column_List">
		<!-- WARNING - @mbggenerated This element is automatically generated by MyBatis Generator, do not modify. This element was generated on Tue Jul 21 16:26:06 CST 2015. -->
		SERVICE_POINT_ID,ADDRESS_1,ADDRESS_2,ADDRESS_3,GEO_CODE,METER_SERIAL_NO,TARIFF_ID,CUSTOMER_ID,
		BLOCK_FLAG,BLOCK_REASON,EXIST,IS_STORAGE,CREATE_DATE,UPDATE_DATE,DIGITAL_ADDRESS
	</sql>
  
  	<!-- 根据资产编号查询tariff_code -->
	<select id="selectTariffCodeByserialNo" parameterType="java.util.Map" resultType="java.util.HashMap">
		SELECT pcs.BLOCK_FLAG, pct.TARIFF_CODE, pcs.CUSTOMER_ID AS SERVICE_POINT_ID , pcs.GEO_CODE
		FROM ppm_public_cms_servicepoint pcs
		LEFT JOIN ppm_public_cms_tariff pct ON pcs.TARIFF_ID = pct.TARIFF_ID
		WHERE pcs.METER_SERIAL_NO = #{meterSerialNo,jdbcType=VARCHAR}
		and pcs.CUSTOMER_ID is not null
 		and pcs.CUSTOMER_ID  != ''
		limit 1
	</select>
	
	<!-- 根据用户编号查询服務點对象 -->
	<select id="selectservicepointByservPointId" resultMap="BaseResultMap" parameterType="java.util.Map">
		select

		<include refid="Base_Column_List" />

		from ppm_public_cms_servicepoint pur

		<where>
			<if test=" servPointId !=null and servPointId != ''">
				pur.SERVICE_POINT_ID = #{servPointId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	<!-- 根据用户编号查询居民用户对象 -->
	<select id="selectservicepointByUserNo" resultMap="BaseResultMap" parameterType="java.util.Map">
		select
		<include refid="Base_Column_List" />
		from ppm_public_cms_servicepoint pur
		<where>
			<if test=" servPointId !=null and servPointId != ''">
				pur.SERVICE_POINT_ID = #{servPointId,jdbcType=VARCHAR}
			</if>
		</where>
	</select>
	
	
	<insert id="insertServPoint" parameterType="com.power7000.restful.model.ServPointModel">
		insert into ppm_public_cms_servicepoint (
		SERVICE_POINT_ID,
		ADDRESS_1,
		ADDRESS_2,
		ADDRESS_3,
		GEO_CODE,
		METER_SERIAL_NO,
		TARIFF_ID,
		BLOCK_FLAG,
		BLOCK_REASON,
		EXIST,
		IS_STORAGE,
		CUSTOMER_ID,
		CREATE_DATE,
		UPDATE_DATE,
		DIGITAL_ADDRESS
		)
		values
		(
		#{servPointId,jdbcType=VARCHAR},
		#{address1,jdbcType=VARCHAR},
		#{address2,jdbcType=VARCHAR},
		#{address3,jdbcType=VARCHAR},
		#{geoCode,jdbcType=VARCHAR},
		#{meterSerialNo,jdbcType=VARCHAR},
		#{tariffId,jdbcType=VARCHAR},
		#{blockFlag,jdbcType=BOOLEAN},
		#{blockReason,jdbcType=VARCHAR},
		#{exist,jdbcType=BOOLEAN},
		#{isStorage,jdbcType=VARCHAR},
		#{customerId,jdbcType=VARCHAR},
		now(),
		now(),
		#{digitalAddress,jdbcType=VARCHAR}
		)
	</insert>
	
	<!-- 根据用户ID修改数据 -->
	<update id="updateServPoint" parameterType="com.power7000.restful.model.ServPointModel">
		update ppm_public_cms_servicepoint
		<set>
			<if test="address1 != null">
				ADDRESS_1 = #{address1,jdbcType=VARCHAR},
			</if>
			<if test="address2 != null">
				ADDRESS_2 = #{address2,jdbcType=VARCHAR},
			</if>
			<if test="address3 != null">
				ADDRESS_3 = #{address3,jdbcType=VARCHAR},
			</if>
			<if test="geoCode != null">
				GEO_CODE = #{geoCode,jdbcType=VARCHAR},
			</if>
			<if test="meterSerialNo != null">
				METER_SERIAL_NO = #{meterSerialNo,jdbcType=VARCHAR},
			</if>
			<if test="tariffId != null">
				TARIFF_ID = #{tariffId,jdbcType=VARCHAR},
			</if>
			<if test="customerId != null">
				CUSTOMER_ID = #{customerId,jdbcType=VARCHAR},
			</if>
			<if test="blockFlag != null">
				BLOCK_FLAG = #{blockFlag},
			</if>
			<if test="blockReason != null">
				BLOCK_REASON = #{blockReason,jdbcType=VARCHAR},
			</if>
			<if test="exist != null and exist != ''">
				EXIST = #{exist},
			</if>
			<if test="digitalAddress != null and digitalAddress != ''">
				DIGITAL_ADDRESS = #{digitalAddress},
			</if>
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
			UPDATE_DATE = now()
		</set>
		where SERVICE_POINT_ID = #{servPointId,jdbcType=VARCHAR}
	</update>
	
	
	<!-- 根据用户ID修改数据 -->
	<update id="updateByMap" parameterType="java.util.Map">
		update ppm_public_cms_servicepoint
		<set>
		
			<if test="isStorage != null and isStorage != ''">
				IS_STORAGE = #{isStorage,jdbcType=VARCHAR},
			</if>
			UPDATE_DATE = now()
		</set>
		where METER_SERIAL_NO = #{meterSerialNo,jdbcType=VARCHAR}
	</update>
	
</mapper>