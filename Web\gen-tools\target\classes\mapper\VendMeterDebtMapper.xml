<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.VendMeterDebtMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.VendMeterDebt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="Utility_ID" jdbcType="VARCHAR" property="utilityId" />
    <result column="Customer_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="Meter_ID" jdbcType="VARCHAR" property="meterId" />
    <result column="Serial_Number" jdbcType="VARCHAR" property="serialNumber" />
    <result column="Comm_Address" jdbcType="VARCHAR" property="commAddress" />
    <result column="Debt_Date" jdbcType="TIMESTAMP" property="debtDate" />
    <result column="Debt_Total" jdbcType="DECIMAL" property="debtTotal" />
    <result column="Debt_Period" jdbcType="DECIMAL" property="debtPeriod" />
    <result column="Payed_Period" jdbcType="DECIMAL" property="payedPeriod" />
    <result column="Left_Period" jdbcType="DECIMAL" property="leftPeriod" />
    <result column="Money_Period" jdbcType="DECIMAL" property="moneyPeriod" />
    <result column="Debt_Money" jdbcType="DECIMAL" property="debtMoney" />
    <result column="Payed_Total" jdbcType="DECIMAL" property="payedTotal" />
    <result column="Divide_Remainder" jdbcType="DECIMAL" property="divideRemainder" />
    <result column="Debt_Type" jdbcType="DECIMAL" property="debtType" />
    <result column="Record_State" jdbcType="DECIMAL" property="recordState" />
    <result column="Debt_Collect_Month" jdbcType="INTEGER" property="debtCollectMonth" />
    <result column="Debt_Collect_Date" jdbcType="TIMESTAMP" property="debtCollectDate" />
    <result column="User_ID" jdbcType="VARCHAR" property="userId" />
    <result column="Save_DB_Date" jdbcType="TIMESTAMP" property="saveDbDate" />
    <result column="Readme" jdbcType="VARCHAR" property="readme" />
  </resultMap>
  
  <select id="selectLimitOne" resultMap="BaseResultMap" parameterType="com.power7000.model.VendMeterDebt">
		<!-- WARNING - @mbggenerated This element is automatically generated by MyBatis Generator, do not modify. This element was generated on Fri Mar 18 10:37:28 CST 2016. -->
		select
		*
		from vend_meter_debt
		<where>
			1=1
			
			<if test="readme != null and readme != ''">
				and README = #{readme, jdbcType=VARCHAR}
			</if>
		</where>
		ORDER BY Debt_Date desc limit 1
	</select>
  
</mapper>