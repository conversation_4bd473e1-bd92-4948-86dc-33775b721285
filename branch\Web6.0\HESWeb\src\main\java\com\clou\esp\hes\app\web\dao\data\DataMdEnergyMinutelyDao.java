/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DataMeterDataEnergyMinutely{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-24 03:20:08
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.data;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.data.DataIntegrityDetails;
import com.clou.esp.hes.app.web.model.data.DataMdEnergyMinutely;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;

@MyBatisDao
public interface DataMdEnergyMinutelyDao extends
		CrudDao<DataMdEnergyMinutely> {

	public DataMdEnergyMinutely getMaxValue();

	public int batchInsert(@Param(value = "date") String date,@Param(value = "hour") String hour);
	/**
	 * 获取采集数据（分页）
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<DataIntegrityDetails> getIntegrityDetailsForJqGrid(JqGridSearchTo jqGridSearchTo);
	/**
	 * 获取采集数据
	 * @param jqGridSearchTo
	 * @return
	 */
	public List<DataIntegrityDetails> getDataIntegrityDetailList(@Param(value = "deviceId") String deviceId,@Param(value = "date") String date);
	/**
	 * 根据对象获取抄读列表
	 * @param dataIntegrityDetails
	 * @return
	 */
	public List<DataIntegrityDetails> getDataIntegrityDetailList(DataIntegrityDetails dataIntegrityDetails);
	
	/**
	 *获取meter基础信息 
	 */
	public DataIntegrityDetails getMeterBaseInfoById(String deviceId);
	
	/**
	 *获取当天数据 
	 */
	public List<DataIntegrityDetails> getDataIntegrityDetail(JqGridSearchTo jqGridSearchTo);
}