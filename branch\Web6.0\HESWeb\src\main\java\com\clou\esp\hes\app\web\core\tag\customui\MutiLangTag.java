package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：MutiLangTag.java
 * 版权：Copyright by HESWeb Team
 * 描述：多语言输出工具
 * 修改人：严浪
 * 修改时间：2017年8月9日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class MutiLangTag extends TagSupport {
	/**
	 *  Description: 1、根据两个key拼接… 2、… Implement: 1、lanKey内容如:{0}删除成功…
	 * 2、langArg内容如:用户 3,最终结果如: 用户删除成功
	 */
	private static final long serialVersionUID = 1L;
	protected String lanKey; 
	protected String langArg; 
	

	public int doStartTag() throws JspTagException {
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspTagException {
		JspWriter out = null;
		try {
			out = this.pageContext.getOut();
			out.print(end().toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return EVAL_PAGE;
	}

	public StringBuffer end() {
		StringBuffer sb = new StringBuffer();
		if(StringUtil.isNotEmpty(langArg)){
			sb.append(MutiLangUtil.doMutiLang(lanKey, langArg));
		}else{
			sb.append(MutiLangUtil.doMutiLang(lanKey));
		}
		return sb;
	}

	/**
	 * 编码内容
	 * @return
	 */
	public String getLanKey() {
		return lanKey;
	}

	/**
	 * 编码内容
	 * @param lanKey
	 */
	public void setLanKey(String lanKey) {
		this.lanKey = lanKey;
	}

	/**
	 * 拼接编码 不为空这如下
	 *  Description: 1、根据两个key拼接… 2、… Implement: 1、lanKey内容如:{0}删除成功…
	 * 2、langArg内容如:用户 3,最终结果如: 用户删除成功
	 * @return
	 */
	public String getLangArg() {
		return langArg;
	}

	/**
	 * 拼接编码 不为空这如下
	 *  Description: 1、根据两个key拼接… 2、… Implement: 1、lanKey内容如:{0}删除成功…
	 * 2、langArg内容如:用户 3,最终结果如: 用户删除成功
	 * @param langArg
	 */
	public void setLangArg(String langArg) {
		this.langArg = langArg;
	}
	
	

	
}
