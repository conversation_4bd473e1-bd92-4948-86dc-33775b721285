package com.clou.esp.hes.app.web.core.tag.vo;


/**
 * 文件名：FindListOpt.java
 * 版权：Copyright by Power7000g Team
 * 描述：公用操作类
 * 修改人：严浪
 * 修改时间：2017年3月20日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FindListOpt {
    private String url;//操作链接地址
    private String title;//按钮名称
    private String icon;//class样式
    private String width;//弹出窗宽度
    private String height;//弹出窗高度
    private OptTypeDirection type;//按钮类型
    private String value;//传入参数
    private String isbtn;//是否是操作选项以外的链接
    private String message;//询问链接的提示语
    private String exp;//判断链接是否显示的表达式
    private String funname;//自定义函数名称
    private String onclick;//选项单击事件
    
    
    
    
    public FindListOpt() {
        super();
        // TODO Auto-generated constructor stub
    }


    public FindListOpt(String url, String title, String icon, OptTypeDirection type, String value,
                       String isbtn, String message, String exp, String funname, String onclick) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.value = value;
        this.isbtn = isbtn;
        this.message = message;
        this.exp = exp;
        this.funname = funname;
        this.onclick = onclick;
    }




    public FindListOpt(String url, String title,String exp, String icon, OptTypeDirection type,
                       String message) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.message = message;
        this.exp = exp;
    }
    
    


    public FindListOpt(String url, String title, String icon, OptTypeDirection type, String exp) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.exp = exp;
    }
    
    public FindListOpt(String url, String title, String icon, OptTypeDirection type, String exp,String width) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.exp = exp;
        this.width=width;
    }


    public FindListOpt(String title, String icon, OptTypeDirection type, String exp,
                       String funname) {
        super();
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.exp = exp;
        this.funname = funname;
    }
    
    
    


    public FindListOpt(String url, String title, String icon, OptTypeDirection type,
                       String message, String exp, String funname) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.message = message;
        this.exp = exp;
        this.funname = funname;
    }


    public FindListOpt(String url, String title, String icon, OptTypeDirection type, String width, String height,
                       String funname, String onclick) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.type = type;
        this.funname = funname;
        this.onclick = onclick;
        this.width = width;
        this.height = height;
    }
    
    


    public FindListOpt(String url, String title, String icon, String width, String height,
                       OptTypeDirection type, String exp) {
        super();
        this.url = url;
        this.title = title;
        this.icon = icon;
        this.width = width;
        this.height = height;
        this.type = type;
        this.exp = exp;
    }


    public String getUrl() {
        return url;
    }


    public String getTitle() {
        return title;
    }


    public String getIcon() {
        return icon;
    }


    public OptTypeDirection getType() {
        return type;
    }


    public String getValue() {
        return value;
    }


    public String getIsbtn() {
        return isbtn;
    }


    public String getMessage() {
        return message;
    }


    public String getExp() {
        return exp;
    }


    public String getFunname() {
        return funname;
    }


    public String getOnclick() {
        return onclick;
    }


    public void setUrl(String url) {
        this.url = url;
    }


    public void setTitle(String title) {
        this.title = title;
    }


    public void setIcon(String icon) {
        this.icon = icon;
    }


    public void setType(OptTypeDirection type) {
        this.type = type;
    }


    public void setValue(String value) {
        this.value = value;
    }


    public void setIsbtn(String isbtn) {
        this.isbtn = isbtn;
    }


    public void setMessage(String message) {
        this.message = message;
    }


    public void setExp(String exp) {
        this.exp = exp;
    }


    public void setFunname(String funname) {
        this.funname = funname;
    }


    public void setOnclick(String onclick) {
        this.onclick = onclick;
    }


    public String getWidth() {
        return width;
    }


    public String getHeight() {
        return height;
    }


    public void setWidth(String width) {
        this.width = width;
    }


    public void setHeight(String height) {
        this.height = height;
    }     
    
    

}
