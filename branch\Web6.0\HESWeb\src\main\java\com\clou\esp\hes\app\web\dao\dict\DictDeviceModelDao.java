/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class DictDeviceModel{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：严浪
 * 创建于：2017-11-18 02:59:36
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.dict;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.dict.DictDeviceModel;

import java.util.List;

@MyBatisDao
public interface DictDeviceModelDao extends CrudDao<DictDeviceModel>{
    public List<DictDeviceModel> getDictDeviceList(DictDeviceModel entity);

}