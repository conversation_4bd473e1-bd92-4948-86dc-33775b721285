<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.PrepTransExtMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.PrepTransExt">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="TRANSACTION_ID" jdbcType="VARCHAR" property="transactionId" />
    <id column="CO_CONCEPTO" jdbcType="VARCHAR" property="coConcepto" />
    <id column="CO_SISTEMA" jdbcType="VARCHAR" property="coSistema" />
    <result column="TS" jdbcType="DATE" property="ts" />
    <result column="NUM_APA" jdbcType="VARCHAR" property="numApa" />
    <result column="CO_MARCA" jdbcType="VARCHAR" property="coMarca" />
    <result column="CUST_NAME" jdbcType="VARCHAR" property="custName" />
    <result column="CUSTOMER_NUMBER" jdbcType="VARCHAR" property="customerNumber" />
    <result column="SERVICE_POINT_NO" jdbcType="VARCHAR" property="servicePointNo" />
    <result column="VENDOR_ID" jdbcType="VARCHAR" property="vendorId" />
    <result column="RECPT_NO" jdbcType="VARCHAR" property="recptNo" />
    <result column="TOKEN_NO" jdbcType="VARCHAR" property="tokenNo" />
    <result column="PMETHOD" jdbcType="VARCHAR" property="pmethod" />
    <result column="CSMO_FACT" jdbcType="DECIMAL" property="csmoFact" />
    <result column="CYCLE" jdbcType="INTEGER" property="cycle" />
    <result column="CYCLE_DATE" jdbcType="DATE" property="cycleDate" />
    <result column="IMP_CONCEPTO" jdbcType="DECIMAL" property="impConcepto" />
    <result column="DEBT_REF_NO" jdbcType="VARCHAR" property="debtRefNo" />
    <result column="COD_UNICOM" jdbcType="DECIMAL" property="codUnicom" />
    <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName" />
    <result column="NUM_CHEQUE" jdbcType="VARCHAR" property="numCheque" />
    <result column="COD_CENCOBRO_BANK" jdbcType="DECIMAL" property="codCencobroBank" />
    <result column="EXPORTED" jdbcType="DECIMAL" property="exported" />
    <result column="EXPORTED_TS" jdbcType="DATE" property="exportedTs" />
  </resultMap>
</mapper>