package com.clou.esp.hes.app.web.core.timers;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.clou.esp.hes.app.web.service.data.DataIntegrityService;
import com.clou.esp.hes.app.web.service.demo.TestDataService;

@Component
public class Job {
	@Resource
	private TestDataService testDataService;
	@Resource
	private DataIntegrityService dataIntegrityService;

	// @Scheduled(cron = "*/10 * * * * *")
	// public void s10() {
	// System.out.println("定时任务测试===" + System.currentTimeMillis());
	// }
	//
	// @Scheduled(cron="0 */1 * * * *")
	// public void m1(){
	// org.jeecgframework.core.util.LogUtil.info("1m");
	// }
	@Scheduled(cron = "0 0 12 * * ?")
	public void statisticsIntegrity12() {
		// dataIntegrityService.statisticsIntegrity();
	}

	@Scheduled(cron = "0 0 8 * * ?")
	public void statisticsIntegrity8() {
		// dataIntegrityService.statisticsIntegrity();
	}
	
	/**
	 * 计算首页完整率
	 */
	@Scheduled(cron = "0 30 */4 * * ?")	//每4小时执行一次
	public void statisticsIntegrity4() {
//		dataIntegrityService.statisticsIntegrity();
	}

	@Scheduled(cron = "0 0 3 * * ?")
	public void threeOClockPerDay() {
		//testDataService.insertMeterEventTestData();
	}

	@Scheduled(cron = "0 0 2 * * ?")
	public void twoOClockPerDay() {
		//testDataService.saveMeterProgressMissData();
	}

	@Scheduled(cron = "0 0 1 * * ?")
	public void oneOClockPerDay() {
		//testDataService.insertMeterTestData();
	}

}