package com.clou.esp.hes.app.web.core.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class PwdUtil {
	public static boolean checkPassword(String password){
		Pattern Password_Pattern = Pattern.compile("^(?=.*[0-9])(?=.*[a-zA-Z])(.{8,20})$");
		Matcher matcher = Password_Pattern.matcher(password);
		if (matcher.matches()) {
		   return true;
		  }
           return false;
	 }
		
}
