package com.clou.esp.hes.app.web.core.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.model.system.SysUser;
import com.clou.esp.hes.app.web.service.system.SysOrgService;
import com.google.common.collect.Lists;
import com.power7000g.core.util.jqgrid.JqGridSearchTo;


/**
 * @ClassName: Tool
 * @Description: 辅助工具类
 * <AUTHOR>
 * @date 2018年7月11日 下午5:43:42
 *
 */
public class OrganizationUtils {

    //excludeParentflag false：除去本级     level，当级下的  0全级，1只展示下级
	public static List<String> assembleOrgIds(SysUser su,SysOrgService sysOrgService,boolean excludeParentflag,int level) {

				List<String> orgIdList = new ArrayList<String>();
				SysOrg sysOrg=new SysOrg();
				sysOrg.setId(su.getOrgId());
				SysOrg org = sysOrgService.get(sysOrg);
				
				SysOrg sysOrg1 = new SysOrg();
				sysOrg1.setParentOrgTid(su.getOrgId());
				List<SysOrg> list = sysOrgService.getListByparentOrgTid(sysOrg1);
				List<SysOrg> orgList = sysOrgService.getListByOrgCode(org);

				if(level == 1){
					for(SysOrg o : list){
						orgIdList.add(o.getId());
					}
					
					if(!excludeParentflag){
						orgIdList.add(org.getId());
					}
					
				}else if(level == 0){
					for(SysOrg o : orgList){
						if(excludeParentflag){
							if(su.getOrgId().equals(o.getId())){
								continue;
							}
						}
						orgIdList.add(o.getId());
					}	
				}

				if(orgIdList.size() == 0){
					orgIdList.add(su.getOrgId());
				}
				
				return orgIdList;
	}

	public static List<String> assembleOrgIds(String [] orgIds,SysOrgService sysOrgService,boolean excludeParentflag,int level) {
		List<String> orgIdList = new ArrayList<String>();
		SysOrg sysOrg=new SysOrg();
		sysOrg.setId(orgIds[0]);
		SysOrg org = sysOrgService.get(sysOrg);
		String defaultId= "";
		if(org.getParentOrgTid().equals("0")){
			defaultId = org.getId();
			SysOrg sysOrg1 = new SysOrg();
			sysOrg1.setParentOrgTid(defaultId);
			List<SysOrg> list = sysOrgService.getListByparentOrgTid(sysOrg1);
			List<SysOrg> orgList = sysOrgService.getListByOrgCode(org);

			if(level == 1){
				for(SysOrg o : list){
					orgIdList.add(o.getId());
				}

				if(!excludeParentflag){
					orgIdList.add(org.getId());
				}

			}else if(level == 0){
				for(SysOrg o : orgList){
					if(excludeParentflag){
						if(defaultId.equals(o.getId())){
							continue;
						}
					}
					orgIdList.add(o.getId());
				}
			}

			if(orgIdList.size() == 0){
				orgIdList.add(defaultId);
			}
		}else{
			for(String orgId : orgIds){
				defaultId = orgId;
				SysOrg sysOrg2=new SysOrg();
				sysOrg2.setId(orgId);
				SysOrg org2= sysOrgService.get(sysOrg2);

				SysOrg sysOrg1 = new SysOrg();
				sysOrg1.setParentOrgTid(defaultId);
				List<SysOrg> list = sysOrgService.getListByparentOrgTid(sysOrg1);
				List<SysOrg> orgList = sysOrgService.getListByOrgCode(org2);

				if(level == 1){
					for(SysOrg o : list){
						orgIdList.add(o.getId());
					}

					if(!excludeParentflag){
						orgIdList.add(org.getId());
					}

				}else if(level == 0){
					for(SysOrg o : orgList){
						if(excludeParentflag){
							if(defaultId.equals(o.getId())){
								continue;
							}
						}
						orgIdList.add(o.getId());
					}
				}

				if(orgIdList.size() == 0){
					orgIdList.add(defaultId);
				}
			}
		}


		return orgIdList;
	}

	//excludeParentflag false：除去本级     level，当级下的  0全级，1只展示下级
	public static List<String> assembleOrgIds(String orgId,SysOrgService sysOrgService,boolean excludeParentflag,int level) {

		List<String> orgIdList = new ArrayList<String>();
		SysOrg sysOrg=new SysOrg();
		sysOrg.setId(orgId);
		SysOrg org = sysOrgService.get(sysOrg);

		SysOrg sysOrg1 = new SysOrg();
		sysOrg1.setParentOrgTid(orgId);
		List<SysOrg> list = sysOrgService.getListByparentOrgTid(sysOrg1);
		List<SysOrg> orgList = sysOrgService.getListByOrgCode(org);

		if(level == 1){
			for(SysOrg o : list){
				orgIdList.add(o.getId());
			}

			if(!excludeParentflag){
				orgIdList.add(org.getId());
			}

		}else if(level == 0){
			for(SysOrg o : orgList){
				if(excludeParentflag){
					if(orgId.equals(o.getId())){
						continue;
					}
				}
				orgIdList.add(o.getId());
			}
		}

		if(orgIdList.size() == 0){
			orgIdList.add(orgId);
		}

		return orgIdList;
	}

	/**
	 * 接收从前台制定的orgId，如果为空，获取登录用户的id
	 */
	public static List<String> getOrgIds(SysOrgService sysOrgService,String orgId) {
			List<String> orgIds = Lists.newArrayList();
			if(StringUtils.isEmpty(orgId)) {
				SysUser su = TokenManager.getToken();
				orgId=su.getOrgId();
			}
			
			if(StringUtils.isNotEmpty(orgId)) {
				SysOrg sysOrg= sysOrgService.getEntity(orgId);
		    	if(sysOrg!=null) {
		    		String orgCode=sysOrg.getOrgCode();
		    		sysOrg.setOrgCode(orgCode);
		    		List<SysOrg> orgs = sysOrgService.getListByOrgCode(sysOrg);
		    		for(int i=0;i<orgs.size();i++) {
		    			orgIds.add(orgs.get(i).getId());
		    		}
		    	}
			}
    	return orgIds;
	}
	
	
	
	
	
	
	
	
}
