package com.clou.esp.hes.app.web.core.util;

import java.util.HashMap;
import java.util.Map;

import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;

import com.clou.esp.hes.app.web.model.system.SysServiceAttribute;
import com.clou.esp.hes.app.web.service.system.SysServiceAttributeService;
import com.power7000g.core.util.base.StringUtil;

public class UciInterfaceUtil{
	
	/**
	 * 接口列表
	 */
	private static Map<String,Object> interfaces=new HashMap<String, Object>();
	
	
	public static Map<String, Object> getInterfaces() {
		return interfaces;
	}

	public static void setInterfaces(Map<String, Object> interfaces) {
		UciInterfaceUtil.interfaces = interfaces;
	}



	public static Object getInterface(String interfaceName,Class cal,SysServiceAttributeService sysServiceAttributeService){
		String uciUrl = ResourceUtil.getSessionattachmenttitle("uci.service.address");
		if(!interfaces.containsKey(interfaceName)){
			return null;
		}
		String address=(String)interfaces.get(interfaceName);
		if(StringUtil.isEmpty(address)){
			return null;
		}
		if(sysServiceAttributeService==null){
			return null;
		}
		
        JaxWsProxyFactoryBean svr = new JaxWsProxyFactoryBean();
        svr.setServiceClass(cal);
        if(StringUtil.isNotEmpty(uciUrl)){
        	svr.setAddress(uciUrl + address);
        }else{
        	SysServiceAttribute entity=new SysServiceAttribute();
    		entity.setAttributeName("Application.UCI.Interface.Url");
        	SysServiceAttribute ssa = sysServiceAttributeService.get(entity);
    		if(ssa==null){
    			return null;
    		}
        	 svr.setAddress(ssa.getAttributeValue() + address);
        }
        return svr.create();
	}
}
