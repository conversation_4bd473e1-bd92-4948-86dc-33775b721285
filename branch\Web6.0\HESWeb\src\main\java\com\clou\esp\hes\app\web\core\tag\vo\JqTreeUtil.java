package com.clou.esp.hes.app.web.core.tag.vo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：JqTreeUtil.java
 * 版权：Copyright by Power7000g Team
 * 描述：树形结构列表操作类
 * 修改人：严浪
 * 修改时间：2017年3月15日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class JqTreeUtil {
    /**主键字段名称*/
    private String idFieldName;
    /**显示字段*/
    private String fields;
    /**子集列表字段名称*/
    private String subsetFieldName;
    /**父级字段名称*/
    private String parentFieldName;
    /**展开或关闭状态字段名称*/
    private String expandedFieldName;
    
    
    public String getIdFieldName() {
        return idFieldName;
    }
    public String getFields() {
        return fields;
    }
    public String getSubsetFieldName() {
        return subsetFieldName;
    }
    public String getParentFieldName() {
        return parentFieldName;
    }
    public String getExpandedFieldName() {
        return expandedFieldName;
    }
    /**
     * 主键字段名称
     */
    public void setIdFieldName(String idFieldName) {
        this.idFieldName = idFieldName;
    }
    /**
     * 显示字段
     */
    public void setFields(String fields) {
        this.fields = fields;
    }
    /**
     * 子集列表字段名称
     */
    public void setSubsetFieldName(String subsetFieldName) {
        this.subsetFieldName = subsetFieldName;
    }
    /**
     * 父级字段名称
     */
    public void setParentFieldName(String parentFieldName) {
        this.parentFieldName = parentFieldName;
    }
    /**
     * 展开或关闭状态字段名称
     */
    public void setExpandedFieldName(String expandedFieldName) {
        this.expandedFieldName = expandedFieldName;
    }
    
  
    /**
     * 
     * 返回树形列表结构数据
     * @param list
     * @return 
     * @see
     */
    public List<Map<String, Object>> getTreeGridData(List<?> list){
        return this.getTreeGridData(list, 0);
    }
    
    private List<Map<String, Object>> getTreeGridData(List<?> list,int level){
        List<Map<String, Object>> tgList=new ArrayList<>();
        if(list!=null&&list.size()>0)
        for(Object o:list){
            JqTreeGrid jtg=new JqTreeGrid();
            jtg.setLevel(level);
            Object id=TagUtil.fieldNametoValues(StringUtil.isNotEmpty(this.idFieldName)?this.idFieldName:"id", o);
            jtg.setId(id!=null?(String)id:"");
            Object parent=TagUtil.fieldNametoValues(StringUtil.isNotEmpty(this.parentFieldName)?this.parentFieldName:"parent", o);
            jtg.setParent(parent!=null?(String)parent:"");
            if(StringUtil.isNotEmpty(this.expandedFieldName)){
                Object expanded=TagUtil.fieldNametoValues(this.expandedFieldName, o);
                if(expanded!=null&&expanded instanceof Boolean){
                    jtg.setExpanded((boolean)expanded);
                }
            }
            if(StringUtil.isNotEmpty(this.fields)){
                jtg.setMapParam(this.fields.split(","), o);
            }
            Object subObj=TagUtil.fieldNametoValues(StringUtil.isNotEmpty(this.subsetFieldName)?this.subsetFieldName:"list", o);
            if(subObj!=null&&subObj instanceof List){
                if(((List)subObj).size()<=0){
                    jtg.setLeaf(true);
                }
               tgList.add(jtg.getMapPatam());
               tgList.addAll(getTreeGridData((List)subObj,level+1));
            }else{
                jtg.setLeaf(true);
                tgList.add(jtg.getMapPatam());
            }
        }
        return tgList;
    }
    
 

}
