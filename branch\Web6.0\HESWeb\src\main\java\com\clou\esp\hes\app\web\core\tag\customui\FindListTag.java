package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.tag.customui.util.TagUtil;
import com.clou.esp.hes.app.web.core.tag.vo.ExpUtl;
import com.clou.esp.hes.app.web.core.tag.vo.FindListColumn;
import com.clou.esp.hes.app.web.core.tag.vo.FindListExp;
import com.clou.esp.hes.app.web.core.tag.vo.FindListOpt;
import com.clou.esp.hes.app.web.core.tag.vo.OptTypeDirection;
import com.clou.esp.hes.app.web.core.tag.vo.TreeTypeDirection;
import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.power7000g.core.util.base.DateUtils;
import com.power7000g.core.util.base.StringUtil;

/**
 * 文件名：FindListDataTag.java
 * 版权：Copyright by Power7000g Team
 * 描述：查询列表展示查询标签
 * 修改人：严浪
 * 修改时间：2017年3月1日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FindListTag  extends TagSupport {
    /**
     * 意义，目的和功能，以及被用到的地方<br>
     */
    private static final long serialVersionUID = 1L;
    protected String fields = "";// 显示字段
    protected String searchFields = "";// 查询字段  
    protected String searchString=""; //查询类型
    protected String name;// 表格标示
    protected String title;// 表格标示
    protected String idField="id";// 主键字段
    protected boolean treegrid = false;// 是否是树形列表
    protected String expandColumn="id";  //可展开列名称
    protected boolean isOpt=false; //是否行操作按钮
    protected boolean isQuery=false; //是否有查询
    protected boolean isToBar=false; //是否有操作按钮
    protected boolean rownumbers=false; //是否显示行序号
    protected FindListColumn opt;
    protected StringBuffer sele=new StringBuffer();
    
    protected List<FindListColumn> columnList = new ArrayList<FindListColumn>();// 列表操作显示
    
    protected List<FindListOpt>  toolBarList=new ArrayList<FindListOpt>();  //头部操作按钮
    
    protected List<FindListOpt> optList =new ArrayList<FindListOpt>();  //列表操作按钮
    
    
    public Map<String, Object> map;// 封装查询条件
    private String actionUrl;// 分页提交路径
    private String editurl; //编辑提交URL
    public int pageSize = 10;
    public boolean pagination = true;// 是否显示分页
    private String width;
    private String height;
    private String editHeight;
    private boolean checkbox = false;// 是否显示复选框
    private String sortName;//定义的列进行排序
    private String sortOrder = "asc";//定义列的排序顺序，只能是"递增"或"降序".
    private boolean showRefresh = true;// 定义是否显示刷新按钮
    private String onLoadSuccess;// 数据加载完成调用方法
    private String onClick;// rowid,iCol,cellcontent,e  当点击单元格时触发。rowid：当前行id；iCol：当前单元格索引；cellContent：当前单元格内容；e：event对象
    private String onDblClick;//rowid,iRow,iCol,e 双击行时触发。rowid：当前行id；iRow：当前行索引位置；iCol：当前单元格位置索引；e:event对象
    private String langArg;
    private String defaults="";
    private String beforeInquiries;  //点击查询前处理事件
    private String beforeRequest; //向服务器请求前调取方法
    private String beforeSelectRow;
    private boolean shrinkToFit=false; //此属性用来说明当初始化列宽度时候的计算类型，如果为ture，则按比例初始化列宽度。如果为false，则列宽度使用t:fgCol指定的宽度 默认:false
    private boolean autowidth=true; //默认值为true。则Grid的宽度会根据父容器的宽度自动重算。重算仅发生在Grid初始化的阶段；
    private String queryDataFun; //自定义查询参数方法，该方法必须一个对象，如:{"name":"test"}
    private String loadComplete; //当从服务器返回响应时执行，xhr：XMLHttpRequest 对象
    private boolean isOneRow=true;
    private String beforeLoading; //jqgrid加载前执行方法，返回data则将其拼接到查询条件里面去
    private String afterLoading; //jqgrid加载后执行方法,只执行一次 
    private String extParam;	//扩展参数
    
    public void setColumn(String name, String title, String langArg, String classes,
                          boolean hidden, String align, Integer width, String formatter,
                          boolean sortable, String queryMode, String replace, String url,
                          String dictionary, String valType,String imageSize,boolean frozenColumn,
                          String formatterjs,boolean query,String defaults,String queryHtml,
                          String cellattr,boolean isWrap,boolean editable,String edittype,String editoptions,String seleDef,String splitStr,String channel,boolean isKey,boolean isOnly){
        FindListColumn flc=new FindListColumn(name, title, langArg, classes, hidden, query, align, width, 
        		formatter, formatterjs, sortable, queryMode, replace, url, dictionary,
        		valType, imageSize, frozenColumn, defaults, queryHtml, cellattr,isWrap,
        		editable,edittype,editoptions,seleDef,splitStr,channel,isKey,isOnly);
        if(query){
            isQuery=query;
            isToBar=true;
            if(isWrap){
            	isOneRow=false;
            }
        }
        if("opt".equals(name)){
            opt=flc;
            isOpt=true;
        }else{
            fields= StringUtil.isEmpty(fields)?flc.getName():fields+","+flc.getName();
            if(StringUtil.isNotEmpty(url)){
                String[] dty=url.split("#");
                if(dty.length==3){
                    sele.append("var "+name+"Map={};");
                    sele.append("$(function() {"+name+"fun();});");
                    sele.append("var "+name+"fun=function(){  if("+this.name+"isLoad){"+this.name+"isLoad=false;");
                    sele.append("   $.ajax({");
                    sele.append("       type: 'POST',");
                    sele.append("       url: '"+dty[0]+"',");
                    sele.append("       dataType: 'json',");
                    sele.append("       success: function(data){");
                    sele.append("       if(data.success){");
                    sele.append("          for(var i=0;i<data.obj.length;i++){");
                    sele.append("          "+name+"Map[data.obj[i]."+dty[1]+"] =data.obj[i]."+dty[2]+";");
                    sele.append("          }");
                    sele.append("        }else{}");
                    sele.append(""+this.name+"isLoad=true;");
                    sele.append("     },error: function (msg) {");
                    sele.append(""+this.name+"isLoad=true;");
                    sele.append("      }");
                    sele.append("    });");
                    sele.append("}else{setTimeout(function(){"+name+"fun();}, 100);}};");
                }
            }else if(StringUtil.isNotEmpty(dictionary)){
            	 sele.append("var "+name+"Map={};");
                 sele.append("$(function() {"+name+"fun();});");
                 sele.append("var "+name+"fun=function(){  if("+this.name+"isLoad){"+this.name+"isLoad=false;");
                 sele.append("   $.ajax({");
                 sele.append("       type: 'POST',");
                 sele.append("       url: getRootPathWeb()+'/sysTypegroupController/getSelete.do?dictionary="+dictionary+"',");
                 sele.append("       dataType: 'json',");
                 sele.append("       success: function(data){");
                 sele.append("       if(data.success){");
                 sele.append("          for(var i=0;i<data.obj.length;i++){");
                 sele.append("          "+name+"Map[data.obj[i].typecode] =i18n.t(data.obj[i].i18nTypename);");
                 sele.append("          }");
                 sele.append("        }else{}");
                 sele.append(""+this.name+"isLoad=true;");
                 sele.append("     },error: function (msg) {");
                 sele.append(""+this.name+"isLoad=true;");
                 sele.append("      }");
                 sele.append("    });");
                 sele.append("}else{setTimeout(function(){"+name+"fun();}, 100);}};");
            }else if(StringUtil.isNotEmpty(replace)){
                String[] reps=replace.split(splitStr);
                sele.append("var "+name+"Map={};");
                for(String rep:reps){
                	if(StringUtil.isNotEmpty(rep)){
                		String key=rep.substring(0,rep.indexOf(":"));
                		String value=rep.substring(rep.indexOf(":")+1, rep.length());
                		value=MutiLangUtil.doMutiLang(value);
                		sele.append("          "+name+"Map['"+key+"'] ='"+value+"';");
                	}
                }
               
            }
        }
        columnList.add(flc);
    }
    
    
    public void setFunOpt(String title, String icon,String operationCode, String exp,
                          String funname){
        FindListOpt flo=  new FindListOpt(title, icon, OptTypeDirection.Fun, exp, funname);
        //权限操作
        optList.add(flo);
    }
    
    public void setToolBar(String url,String operationCode, String title, String icon,String width, String height,
                           String funname, String onclick){
        FindListOpt flo=  new FindListOpt(url, title, icon, OptTypeDirection.ToolBar, width, height, funname, onclick);
        isToBar=true;
        //权限操作
        toolBarList.add(flo);
    }
    
    public void setConfOpt(String url,String operationCode, String title,String exp, String icon,String message){
        FindListOpt flo=  new FindListOpt(url, title, exp, icon, OptTypeDirection.Confirm, message);
        //权限操作
        optList.add(flo);
    }
    
    public void setDefOpt(String url,String operationCode, String title, String icon, String exp){
        FindListOpt flo=  new FindListOpt(url, title, icon, OptTypeDirection.Deff, exp);
        //权限操作
        optList.add(flo);
    }
    
    
    public void setDelOpt(String url, String title, String icon,String  operationCode,
                          String message, String exp, String funname){
        FindListOpt flo=  new FindListOpt(url, title, icon, OptTypeDirection.Del, message, exp, funname);
        //权限操作
        optList.add(flo);
    }
    
    public void setOpenOpt(String url,String operationCode, String title, String icon, String width, String height,
                        String openModel, String exp){
        
        FindListOpt flo=  new FindListOpt(url, title, icon, width, height, OptTypeDirection.valueOf(openModel), exp);
        //权限操作
        optList.add(flo);
    }
    
    
    public void setSlidesOut(String title, String exp, String langArg, String icon,
			String url,String proportion){
    	FindListOpt flo=new FindListOpt( url,  title,  icon, OptTypeDirection.SidesOut, exp, proportion);
    	optList.add(flo);
    }
    
    
    
    public int doStartTag() throws JspTagException {
        return EVAL_PAGE;
    }
   /* public static void getNewFile(String templateName,Map<String, Object> paramMap,String fileName){
        String filePath="C:\\Users\\<USER>\\Documents\\test\\";
        String filePathSub="D:\\MyWorkspaces\\CodeGeneration\\template";
        try {
            File file =new File(filePath);    
          //如果文件夹不存在则创建    
          if  (!file .exists()  && !file .isDirectory()){       
              file.mkdirs();   
          } 
            Configuration configuration = new Configuration();  
            configuration.setDirectoryForTemplateLoading(new File(filePathSub));
            configuration.setObjectWrapper(new DefaultObjectWrapper());  
           // configuration.setDefaultEncoding("UTF-8");   //这个一定要设置，不然在生成的页面中 会乱码  
            //获取或创建一个模版。  
            Template template = configuration.getTemplate(templateName); 
            Writer writer  = new OutputStreamWriter(new FileOutputStream(filePath+fileName),"UTF-8");  
            template.process(paramMap, writer);  
        }catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (TemplateException e) {  
            e.printStackTrace();  
        }    
    }*/
    public int doEndTag() throws JspException {
        JspWriter out = null;
        title=MutiLangUtil.doMutiLang(title);
        try {
        	/* List<Map<String,Object>> tableList=new ArrayList<Map<String,Object>>();
        	 List<Map<String,Object>> paramList=new ArrayList<Map<String,Object>>();
        	for(FindListColumn c:columnList){
        		 Map<String,Object> pm=new HashMap<String,Object>();
        		 pm.put("jName", c.getName());
        		 pm.put("jComment", c.getTitle());
        		 paramList.add(pm);
        	}
        	Map<String,Object> m=new HashMap<String,Object>();
        	m.put("paramList", paramList);
        	m.put("jPname", name);
        	m.put("jComment", title);
        	tableList.add(m);
        	 Map<String,Object> sm=new HashMap<String,Object>();
        	 sm.put("tableList", tableList);
             getNewFile("power7000.jsp.json.ftl", sm, name+".json");*/
            out = this.pageContext.getOut();
     //       if (style.equals("easyui")) {
                    out.print(end().toString());
                    out.flush();
           /* }else{
                out.print(datatables().toString());
                out.flush();
            }*/
        } catch (IOException e) {
            e.printStackTrace();
        }finally{
            if(out!=null){
                try {

                    out.clearBuffer();
                    end().setLength(0);
                    // 清空资源
                    columnList.clear();
                    toolBarList.clear();
                    columnList.clear();
                    optList.clear();
                    isOpt=false;
                    isQuery=false;
                    isToBar=false;
                    sele.setLength(0);
                    fields = "";
                    searchFields = "";
                    searchString="";
                    defaults="";
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            
        }
        return EVAL_PAGE;
    }


    private StringBuffer end() {
        StringBuffer sb = new StringBuffer();
        width = (StringUtil.isEmpty(width)) ? "auto" : width;
        if(StringUtil.isNotEmpty(height)&&height.indexOf("auto")>=0&&height.length()>4){
        	editHeight=height.replace("auto", "");
        	height="auto";
        }else{
        	height = (StringUtil.isEmpty(height)) ? "auto" : height;
        }
        		if(isQuery){
        			if(isOneRow){
        				sb.append("<div class=\"col-md-12 margin_bottom10\" id=\"content_row_"+name+"\">");
        			}else{
        				sb.append("<div class=\"col-md-12 \" id=\"content_row_"+name+"\">");
        			}
	                sb.append(getQueryConditions()); //这里面放查询过滤条件
	                sb.append("<div class=\"col-md-2 f_right  \">");
	                if(StringUtil.isNotEmpty(searchFields)){
	                	sb.append("<button type=\"button\" class=\"btn search_btn  space_h_lable \" title=\"Reset\" id=\""+name+"_resetBtn\"><i class=\"fa fa-refresh\"></i></button>");
	                	sb.append("<button type=\"button\" class=\"btn search_btn  space_h_lable \" title=\"Search\" id=\""+name+"_queryBtn\" onclick=\""+name+"searchOnEnterFn()\"><i class=\"fa fa-search\"></i></button>");
	                	/*if(!isOneRow){
	                		sb.append("<button type=\"button\" class=\"btn search_btn \" id=\""+name+"_resetBtn\"><i class=\"fa fa-refresh\"></i></button>");
	                		sb.append("<button type=\"button\" class=\"btn search_btn \" id=\""+name+"_queryBtn\" onclick=\""+name+"searchOnEnterFn()\"><i class=\"fa fa-search\"></i></button>");
	                	}else{
	                	}*/
	                    if(isQuery){
	                        sb.append(" <script type=\"text/javascript\">");
	                        sb.append("var isOpenCollapse=true;");
	                        sb.append("var bodyHeight=document.body.scrollHeight;");
	                        sb.append("var rowHeight=null;");
	                        sb.append("var rowDiv=window.document.getElementById('content_row_"+name+"');");
	                        sb.append(" $(function() {　");
	                        sb.append("    $(\"#"+name+"_collapse_id\").each(function() {");
	                        sb.append("if(rowDiv!=null){");
	                        sb.append("var rowHeight=rowDiv.offsetHeight;");
	                        sb.append("}");
	                        sb.append("        $(this).click(function() {　");
	                        sb.append("           $(\"#"+name+"_queryBtn\").toggle();");
	                        sb.append("            $(\"#"+name+"_resetBtn\").toggle();");
	                        sb.append("if(isOpenCollapse){");
	                        sb.append("isOpenCollapse=false;");
	                        sb.append(""+name+"jqgridHeight=bodyHeight-230;");
	                        sb.append(""+name+"startrun("+name+"jqgridHeight);");
	                        sb.append("}else{");
	                        sb.append("isOpenCollapse=true;");
	                        sb.append(""+name+"jqgridHeight=bodyHeight-rowHeight-195;");
	                        sb.append(""+name+"startrun("+name+"jqgridHeight);");
	                        sb.append("}");
	                        sb.append("        });");
	                        sb.append("    });");
	                        sb.append(" });");
	                        sb.append(" function "+name+"startrun(endVal){");
	                        sb.append("$(\"div#gview_"+name+" div.ui-jqgrid-bdiv\").animate({height:endVal},'fast');");
	                       // sb.append("$(\"#"+name+"\").setGridHeight(endVal);");
	                    	sb.append("}");
	                        sb.append(" </script>");
	                    }
	                }
	                sb.append("</div>");
	                sb.append("</div>");
                }
                sb.append("<div class=\"jqGrid_wrapper blue\" id=\""+name+"_content\">");
                sb.append("<table id=\""+name+"\"></table>");
                    sb.append("<div id=\"pager_"+name+"\">");
                    sb.append("</div>");
                sb.append("</div>");
    sb.append("<script type=\"text/javascript\">");
  
    sb.append("var "+name+"isLoad=true;");
    sb.append("var "+name+"jqgridHeight='"+height+"';");
    sb.append(sele);
    sb.append("   layer.load();");
    sb.append("  $(function() {");
    sb.append("var bodyHeight=document.body.scrollHeight;");
    sb.append("var rowDiv=window.document.getElementById('content_row_"+name+"');");
    sb.append("if(rowDiv!=null){");
    sb.append("var rowHeight=rowDiv.offsetHeight;");
    sb.append("if("+name+"jqgridHeight=='auto'){");
    sb.append(""+name+"jqgridHeight=(bodyHeight-rowHeight-150)"+(StringUtil.isNotEmpty(editHeight)?editHeight:"")+";");
    sb.append("}}else{");
    sb.append("if("+name+"jqgridHeight=='auto'){");
    sb.append(""+name+"jqgridHeight=(bodyHeight-150)"+(StringUtil.isNotEmpty(editHeight)?editHeight:"")+";");
    sb.append("}}");
    sb.append(""+name+"fun();");
    sb.append("  });     ");
    sb.append("var "+name+"fun=function(){  if("+name+"isLoad){"+name+"isLoad=false;");
    sb.append("$.jgrid.defaults.styleUI = \"Bootstrap\";");
    sb.append(getHeadQueryOpt());
    if(StringUtil.isNotEmpty(beforeLoading)){
    	sb.append("var "+name+"queryData="+beforeLoading+"();");
    	sb.append("if("+name+"queryData){");
    	sb.append(""+name+"headData= Object.assign("+name+"headData, "+name+"queryData);\n");
    	sb.append("}");
    }
    sb.append("$(\"#"+name+"\").jqGrid({ ");
    //是否为树形结构
    if(treegrid){
        sb.append(" treeGrid: "+treegrid+", ");   //是否树形
        sb.append(" treeGridModel: 'adjacency',"); //treeGrid模式，跟json元数据有关 ,adjacency/nested  
        sb.append("  ExpandColumn : '"+expandColumn+"',"); 
        sb.append(" ExpandColClick:true,");
        sb.append(" jsonReader: {"); //读取后端json数据的格式
        sb.append("     repeatitems: false"); 
        sb.append(" },"); 
    }else{
        sb.append("sortname:'"+sortName+"',");
        sb.append("sortorder:'"+sortOrder+"',");
        sb.append("   rownumbers: "+rownumbers+",  ");//行序号
    }
    if(StringUtil.isNotEmpty(beforeRequest)){
    	sb.append("beforeRequest: "+beforeRequest+",");//向服务器请求前调取方法
    }
    if(StringUtil.isNotEmpty(loadComplete)){
    	sb.append("loadComplete:"+loadComplete+","); //当从服务器返回响应时执行，xhr：XMLHttpRequest 对象
    }
    if(StringUtil.isNotEmpty(beforeSelectRow)){
    	sb.append("beforeSelectRow:"+beforeSelectRow+",");
    }
    sb.append("altRows: true,");//单双行样式不同  
    sb.append("altclass: 'differ',"); //
    sb.append("url:'"+actionUrl+"',");
    if(StringUtil.isNotEmpty(editurl)){
    	sb.append("editurl:'"+editurl+"',");
    }
    sb.append("mtype:\"post\",");
    sb.append("  datatype: \"json\",");
    sb.append("   height: "+name+"jqgridHeight,");
    sb.append("   width: \""+width+"\",");
    sb.append("   autowidth: "+autowidth+",");
    sb.append("   multiselect: "+checkbox+", ");//复选框 
    if(checkbox){
    	sb.append("multiselectWidth:'29px',");
    }
    sb.append("colNames: [");
    sb.append(getColNames());//这里插入列头部
    sb.append("],");
    sb.append("colModel: [");
    sb.append(getColModel());//这里插入列详细数据
    sb.append("],");
    if(pagination){
    	sb.append("pager: \"#pager_"+name+"\",");
    	sb.append("   rowNum: "+pageSize+",");
    	sb.append("   rowList: [" + pageSize * 1 + "," + pageSize * 2 + "," + pageSize * 3 +"," + pageSize * 4 +"," + pageSize * 5 + "],");
    }else{
    	if(showRefresh)
    	sb.append("pager: \"#pager_"+name+"\",");
    	sb.append("   rowNum: 99999,");
    	sb.append("   pagerpos: false,");
    	sb.append("   pgbuttons: false,");
    }
    sb.append("   viewrecords: true,");  //是否显示总记录数
    if(StringUtil.isNotEmpty(extParam)){
    	sb.append(extParam);
    }
    if(title!=null)
    if(StringUtil.isNotEmpty(title)){
    	sb.append("   caption: \""+title+"\",");
    }else{
    	sb.append("   caption: \"&nbsp;\",");
    }
    sb.append("shrinkToFit:"+shrinkToFit+",  ");
    sb.append("autoScroll: true,");
    sb.append(" postData:"+name+"headData,");
    sb.append(StringUtil.isNotEmpty(onLoadSuccess)?("gridComplete:"+onLoadSuccess+","):"");
    sb.append(StringUtil.isNotEmpty(onClick)?("onCellSelect:"+onClick+","):"");
    sb.append(StringUtil.isNotEmpty(onDblClick)?("ondblClickRow:"+onDblClick+","):"");
    sb.append("});");
    if(showRefresh){
    	//刷新按钮保留
    	sb.append("$(\"#"+name+"\").jqGrid(\"navGrid\", \"#pager_"+name+"\", {");
    	sb.append("   edit: false,");
    	sb.append("   add: false,");
    	sb.append("    del: false,");
    	sb.append("    search: false");
    	sb.append(" }, {");
    	sb.append("    height: 200,");
    	sb.append("    reloadAfterSubmit: true");
    	sb.append("});");
    }
    //添加头部按钮
    if(toolBarList!=null&&toolBarList.size()>0){
    	sb.append(getHeadOpeBtn());
    }
    //固定列
    sb.append(" jQuery(\"#"+name+"\").jqGrid('setFrozenColumns');");
    //加滚动条
    sb.append("$(\"#"+name+"\").closest(\".ui-jqgrid-bdiv\").css({ \"overflow-x\" : \"scroll\" }); "); 
    sb.append(" $(\"#"+name+"\").closest(\".ui-jqgrid-bdiv\").css({ \"overflow-y\" : \"scroll\" }); ");
    //tab切换
    sb.append("$('a[data-toggle=\"tab\"]').on('shown.bs.tab', function(e) {");
    sb.append("     var width = $(\"#gbox_"+name+"\").parent().width();");
    sb.append(" if(width>0){");
    sb.append("    $(\"#"+name+"\").setGridWidth(width);");
    sb.append("}");
    if(StringUtil.isNotEmpty(height)&&height.equals("auto")){
    	sb.append("var bodyHeight1=document.body.scrollHeight;");
    	sb.append("var rowDiv1=window.document.getElementById('content_row_"+name+"');");
        sb.append("if(rowDiv1!=null){");
        sb.append("var rowHeight1=rowDiv1.offsetHeight;");
        sb.append(""+name+"jqgridHeight=(bodyHeight1-rowHeight1-150)"+(StringUtil.isNotEmpty(editHeight)?editHeight:"")+";");
        sb.append("}else{");
        sb.append(""+name+"jqgridHeight=(bodyHeight1-150)"+(StringUtil.isNotEmpty(editHeight)?editHeight:"")+";");
        sb.append("}");
        sb.append("    $(\"#"+name+"\").setGridHeight("+name+"jqgridHeight);");
    }
    sb.append("});");
    //根据窗口大小调整页面
    sb.append(" $(window).bind(\"resize\", function() {");
    sb.append("     var width = $(\"#gbox_"+name+"\").parent().width();");
    sb.append(" if(width>0){");
    sb.append("    $(\"#"+name+"\").setGridWidth(width);");
    sb.append("}");
    if(StringUtil.isNotEmpty(height)&&height.equals("auto")){
    	sb.append("var bodyHeight1=document.body.scrollHeight;");
    	sb.append("var rowDiv1=window.document.getElementById('content_row_"+name+"');");
        sb.append("if(rowDiv1!=null){");
        sb.append("var rowHeight1=rowDiv1.offsetHeight;");
        sb.append(""+name+"jqgridHeight=(bodyHeight1-rowHeight1-150)"+(StringUtil.isNotEmpty(editHeight)?editHeight:"")+";");
        sb.append("}else{");
        sb.append(""+name+"jqgridHeight=(bodyHeight1-150)"+(StringUtil.isNotEmpty(editHeight)?editHeight:"")+";");
        sb.append("}");
        sb.append("    $(\"#"+name+"\").setGridHeight("+name+"jqgridHeight);");
    }
    sb.append(" });");
    sb.append(" jQuery(\"#"+name+"_resetBtn\").click(function() {");
    sb.append(getResetOpt());//刷新按钮操作代码
    sb.append("           "+name+"searchOnEnterFn();");
    sb.append("  }); ");
    if(StringUtil.isNotEmpty(afterLoading)){
    	sb.append(afterLoading+"();");
    }
    sb.append("   "+name+"isLoad=true;");
    sb.append("   layer.closeAll('loading');");
    sb.append("}else{setTimeout(function(){"+name+"fun();}, 100);}};");
    sb.append("function "+name+"searchOnEnterFn(){");
    if(StringUtil.isNotEmpty(beforeInquiries)){
    	sb.append("if(!"+beforeInquiries+"()){return;}");
    }
    sb.append(getQueryOpt());//查询按钮操作代码
    sb.append("}");
    sb.append("function "+name+"optFormatter(cellvalue, options, rowObject){");
    sb.append(getOptBtn());//添加按钮
    sb.append("}");
    sb.append("</script>");
        return sb;
    }
    /**
     * 查询条件封装
     * @return 
     * @see
     */
    private StringBuffer getQueryConditions(){
        StringBuffer sb = new StringBuffer();
        for(FindListColumn f:columnList){
            if("opt".equals(f.getName())){
                continue;
            }
           
            if(f.isQuery()){
                searchFields=StringUtil.isEmpty(searchFields)?f.getName():searchFields+","+f.getName();
                searchString=StringUtil.isEmpty(searchString)?f.getQueryMode():searchString+","+f.getQueryMode();
                if(f.isWrap()){
                	sb.append("</div>");
                	sb.append("<div class=\"col-md-12 margin_bottom10\" >");
                }
                // single单字段查询；scope范围查询
                if("single".equals(f.getQueryMode())){
                	if(StringUtil.isNotEmpty(f.getQueryHtml())){
                		sb.append(f.getQueryHtml());
                	}else if(StringUtil.isNotEmpty(f.getUrl())){
                    	String[] dty=f.getUrl().split("#");
                    	 if(dty.length==3){
                            sb.append("<div class=\"col-md-2\">");
                            sb.append(" <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                            sb.append(" <select  class=\"hes_select\" id=\""+f.getName()+"_txt\" >");
                            if(StringUtil.isNotEmpty(f.getSeleDef())){
                            	sb.append("<option value=\"\">"+f.getSeleDef()+"</option>");
                            }
                            sb.append("</select>");
                            sele.append("$(function() {"+f.getName()+"ofun();});");
                            sele.append("var "+f.getName()+"ofun=function(){  if("+name+"isLoad){"+name+"isLoad=false;");
                            sele.append("   for(var prop in "+f.getName()+"Map){");
                            sele.append("     if("+f.getName()+"Map.hasOwnProperty(prop)){");
                            if(StringUtil.isNotEmpty(f.getDefaults())){
                            	sele.append("   if(prop=='"+f.getDefaults()+"'){");
                            	sele.append("          $(\"#"+f.getName()+"_txt\").append(\"<option value='\"+prop+\"'>\"+"+f.getName()+"Map[prop]+\"</option>\");");
                            	sele.append("   }else{");
                            	sele.append("          $(\"#"+f.getName()+"_txt\").append(\"<option value='\"+prop+\"'>\"+"+f.getName()+"Map[prop]+\"</option>\");");
                            	sele.append("    }");
                            }else{
                            	sele.append("          $(\"#"+f.getName()+"_txt\").append(\"<option value='\"+prop+\"'>\"+"+f.getName()+"Map[prop]+\"</option>\");");
                            }
                            sele.append("        }");
                            sele.append("   }");
                            sele.append("  $('#"+f.getName()+"_txt').chosen({");
                            sele.append("      max_selected_options:1");
                            sele.append("   });");
                            sele.append("   "+name+"isLoad=true;");
                            sele.append("}else{setTimeout(function(){"+f.getName()+"ofun();}, 100);}};");
                            sb.append(" </div>");
                        }else if(dty.length==4){
                        	ChooseTag chooseTag=new ChooseTag();
                        	chooseTag.setHeight("70%");
                        	chooseTag.setWidth("60%");
                        	chooseTag.setUrl(dty[0]);
                        	chooseTag.setHiddenid(dty[1]);
                        	chooseTag.setHiddenName(dty[2]);
                        	chooseTag.setInputId(dty[3]);
                        	chooseTag.setTitle(f.getTitle());
                        	chooseTag.setTextid(f.getName()+"_txt");
                        	chooseTag.setTextname(f.getName()+"_text_txt");
                            sb.append("<div class=\"col-md-2\">");
                            sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                            sb.append("<input type=\"text\" class=\"hes_input\" id=\""+f.getName()+"_text_txt\" disabled=\"disabled\" />");
                            if(StringUtil.isNotEmpty(f.getDefaults())){
                            	sb.append("<input type=\"hidden\" class=\"hes_input\" id=\""+f.getName()+"_txt\" value=\""+f.getDefaults()+"\" />");
                            }else{
                            	sb.append("<input type=\"hidden\" class=\"hes_input\" id=\""+f.getName()+"_txt\" />");
                            }
                            sb.append(chooseTag.end());
                            sb.append(" </div>");
                        }else if(dty.length==5){
                    		JqueryZtreeTag jzt=new JqueryZtreeTag();
                    		jzt.setId(f.getName()+"_zTree");
                    		jzt.setUrl(dty[0]);
                    		jzt.setTreeType(TreeTypeDirection.getTreeType(dty[1]));
                    		if(StringUtil.isNotEmpty(dty[2])){
                    			if(dty[2].equals("true")){
                    				jzt.setIsDload(true);
                    			}else{
                    				jzt.setIsDload(false);
                    			}
                    		}
                    		if(StringUtil.isNotEmpty(dty[3])&&!dty[3].equals("no")){
                    			jzt.setOnClick(dty[3]);
                    		}
                    		 sb.append("<div class=\"col-md-2 chosen-container\">");
                    		 sb.append("<lable class=\"hes_lable\">"+f.getTitle()+"</lable>");
                    		 sb.append("<div class=\"hes_input\" id=\""+f.getName()+"chosen-contral\">");
                    		 if(StringUtil.isNotEmpty(f.getDefaults())){
                    			 String[] strs=f.getDefaults().split(f.getSplitStr());
                        		 sb.append("<span id=\""+f.getName()+"_view_txt\" >Choose "+strs.length+"</span>");
                    		 }else{
                    			 sb.append("<span id=\""+f.getName()+"_view_txt\" ></span>");
                    		 }
                    		 sb.append("	<i id=\""+f.getName()+"fa-chevron-circle-down\" class=\"fa fa-chevron-circle-down\"></i> ");
                    		 sb.append("	<i id=\""+f.getName()+"fa-chevron-circle-up\"	class=\"fa fa-chevron-circle-up\"></i>");
                    		 if(StringUtil.isNotEmpty(f.getDefaults())){
                    			 sb.append("    <input type=\"hidden\" id=\""+f.getName()+"_txt\" value=\""+f.getDefaults()+"\" > ");
                    		 }else{
                    			 sb.append("    <input type=\"hidden\" id=\""+f.getName()+"_txt\" > ");
                    		 }
                    		 sb.append("</div>");
                    		 sb.append("<div id=\""+f.getName()+"chosen-drop\"  class=\"chosen-drop\">");
                    		 sb.append("	<div class=\"input-group \">");
                    		 sb.append("		<input type=\"text\" class=\"hes_search_input \" id=\""+f.getName()+"_search_input_id\" ");
                    		 sb.append("			placeholder=\"Search for...\"> <span class=\"input-group-btn\">");
            				 sb.append("			<button class=\"hes_btn green\" onclick=\""+f.getName()+"RefreshzTree();\" type=\"button\">");
            				 sb.append("				<i class=\"fa fa-search\"></i>");
            				 sb.append("			</button>");
            				 sb.append("			</span>");
            				 sb.append("		</div>");
            				 if(StringUtil.isNotEmpty(f.getDefaults())){
            					 jzt.setLoadingComp(f.getName()+"loadingComp");
            				 }
            				 sb.append(jzt.end());
            				 sb.append(" <script type=\"text/javascript\"> ");
            				 if(StringUtil.isNotEmpty(f.getDefaults())){
                    			 String[] strs=f.getDefaults().split(f.getSplitStr());
                    			 sb.append("	function "+f.getName()+"loadingComp(){");
                    			 sb.append("   var "+f.getName()+"TreeObj = $.fn.zTree.getZTreeObj(\""+f.getName()+"_zTree\");");
                    			 for(int i=0;i<strs.length;i++){
                    				 sb.append(" var "+f.getName()+"node"+i+" = "+f.getName()+"TreeObj.getNodeByParam(\"id\", \""+strs[i]+"\");");
                    				 sb.append(""+f.getName()+"TreeObj.checkNode("+f.getName()+"node"+i+", true, true);");
                    				 sb.append(""+f.getName()+"TreeObj.selectNode("+f.getName()+"node"+i+", false);");
                    			 }
                    			 sb.append("	}");
        		            }
            				 sb.append("$(\"#"+f.getName()+"chosen-contral\").click(function() {");
            				 sb.append(""+f.getName()+"openClosChose();");
            				 sb.append("	});");
            				 sb.append("	function "+f.getName()+"openClosChose(){");
            				 sb.append("		$(\"#"+f.getName()+"chosen-drop\").toggle();");
            				 sb.append("		$(\"#"+f.getName()+"fa-chevron-circle-down\").toggle();");
            				 sb.append("		$(\"#"+f.getName()+"fa-chevron-circle-up\").toggle();");
            				 sb.append("	}");
            				 sb.append("	function "+f.getName()+"clearChose(){");
            				 sb.append("   var "+f.getName()+"TreeObj = $.fn.zTree.getZTreeObj(\""+f.getName()+"_zTree\");");
            				 sb.append("   "+f.getName()+"TreeObj.checkAllNodes(false);");
            				 //sb.append("    "+f.getName()+"openClosChose();");
            				 sb.append("	}");
            				 sb.append("	function "+f.getName()+"applyChose(){");
            				 sb.append("   var "+f.getName()+"TreeObj = $.fn.zTree.getZTreeObj(\""+f.getName()+"_zTree\");");
            				 sb.append("   var "+f.getName()+"nodes="+f.getName()+"TreeObj.getCheckedNodes(true);");
            				 if(StringUtil.isNotEmpty(dty[4])&&!dty[4].equals("no")){
            					 sb.append("if(!"+dty[4]+"("+f.getName()+"nodes)){return;}");
            				 }
            				 sb.append("   var "+f.getName()+"TreeVal=\"\" ; ");
            				 sb.append(" for(var i=0;i<"+f.getName()+"nodes.length;i++){");
            				 sb.append("     "+f.getName()+"TreeVal+="+f.getName()+"nodes[i].id + \",\";");
            			     sb.append("      }");
            			     sb.append(" $(\"#"+f.getName()+"_view_txt\").html(\"Choose \"+"+f.getName()+"nodes.length);" );
            			     sb.append(" $(\"#"+f.getName()+"_txt\").val("+f.getName()+"TreeVal);" );
            				 sb.append("     "+f.getName()+"openClosChose();");
            				 sb.append("	}");
            				 sb.append("	function "+f.getName()+"RefreshzTree(){");
            				 sb.append("    var "+f.getName()+"SearchVal=$(\"#"+f.getName()+"_search_input_id\").val();");
            				 sb.append("   var "+f.getName()+"TreeObj = $.fn.zTree.getZTreeObj(\""+f.getName()+"_zTree\");");
            				 sb.append("   $.ajax({");
	        		            sb.append("       type: 'POST',");
	        		            sb.append("       url: '"+jzt.getUrl()+"',");
	        		            sb.append("       data: {searchName:"+f.getName()+"SearchVal},");
	        		            sb.append("       dataType: 'json',");
	        		            sb.append("       success: function(data){");
	        		            sb.append("       if(data.success){");
	        		            sb.append("           $.fn.zTree.init($(\"#"+jzt.getId()+"\"), "+jzt.getId()+"Setting, data.obj);");
	        		            sb.append("        }else{");
	        		            sb.append(" window.parent.layer.msg(data.msg, {icon: 2});");
	        		            sb.append("        }");
	        		            sb.append("     },error: function (msg) {");
	        		            sb.append(" window.parent.layer.msg('Request error', {icon: 2});");
	        		            sb.append("      }");
	        		            sb.append("    });");
            				 sb.append("	}");
            				 sb.append(" </script> ");
	        				 sb.append("	<div class=\"chosen-drop-button\">");
	        				 sb.append("		<button onclick=\""+f.getName()+"clearChose();\" class=\"btn btn-blue\">Clear</button>");
	        				 sb.append("		<button onclick=\""+f.getName()+"applyChose();\" class=\"btn btn-danger\">Apply</button>");
	        				 sb.append("		</div>");
	        				 sb.append("	</div>");
	        				 sb.append("	</div>");
                    	}
                    }else if(StringUtil.isNotEmpty(f.getDictionary())){
                        sb.append("<div class=\"col-md-2\">");
                        sb.append("  <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                        sb.append(" <select data-placeholder=\""+"MutiLangUtil.doMutiLang(\"system.pleaseChoose\", f.getTitle())"+"\" class=\"hes_select\" multiple tabindex=\"4\" id=\""+f.getName()+"_txt\" >");
                        sb.append("</select>");
                        sb.append(" <script type=\"text/javascript\"> ");
                        sb.append("   for(var prop in "+f.getName()+"Map){");
                        sb.append("     if("+f.getName()+"Map.hasOwnProperty(prop)){");
                        if(StringUtil.isNotEmpty(f.getDefaults())){
                        	sb.append("   if(prop=='"+f.getDefaults()+"'){");
                        	sb.append("          $(\"#"+f.getName()+"_txt\").append(\"<option value='\"+prop.value+\"'>\"+"+f.getName()+"Map[prop]+\"</option>\");");
                        	sb.append("   }else{");
                        	sb.append("          $(\"#"+f.getName()+"_txt\").append(\"<option value='\"+prop+\"'>\"+"+f.getName()+"Map[prop]+\"</option>\");");
                        	sb.append("   }");
                        }else{
                        	sb.append("          $(\"#"+f.getName()+"_txt\").append(\"<option value='\"+prop+\"'>\"+"+f.getName()+"Map[prop]+\"</option>\");");
                        }
                        sb.append("        }");
                        sb.append("   }");
                       /* sb.append("   $('#"+f.getName()+"_txt').chosen({");
                        sb.append("      max_selected_options:1");
                        sb.append("   });");*/
                        sb.append(" </script> ");
                        sb.append(" </div>");
                    }else if(StringUtil.isNotEmpty(f.getReplace())){
                        String[] reps=f.getReplace().split(f.getSplitStr());
                        sb.append("<div class=\"col-md-2\">");
                        sb.append("    <label class=\"hes_lable\">"+f.getTitle()+"</label>");
                        sb.append(" <select  class=\"hes_select\"  id=\""+f.getName()+"_txt\" >");
                        if(StringUtil.isNotEmpty(f.getSeleDef())){
                        	sb.append("<option value=\"\">"+f.getSeleDef()+"</option>");
                        }
                        for(String rep:reps){
                            String key=rep.substring(0,rep.indexOf(":"));
                            String value=rep.substring(rep.indexOf(":")+1, rep.length());
                            if(StringUtil.isNotEmpty(key)&&StringUtil.isNotEmpty(value)){
                            	if(StringUtil.isNotEmpty(f.getDefaults())&&f.getDefaults().equals(key)){
                            		sb.append("<option selected=\"selected\" value=\""+key+"\">"+MutiLangUtil.doMutiLang(value)+"</option>");
                            	}else{
                            		sb.append("<option value=\""+key+"\">"+MutiLangUtil.doMutiLang(value)+"</option>");
                            	}
                            }
                        }
                        sb.append("</select>");
                       /* sb.append(" <script type=\"text/javascript\"> ");
                        sb.append(" $('#"+f.getName()+"_txt').chosen({ ");
                        sb.append("     max_selected_options:1 ");
                        sb.append("}); ");
                        sb.append(" </script> ");*/
                        sb.append(" </div>");
                    }else if(StringUtil.isNotEmpty(f.getFormatter())){
                        sb.append("<div class=\"col-md-2\">");
                        sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                        sb.append("     <input type=\"text\" class=\"Wdate hes_input\" id=\""+f.getName()+"_txt\" value=\""+(StringUtil.isNotEmpty(f.getDefaults())?f.getDefaults():"")+"\"  onClick=\"WdatePicker({lang:'en_us',dateFmt:'"+DateUtils.getJqdfmatMwpfmt(f.getFormatter())+"'})\" > ");
                        sb.append(" </div>");
                    }else{
                        sb.append("<div class=\"col-md-2\">");
                        sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                        sb.append("       <input type=\"text\" class=\"hes_input\" id=\""+f.getName()+"_txt\" value=\""+(StringUtil.isNotEmpty(f.getDefaults())?f.getDefaults():"")+"\" \"> ");
                        sb.append(" </div>");
                    }
                }else{
                	if(StringUtil.isNotEmpty(f.getQueryHtml())){
                		sb.append(f.getQueryHtml());
                	}else if(StringUtil.isNotEmpty(f.getFormatter())){
                		if(StringUtil.isNotEmpty(f.getDefaults())&&f.getDefaults().split(f.getSplitStr()).length>=2){
                   		 	String[] strs=f.getDefaults().split(f.getSplitStr());
	                   		sb.append("<div class=\"col-md-2\">");
	                        sb.append("    <label class=\"hes_lable\">"+MutiLangUtil.doMutiLang("system.start")+" "+f.getTitle()+"</label>");
	                        sb.append(" <input type=\"text\" class=\"Wdate hes_input\" id=\""+f.getName()+"_start_txt\" value=\""+strs[0]+"\"  onClick=\"WdatePicker({isShowClear:false,lang:'en_us',dateFmt:'"+DateUtils.getJqdfmatMwpfmt(f.getFormatter())+"'})\"   />");
	                        sb.append(" </div>");
	                        
	                        sb.append("<div class=\"col-md-2\">");
	                        sb.append("    <label class=\"hes_lable\">"+MutiLangUtil.doMutiLang("system.end")+" "+f.getTitle()+"</label>");
	                        sb.append("  <input type=\"text\" class=\"Wdate hes_input\" id=\""+f.getName()+"_end_txt\" value=\""+strs[1]+"\" onClick=\"WdatePicker({isShowClear:false,lang:'en_us',dateFmt:'"+DateUtils.getJqdfmatMwpfmt(f.getFormatter())+"'})\"  />");
	                        sb.append(" </div>");
                		}else{
	                        if(f.getOnlyStartTime()) {
                                sb.append("<div class=\"col-md-2\">");
                                sb.append("    <label class=\"hes_lable\">"+MutiLangUtil.doMutiLang("system.startdate")+" "+f.getTitle()+"</label>");
                                sb.append(" <input type=\"text\" class=\"Wdate hes_input\" id=\""+f.getName()+"_start_txt\"  onClick=\"WdatePicker({isShowClear:false,lang:'en_us',dateFmt:'"+DateUtils.getJqdfmatMwpfmt(f.getFormatter())+"'})\"   />");
                                sb.append(" </div>");
                            }else {
                                sb.append("<div class=\"col-md-2\">");
                                sb.append("    <label class=\"hes_lable\">"+MutiLangUtil.doMutiLang("system.start")+" "+f.getTitle()+"</label>");
                                sb.append(" <input type=\"text\" class=\"Wdate hes_input\" id=\""+f.getName()+"_start_txt\"  onClick=\"WdatePicker({isShowClear:false,lang:'en_us',dateFmt:'"+DateUtils.getJqdfmatMwpfmt(f.getFormatter())+"'})\"   />");
                                sb.append(" </div>");

                                sb.append("<div class=\"col-md-2\">");
                                sb.append("    <label class=\"hes_lable\">"+MutiLangUtil.doMutiLang("system.end")+" "+f.getTitle()+"</label>");
                                sb.append("  <input type=\"text\" class=\"Wdate hes_input\" id=\""+f.getName()+"_end_txt\"  onClick=\"WdatePicker({isShowClear:false,lang:'en_us',dateFmt:'"+DateUtils.getJqdfmatMwpfmt(f.getFormatter())+"'})\"  />");
                                sb.append(" </div>");
                            }

                		}
                    }else{
                    	 if(StringUtil.isNotEmpty(f.getDefaults())&&f.getDefaults().split(f.getSplitStr()).length>=2){
                    		 String[] strs=f.getDefaults().split(f.getSplitStr());
                    		 sb.append("<div class=\"col-md-2\">");
                    		 sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                    		 sb.append(" <input type=\"text\" class=\"hes_input\" id=\""+f.getName()+"_start_txt\" value=\""+strs[0]+"\"   />");
                    		 sb.append(" </div>");
                    		 
                    		 sb.append("<div class=\"col-md-2\">");
                    		 sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                    		 sb.append("  <input type=\"text\" class=\"hes_input\" id=\""+f.getName()+"_end_txt\"  value=\""+strs[1]+"\"     />");
                    		 sb.append(" </div>");
                    	 }else{
                    		 sb.append("<div class=\"col-md-2\">");
                    		 sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                    		 sb.append(" <input type=\"text\" class=\"hes_input\" id=\""+f.getName()+"_start_txt\"   />");
                    		 sb.append(" </div>");
                    		 
                    		 sb.append("<div class=\"col-md-2\">");
                    		 sb.append("    <label  class=\"hes_lable\">"+f.getTitle()+"</label>");
                    		 sb.append("  <input type=\"text\" class=\"hes_input\" id=\""+f.getName()+"_end_txt\"    />");
                    		 sb.append(" </div>");
                    	 }
                    }
                }
            }
        }
        
        return sb;
    }
    /**
     * 头部操作按钮封装
     * @return 
     * @see
     */
    private StringBuffer getHeadOpeBtn(){
        StringBuffer sb = new StringBuffer();
        sb.append("$('#gview_"+name+"').children('div.ui-jqgrid-titlebar').append(\"<div class=' titleBtnItem ' >");
        for(FindListOpt tb:toolBarList){
        	sb.append("<div class='btn_wrapper'  "+(StringUtil.isNotEmpty(tb.getFunname())?" onclick=\\\""+tb.getFunname()+"('"+name+"','"+(StringUtil.isNotEmpty(tb.getTitle())?tb.getTitle():"")+"','"+(StringUtil.isNotEmpty(tb.getUrl())?tb.getUrl():"")+"','"+(StringUtil.isNotEmpty(tb.getWidth())?tb.getWidth():"")+"','"+(StringUtil.isNotEmpty(tb.getHeight())?tb.getHeight():"")+"',"+checkbox+")":"")+"\\\"  title=\\\""+tb.getTitle()+"\\\" ><div class= 'ui-title-btn'><span class='"+tb.getIcon()+"'></span></div></div> ");
        }
        sb.append("</div>\");");
        return sb;
    }
    /**
     * 列头部名称封装
     * @return 
     * @see
     */
    private String getColNames(){
        String sb="";
        String optName="";
        for(FindListColumn f:columnList){
            if("opt".equals(f.getName())){
                optName="'"+f.getTitle()+"'";
                continue;
            }
            sb=StringUtil.isEmpty(sb)?("'"+f.getTitle()+"'"):(sb+",'"+f.getTitle()+"'");
        }
        if(StringUtil.isNotEmpty(optName)){
            sb=optName+","+sb;
        }
        return sb;
    }
    /**
     * 列参数初始化封装
     * @return 
     * @see
     */
    private StringBuffer getColModel(){
        StringBuffer sb = new StringBuffer();
        if(isOpt){
            sb.append("{");
            sb.append("   name: '"+opt.getName()+"',");
            sb.append("   index: '"+opt.getName()+"',");
            sb.append("   editable: false,");
            sb.append(StringUtil.isNotEmpty(opt.getClasses())?("   classes: '"+opt.getClasses()+"',"):"");
            if(StringUtil.isNotEmpty(opt.getWidth())){
            	sb.append("   width: "+opt.getWidth()+",");
            }else{
            	sb.append("   width: 100,");
            }
            sb.append("   sortable: "+opt.isSortable()+",");
            sb.append("   hidden:"+opt.isHidden()+",");
            sb.append(StringUtil.isNotEmpty(opt.getFormatterjs())?("   formatter: "+opt.getFormatterjs()+","):"formatter: "+name+"optFormatter,");
            sb.append(StringUtil.isNotEmpty(opt.getAlign())?("   align: '"+opt.getAlign()+"',"):"");
            sb.append("   frozen : "+opt.isFrozenColumn()+"");
            sb.append(" },");
        }
        for(FindListColumn f:columnList){
            if("opt".equals(f.getName())){
                continue;
            }
            sb.append("{");
            sb.append("   name: '"+f.getName()+"',");
            sb.append("   index: '"+f.getName()+"',");
            if(f.getKey()){
            	sb.append("   key: "+f.getKey()+",");
            }
            sb.append("   editable: "+f.getEditable()+",");
            if(f.getEditable()){
            	if(StringUtil.isNotEmpty(f.getEdittype())){
            		sb.append("   edittype: \""+f.getEdittype()+"\",");
            	}
            	if(StringUtil.isNotEmpty(f.getEditoptions())){
            		sb.append("   editoptions: "+f.getEditoptions()+",");
            	}
            	if(StringUtil.isNotEmpty(f.getFormatter())){
            		sb.append("formatter:\""+f.getFormatter()+"\",");
            	}
            }
            if(StringUtil.isNotEmpty(f.getCellattr())){
            	 sb.append("   cellattr: "+f.getCellattr()+",");
            }
            sb.append(StringUtil.isNotEmpty(f.getClasses())?("   classes: '"+f.getClasses()+"',"):"");
            if(StringUtil.isNotEmpty(f.getWidth())){
                sb.append("   width: "+f.getWidth()+",");
            }
            sb.append("   sortable: "+f.isSortable()+",");
            sb.append("   hidden:"+f.isHidden()+",");
            if(!f.getEditable()&&StringUtil.isNotEmpty(f.getFormatter())){
                sb.append("formatter:\"date\",");
                sb.append("formatoptions: {srcformat:'Y-m-d H:i:s',newformat:'"+f.getFormatter()+"'},");
            }else if(StringUtil.isNotEmpty(f.getReplace())||StringUtil.isNotEmpty(f.getDictionary())||(StringUtil.isNotEmpty(f.getUrl())&&f.getUrl().split("#").length==3)){
                sb.append("formatter:function(cellvalue, options, rowObject){");
                sb.append("if(cellvalue!=null){");
                sb.append("var returnStr='';");
                sb.append("var values=(''+cellvalue).split(\""+f.getSplitStr()+"\");");
                sb.append("for(var i in values){");
                sb.append("var value="+f.getName()+"Map[values[i]];");
                sb.append("if(i==0){");
                sb.append("if(value!=null){");
                sb.append(" returnStr=value;");
                sb.append("}else{");
                sb.append(" returnStr=values[i];");
                sb.append("}");
                sb.append("}else{");
                sb.append("if(value!=null){");
                sb.append(" returnStr+='"+f.getSplitStr()+"</br>'+value+'';");
                sb.append("}else{");
                sb.append(" returnStr+='"+f.getSplitStr()+"</br>'+values[i];");
                sb.append("}");
                sb.append("}");
                sb.append("}return returnStr;");
                sb.append("}else{return '';}},");
            }else if(StringUtil.isNotEmpty(f.getFormatterjs())){
                sb.append("   formatter: "+f.getFormatterjs()+",");
            }else if("img".equals(f.getValType())){
                if(StringUtil.isNotEmpty(f.getImageSize())){
                    String[] imgs=f.getImageSize().split(f.getSplitStr());
                    if(imgs.length>=2){
                        sb.append("formatter:function(cellvalue, options, rowObject){return '<img src=\"'+cellvalue+'\" style=\"width: "+imgs[0]+";height: "+imgs[1]+";\"/>';},");
                    }else{
                        sb.append("formatter:function(cellvalue, options, rowObject){return '<img src=\"'+cellvalue+'\" />';},");
                    }
                }else{
                    sb.append("formatter:function(cellvalue, options, rowObject){return '<img src=\"'+cellvalue+'\" />';},");
                }
            }else if("url".equals(f.getValType())){
                sb.append("formatter:function(cellvalue, options, rowObject){return '<a href=\"'+cellvalue+'\">'+cellvalue+'</a>';},");
            }
            sb.append(StringUtil.isNotEmpty(f.getAlign())?("   align: '"+f.getAlign()+"',"):"");
            sb.append("   frozen : "+f.isFrozenColumn()+"");
            sb.append(" },");
        }
        return sb;
    }
    
    private StringBuffer getOptBtn(){
        StringBuffer sb = new StringBuffer();
        sb.append("var querStr='';");
        for(FindListOpt f:optList){
            boolean isExp=false;
            String ifStr="";
                if(StringUtil.isNotEmpty(f.getExp())){
                    ExpUtl eu=TagUtil.getFindListExp(f.getExp());
                    if(eu.getExpList().size()>0){
                        for(int i=0;i<eu.getExpList().size();i++){
                            FindListExp fle=eu.getExpList().get(i);
                            ifStr+="rowObject."+fle.getField()+fle.getExpression()+fle.getValue();
                            if(i+1<eu.getExpList().size()){
                                ifStr+=eu.getJud().get(i);
                            }
                        }
                        isExp=true;
                    }
                }
               if(isExp){ 
                   sb.append("if(");
                   sb.append(ifStr);
                   sb.append("){");
               }
               String url=f.getUrl();
               if(StringUtil.isNotEmpty(url)){
                   String[] fields=StringUtil.getOptFileName(url);
                   for(String fd:fields){
                       url=url.replace("{"+fd+"}", "'+rowObject."+fd+"+'");
                   }
               }
                if(f.getType().equals(OptTypeDirection.Del)){
                    if(StringUtil.isEmpty(f.getFunname())){
                        sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\"rowDel(\\'"+name+"\\',\\'"+f.getTitle()+"\\',\\'"+url+"\\',\\'"+f.getMessage()+"\\')\" style=\"margin:2px\"></i> </a>';");
                    }else{
                        sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\""+f.getFunname()+"(\\''+rowObject.id+'\\',\\'"+url+"\\',\\'"+f.getMessage()+"\\')\"  style=\"margin:2px\"></i> </a>';");
                    }
                }else if(f.getType().equals(OptTypeDirection.Confirm)){
                    sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\"config(\\'"+name+"\\',\\'"+f.getTitle()+"\\',\\'"+url+"\\',\\'"+f.getMessage()+"\\')\"  style=\"margin:2px\"></i> </a>';");
                }else if(f.getType().equals(OptTypeDirection.Fun)){
                    String funname=f.getFunname();
                    if(StringUtil.isNotEmpty(funname)){
                        String[] fields=StringUtil.getOptFileName(funname);
                        for(String fd:fields){
                            funname=funname.replace("{"+fd+"}", "\\''+rowObject."+fd+"+'\\'");
                        }
                        sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\"> <i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\""+funname+"\"  style=\"margin:2px\"></i> </a>';");
                    }else{
                    	sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\"> <i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" style=\"margin:2px\"></i> </a>';");
                    }
                }else if(f.getType().equals(OptTypeDirection.Deff)){
                    sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\"deffFun(\\'"+name+"\\',\\'"+url+"\\',\\'"+f.getTitle()+"\\')\"  style=\"margin:2px\"></i> </a>';");
                }else if(f.getType().equals(OptTypeDirection.OpenTab)){
                    sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\"openTab(\\'"+name+"\\',\\'"+url+"\\',\\'"+f.getTitle()+"\\',\\'"+f.getWidth()+"\\',\\'"+f.getHeight()+"\\')\"  style=\"margin:2px\"></i> </a>';");
                }else if(f.getType().equals(OptTypeDirection.OpenWin)){
                    sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\"openWin(\\'"+name+"\\',\\'"+url+"\\',\\'"+f.getTitle()+"\\',\\'"+f.getWidth()+"\\',\\'"+f.getHeight()+"\\')\"  style=\"margin:2px\"></i> </a>';");
                }else if(f.getType().equals(OptTypeDirection.SidesOut)){
                	 sb.append("querStr=querStr+'<a href=\"javascript:;\"  title=\""+f.getTitle()+"\" ><i class=\" fa "+(StringUtil.isNotEmpty(f.getIcon())?f.getIcon():"fa-trash-o")+"\" onclick=\""+name+"right_a(\\'"+f.getWidth()+"\\',\\'"+f.getTitle()+"\\',\\'"+url+"\\')\" style=\"margin:2px\"></i> </a>';");
                }
                if(isExp) sb.append("}");
            
        }
        sb.append("return querStr;");
        return sb;
    }
    /**
     * 重置按钮操作封装
     * @return 
     * @see
     */
    private StringBuffer getResetOpt(){
        StringBuffer sb = new StringBuffer();
        for(FindListColumn f:columnList){
            if("opt".equals(f.getName())||!f.isQuery()){
                continue;
            }
            //single单字段查询；scope范围查询*/
            if("single".equals(f.getQueryMode())){
                if((StringUtil.isNotEmpty(f.getUrl())&&StringUtil.isNotEmpty(f.getDictionary()))||StringUtil.isNotEmpty(f.getDictionary())||StringUtil.isNotEmpty(f.getReplace())){
                    sb.append("$('#"+f.getName()+"_txt').val('');");
                    sb.append("$('#"+f.getName()+"_txt').trigger('chosen:updated');");
                }else if(StringUtil.isNotEmpty(f.getUrl())){
                    sb.append("$('#"+f.getName()+"_txt').val('');");
                    sb.append("$('#"+f.getName()+"_text_txt').val('');");
                }else{
                    sb.append("$('#"+f.getName()+"_txt').val('');");
                }
            }else{
                sb.append("$('#"+f.getName()+"_start_txt').val('');");
                sb.append("$('#"+f.getName()+"_end_txt').val('');");
            }
        }
        return sb;
    }
    /**
     * 
     * 查询按钮操作代码封装
     * @return 
     * @see
     */
    private StringBuffer getQueryOpt(){
        StringBuffer sb = new StringBuffer();
        String str="";
        String paramName="";
        for(FindListColumn f:columnList){
            if("opt".equals(f.getName())||!f.isQuery()){
                continue;
            }
            if("single".equals(f.getQueryMode())){
                sb.append("var "+f.getName()+"=$('#"+f.getName()+"_txt').val();");
                sb.append(""+f.getName()+"="+f.getName()+"!=null?"+f.getName()+".toString():'';");
                str+=f.getName()+":"+f.getName()+",";
                paramName="if(k=='"+f.getName()+"'){delete postData[k]; }";
            }else{
                sb.append("var "+f.getName()+"_start"+"=$('#"+f.getName()+"_start_txt').val();");
                sb.append(""+f.getName()+"_start="+f.getName()+"_start!=null?"+f.getName()+"_start.toString():'';");
                sb.append("var "+f.getName()+"_end"+"=$('#"+f.getName()+"_end_txt').val();");
                sb.append(""+f.getName()+"_end="+f.getName()+"_end!=null?"+f.getName()+"_end.toString():'';");
                str+=f.getName()+"_start"+":"+f.getName()+"_start,";
                str+=f.getName()+"_end"+":"+f.getName()+"_end,";
                paramName="if(k=='"+f.getName()+"_start'){delete postData[k]; }";
                paramName="if(k=='"+f.getName()+"_end'){delete postData[k]; }";
            }
        }
        sb.append("var data={");
        sb.append(str);
        sb.append("};");
       
        if(StringUtil.isNotEmpty(queryDataFun)){
        	sb.append("var d="+queryDataFun+"();");
        	sb.append("if(d!=null){");
        	sb.append("var propertys = Object.keys(d);");
        	sb.append(" var postData = $('#"+name+"').jqGrid(\"getGridParam\", \"postData\");  ");
        	sb.append(" $.each(postData, function (k, v) {  ");
        	sb.append(paramName);
        	sb.append("if($.inArray(k, propertys)!=-1){ delete postData[k]; }");
        	sb.append(" });   ");
        	sb.append("data= Object.assign(data, d);\n");
        	sb.append(" $('#"+name+"').jqGrid('setGridParam', {");
            sb.append("async : false,");
            sb.append(" cache : false,");
            sb.append(" traditional: true,");
            sb.append("    url: '"+actionUrl+"',");
            sb.append("    postData: data,");
            sb.append("    page: 1");
            sb.append(" }).trigger('reloadGrid');");
        	sb.append("}");
        }else{
	    	sb.append(" var postData = $('#"+name+"').jqGrid(\"getGridParam\", \"postData\");  ");
	        sb.append(" $.each(postData, function (k, v) {  ");
	        sb.append(paramName);
	        sb.append(" });   ");
        	sb.append(" $('#"+name+"').jqGrid('setGridParam', {");
            sb.append("async : false,");
            sb.append(" cache : false,");
            sb.append(" traditional: true,");
            sb.append("    url: '"+actionUrl+"',");
            sb.append("    postData: data,");
            sb.append("    page: 1");
            sb.append(" }).trigger('reloadGrid');");
        }
        return sb;
    }
    
    /**
     * 头部查询条件
     * @return
     */
    private StringBuffer getHeadQueryOpt(){
        StringBuffer sb = new StringBuffer();
        String str="";
        for(FindListColumn f:columnList){
            if("opt".equals(f.getName())){
                continue;
            }
            if(!f.isQuery()&&StringUtil.isNotEmpty(f.getDefaults())){
            	searchFields=StringUtil.isEmpty(searchFields)?f.getName():searchFields+","+f.getName();
                searchString=StringUtil.isEmpty(searchString)?f.getQueryMode():searchString+","+f.getQueryMode();
                defaults=defaults+","+f.getName()+":'"+f.getDefaults()+"'";
            }else if(!f.isQuery()){
            	continue;
            }
            
            if("single".equals(f.getQueryMode())){
                sb.append("var "+f.getName()+"=$('#"+f.getName()+"_txt').val();");
                sb.append(""+f.getName()+"="+f.getName()+"!=null?"+f.getName()+".toString():'';");
                str+=f.getName()+":"+f.getName()+",";
            }else{
                sb.append("var "+f.getName()+"_start"+"=$('#"+f.getName()+"_start_txt').val();");
                sb.append(""+f.getName()+"_start="+f.getName()+"_start!=null?"+f.getName()+"_start.toString():'';");
                sb.append("var "+f.getName()+"_end"+"=$('#"+f.getName()+"_end_txt').val();");
                sb.append(""+f.getName()+"_end="+f.getName()+"_end!=null?"+f.getName()+"_end.toString():'';");
                str+=f.getName()+"_start"+":"+f.getName()+"_start,";
                str+=f.getName()+"_end"+":"+f.getName()+"_end,";
            }
            
        }
        sb.append("var "+name+"headData={");
        sb.append(str);
        sb.append("'field':'"+fields+"'");
        sb.append(",'searchField':'"+searchFields+"','searchString':'"+searchString+"'"+defaults+"};");
        return sb;
    }


    public String getSearchFields() {
        return searchFields;
    }


    public String getName() {
        return name;
    }


    public String getTitle() {
        return title;
    }


    public boolean isTreegrid() {
        return treegrid;
    }


    public List<FindListOpt> getOptList() {
        return optList;
    }


    public String getActionUrl() {
        return actionUrl;
    }


    public int getPageSize() {
        return pageSize;
    }


    public boolean isPagination() {
        return pagination;
    }


    public String getWidth() {
        return width;
    }


    public String getHeight() {
        return height;
    }


    public boolean isCheckbox() {
        return checkbox;
    }


    public String getSortName() {
        return sortName;
    }


    public String getSortOrder() {
        return sortOrder;
    }


    public boolean isShowRefresh() {
        return showRefresh;
    }


    public String getOnLoadSuccess() {
        return onLoadSuccess;
    }


    public String getOnClick() {
        return onClick;
    }


    public String getOnDblClick() {
        return onDblClick;
    }


    public String getLangArg() {
        return langArg;
    }


    public void setSearchFields(String searchFields) {
        this.searchFields = searchFields;
    }


    public void setName(String name) {
        this.name = name;
    }


    public void setTitle(String title) {
        this.title = title;
    }


    public void setTreegrid(boolean treegrid) {
        this.treegrid = treegrid;
    }


    public void setOptList(List<FindListOpt> optList) {
        this.optList = optList;
    }


    public void setActionUrl(String actionUrl) {
        this.actionUrl = actionUrl;
    }


    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }


    public void setPagination(boolean pagination) {
        this.pagination = pagination;
    }


    public void setWidth(String width) {
        this.width = width;
    }


    public void setHeight(String height) {
        this.height = height;
    }


    public void setCheckbox(boolean checkbox) {
        this.checkbox = checkbox;
    }


    public void setSortName(String sortName) {
        this.sortName = sortName;
    }


    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }


    public void setShowRefresh(boolean showRefresh) {
        this.showRefresh = showRefresh;
    }


    public void setOnLoadSuccess(String onLoadSuccess) {
        this.onLoadSuccess = onLoadSuccess;
    }


    public void setOnClick(String onClick) {
        this.onClick = onClick;
    }


    public void setOnDblClick(String onDblClick) {
        this.onDblClick = onDblClick;
    }


    public void setLangArg(String langArg) {
        this.langArg = langArg;
    }



    public void setExpandColumn(String expandColumn) {
        this.expandColumn = expandColumn;
    }



	public void setDefaults(String defaults) {
		this.defaults = defaults;
	}


	public String getBeforeInquiries() {
		return beforeInquiries;
	}


	public void setBeforeInquiries(String beforeInquiries) {
		this.beforeInquiries = beforeInquiries;
	}


	public String getBeforeRequest() {
		return beforeRequest;
	}


	public void setBeforeRequest(String beforeRequest) {
		this.beforeRequest = beforeRequest;
	}


	public boolean isRownumbers() {
		return rownumbers;
	}


	public void setRownumbers(boolean rownumbers) {
		this.rownumbers = rownumbers;
	}


	public boolean isShrinkToFit() {
		return shrinkToFit;
	}


	public void setShrinkToFit(boolean shrinkToFit) {
		this.shrinkToFit = shrinkToFit;
	}


	public boolean isAutowidth() {
		return autowidth;
	}


	public void setAutowidth(boolean autowidth) {
		this.autowidth = autowidth;
	}


	public String getQueryDataFun() {
		return queryDataFun;
	}


	public void setQueryDataFun(String queryDataFun) {
		this.queryDataFun = queryDataFun;
	}


	public String getLoadComplete() {
		return loadComplete;
	}


	public void setLoadComplete(String loadComplete) {
		this.loadComplete = loadComplete;
	}


	public String getEditurl() {
		return editurl;
	}


	public void setEditurl(String editurl) {
		this.editurl = editurl;
	}


	public String getBeforeSelectRow() {
		return beforeSelectRow;
	}


	public void setBeforeSelectRow(String beforeSelectRow) {
		this.beforeSelectRow = beforeSelectRow;
	}


	public String getBeforeLoading() {
		return beforeLoading;
	}


	public void setBeforeLoading(String beforeLoading) {
		this.beforeLoading = beforeLoading;
	}


	public String getAfterLoading() {
		return afterLoading;
	}


	public void setAfterLoading(String afterLoading) {
		this.afterLoading = afterLoading;
	}


	public String getExtParam() {
		return extParam;
	}


	public void setExtParam(String extParam) {
		this.extParam = extParam;
	}
    
    
    
    
    

}
