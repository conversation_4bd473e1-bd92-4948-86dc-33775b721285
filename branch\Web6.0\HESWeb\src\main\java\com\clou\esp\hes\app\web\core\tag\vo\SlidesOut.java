package com.clou.esp.hes.app.web.core.tag.vo;


/**
 * 文件名：SlidesOut.java
 * 版权：Copyright by Power7000g Team
 * 描述：
 * 修改人：严浪
 * 修改时间：2017年9月20日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class SlidesOut {
	protected String title;
    protected String exp;//判断链接是否显示的表达式
    protected String langArg;//按钮
    protected String icon;//样式
    protected String url; //展示URL
    protected String proportion; //宽度比例
    
	public SlidesOut(String title, String exp, String langArg, String icon,
			String url,String proportion) {
		super();
		this.title = title;
		this.exp = exp;
		this.langArg = langArg;
		this.icon = icon;
		this.url = url;
		this.proportion=proportion;
	}
	
	
	public SlidesOut() {
		super();
		// TODO Auto-generated constructor stub
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getExp() {
		return exp;
	}
	public void setExp(String exp) {
		this.exp = exp;
	}
	public String getLangArg() {
		return langArg;
	}
	public void setLangArg(String langArg) {
		this.langArg = langArg;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}


	public String getProportion() {
		return proportion;
	}


	public void setProportion(String proportion) {
		this.proportion = proportion;
	}
    
    
}
