package com.clou.esp.hes.app.web.listener.system;

import java.util.List;

import javax.servlet.ServletContext;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ServletContextAware;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.clou.esp.hes.app.web.model.system.SysOrg;
import com.clou.esp.hes.app.web.service.system.SysOrgService;

public class InitDataListener implements InitializingBean, ServletContextAware{
//	@Autowired
//	SysOrgService sysOrgService;
	
	/**
	 * TODO
	 * 读取数据库的数据，将其放入application中
	 */
	@Override
	public void setServletContext(ServletContext application) {		
	//  List<SysOrg> sysOrgList =	sysOrgService.getAllList();
	  MutiLangUtil.i18nTransMap.put("dictMenu", "menuname");
	  MutiLangUtil.i18nTransMap.put("sysOrg", "name,description");
	  		
	}
	
	
	@Override
	public void afterPropertiesSet() throws Exception {
		// TODO Auto-generated method stub
		
	}
}