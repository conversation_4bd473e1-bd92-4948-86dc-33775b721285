<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.power7000.mapper.AssetMeterMapper">
  <resultMap id="BaseResultMap" type="com.power7000.model.ghana.AssetMeter">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="UTILITY_ID" jdbcType="VARCHAR" property="utilityId" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="COMMUNICATOR_ID" jdbcType="VARCHAR" property="communicatorId" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
    <result column="HLS_AK" jdbcType="VARCHAR" property="hlsAk" />
    <result column="HLS_EK" jdbcType="VARCHAR" property="hlsEk" />
    <result column="INDEX_DCU" jdbcType="INTEGER" property="indexDcu" />
    <result column="MAC" jdbcType="VARCHAR" property="mac" />
    <result column="COM_TYPE" jdbcType="VARCHAR" property="comType" />
    <result column="IS_ENCRYPT" jdbcType="VARCHAR" property="isEncrypt" />
    <result column="AUTH_TYPE" jdbcType="VARCHAR" property="authType" />
    <result column="FW_VERSION" jdbcType="VARCHAR" property="fwVersion" />
    <result column="REMOVE_FLAG" jdbcType="INTEGER" property="removeFlag" />
    <result column="ADDR" jdbcType="VARCHAR" property="addr" />
    <result column="LONGITUDE" jdbcType="VARCHAR" property="longitude" />
    <result column="LATITUDE" jdbcType="VARCHAR" property="latitude" />
    <result column="COM_PORT" jdbcType="INTEGER" property="comPort" />
    <result column="ct" jdbcType="INTEGER" property="ct" />
    <result column="pt" jdbcType="INTEGER" property="pt" />
    <result column="KEY_FLAG" jdbcType="INTEGER" property="keyFlag" />
    <result column="SRC_ADDR" jdbcType="INTEGER" property="srcAddr" />
    <result column="METER_STATUS" jdbcType="SMALLINT" property="meterStatus" />
  </resultMap>
  
	
	<select id="findByMeterSerialNoForRunstatus" parameterType="java.util.Map" resultMap="BaseResultMap">
		SELECT * FROM ASSET_METER WHERE sn = #{sn} and METER_STATUS='3'
	</select>
	
	<select id="findMsPointByCMSIsStorage" parameterType="java.util.Map" resultType="com.power7000.model.ghana.AssetMeter">
		SELECT pmp.* FROM asset_meter pmp	LEFT JOIN ppm_public_cms_servicepoint pcs 
		ON pmp.sn =	pcs.METER_SERIAL_NO
		WHERE pcs.IS_STORAGE = '1' and pcs.CUSTOMER_ID is not null and pcs.CUSTOMER_ID != ''
		and pcs.METER_SERIAL_NO is not null and pcs.METER_SERIAL_NO != ''
	</select>
	
	<insert id="insertMeter" parameterType="com.power7000.model.ghana.AssetMeter">
		insert into asset_meter (ID,COMMUNICATOR_ID,SN,ORG_ID,MANUFACTURER,MODEL,COM_TYPE,MAC,UTILITY_ID)
		values	(#{id},#{communicatorId},#{sn},#{orgId},#{manufacturer},#{model},#{comType},#{mac},#{utilityId})
  	</insert>
</mapper>