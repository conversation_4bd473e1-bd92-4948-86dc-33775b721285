package com.clou.esp.hes.app.web.core.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.clou.esp.hes.app.web.model.asset.AssetMeasurementProfile;
import com.power7000g.core.util.base.DateUtils;

public class ProgressMissDataUtil {
  
      public static List<Date> regularTime(Date starTime,Date endTime,AssetMeasurementProfile measurementGroup,List<Date> timeList) {  
  		List<Date> timeListNew = new ArrayList();
  		
  		int freq = Integer.parseInt(measurementGroup.getProfileCycle());
  	    String freqType = measurementGroup.getProfileCycleType();
  	    freqType = freqType.toLowerCase();
  	   //分钟2160条  月36条 日90条
  	    Calendar cal = Calendar.getInstance();
  	    cal.setTime(starTime);
  	    
  	    switch (freqType) {
  	    case "minutely": {
  	    	int minute = cal.get(Calendar.MINUTE);
  	    	if (minute != 0) {
  	    		minute = (minute / freq) * freq;
  	    		cal.set(Calendar.MINUTE, minute);
  	    		cal.set(Calendar.SECOND, 0);
  	    		cal.set(Calendar.MILLISECOND, 0);
  	    	}
  	    	
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
  			while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addMinutes(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  			
  	    	break;
  	    }
  	    case "hourly": {
      		cal.set(Calendar.MINUTE, 0);
      		cal.set(Calendar.SECOND, 0);
      		cal.set(Calendar.MILLISECOND, 0);
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
  			while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addHours(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  	    	break;
  	    }
  	    case "daily": {
  	    	cal.set(Calendar.HOUR_OF_DAY, 0);
      		cal.set(Calendar.MINUTE, 0);
      		cal.set(Calendar.SECOND, 0);
      		cal.set(Calendar.MILLISECOND, 0);
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
      		while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addDays(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  	    	break;
  	    }
  	    case "monthly": {
  	    	cal.set(Calendar.DAY_OF_MONTH, 1);
  	    	cal.set(Calendar.HOUR_OF_DAY, 0);
      		cal.set(Calendar.MINUTE, 0);
      		cal.set(Calendar.SECOND, 0);
      		cal.set(Calendar.MILLISECOND, 0);
      		Date tmpDate= cal.getTime();
      		timeList.add(tmpDate);	
      		while(!tmpDate.after(endTime)) {
  				tmpDate=org.apache.commons.lang.time.DateUtils.addMonths(tmpDate, freq);
  				if(!tmpDate.after(endTime)) {
  					timeList.add(tmpDate);	
  				}
  			}
  	    	break;
  	    }
      	default:
      		break;
  	    }
  	    //分钟2160条 小时1080 月36条 日90条
  	    switch (freqType) {
  	    case "minutely": {
  	    	
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 100){
  	    			int count = timeList.size() - 100;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    case "hourly": {
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 100){
  	    			int count = timeList.size() - 100;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    case "daily": {
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 100){
  	    			int count = timeList.size() - 100;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    case "monthly": {
  	    	if(timeList != null && timeList.size() > 0){
  	    		if(timeList.size() > 100){
  	    			int count = timeList.size() - 100;
  	    			for(Date tmpDate : timeList ){
  	    				if(count > 0){
  	    					count--;
  	    					continue;
  	    				}else{
  	    					timeListNew.add(tmpDate);
  	    				}
  	    			}
  	    		}else{
  	    			timeListNew =timeList;
  	    		}
  	    		
  	    	}
  	    	break;
  	    }
  	    default:
      		break;
  	    }
  	  //  timeListNew
  	    Collections.reverse(timeListNew); // 倒序排列 
  		return timeListNew;   
  	}
    	 
      

}

