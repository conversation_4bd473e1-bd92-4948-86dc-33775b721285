/*********************************************************************************************************
 * Copyright (c) 2017,深圳科陆电子科技股份有限公司
 * All rights reserved.
 *
 * 文件名称:public class AssetReportRoleMap{ } 
 * 
 * 摘    要： 
 * 版    本：1.0
 * 作    者：罗佳
 * 创建于：2020-06-23 04:02:06
 * 最后修改时间：
 * 
 *********************************************************************************************************/
package com.clou.esp.hes.app.web.dao.report;

import com.clou.esp.hes.app.web.core.annotation.MyBatisDao;
import com.clou.esp.hes.app.web.dao.common.CrudDao;
import com.clou.esp.hes.app.web.model.report.AssetReportRoleMap;

import java.util.List;

@MyBatisDao
public interface AssetReportRoleMapDao extends CrudDao<AssetReportRoleMap> {
	public int batchInsert(List<AssetReportRoleMap> cList);

}