package com.clou.esp.hes.app.web.core.interceptors;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.clou.esp.hes.app.web.core.shiro.token.manager.TokenManager;
import com.clou.esp.hes.app.web.core.util.Globals;
import com.clou.esp.hes.app.web.enums.system.EnumUserType;
import com.clou.esp.hes.app.web.model.system.SysUser;

/**
 * 数据权限拦截器
 * 
 * <AUTHOR>
 * 
 */
public class DataAuthInterceptor implements HandlerInterceptor {

	private static final Logger logger = Logger
			.getLogger(DataAuthInterceptor.class);
	private List<String> includeUrls;

	public List<String> getIncludeUrls() {
		return includeUrls;
	}

	public void setIncludeUrls(List<String> includeUrls) {
		this.includeUrls = includeUrls;
	}

	/**
	 * 在controller后拦截
	 */
	public void afterCompletion(HttpServletRequest request,
			HttpServletResponse response, Object object, Exception exception)
			throws Exception {

	}

	public void postHandle(HttpServletRequest request,
			HttpServletResponse response, Object object,
			ModelAndView modelAndView) throws Exception {

	}

	/**
	 * 在controller前拦截
	 */
	public boolean preHandle(HttpServletRequest request,
			HttpServletResponse response, Object object) throws Exception {
		// String requestPath = ResourceUtil.getRequestPath(request);//
		// 用户访问的资源地址
		// if (!includeUrls.contains(requestPath)) {
		// return true;
		// }
		// 获取当前登录操作员
		SysUser sysUser = TokenManager.getToken();
		// 查询数据权限表；还未做
		if (sysUser == null) {
			return true;
		}
		Integer userType = sysUser.getUserType();
		if (userType != EnumUserType.SUPER_USER.getIndex()) {// 超级管理员
			request.setAttribute(Globals.REQUESST_DATA_AUTH_VENDOR_ID,
					sysUser.getUtilityId());
			if (userType != EnumUserType.SYSTEM_USER.getIndex()) {// 超级管理员
				request.setAttribute(Globals.REQUESST_DATA_AUTH_ORG_ID,
						sysUser.getOrgId());
			}
		}
		return true;
	}
}
