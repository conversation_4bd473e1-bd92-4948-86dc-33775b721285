package com.clou.esp.hes.app.web.core.pushlet;

import nl.justobjects.pushlet.core.Event;
import nl.justobjects.pushlet.core.Session;
import nl.justobjects.pushlet.core.SessionManager;
import nl.justobjects.pushlet.util.PushletException;

public class MySessionManager extends SessionManager {
	
	@Override  
    public Session createSession(Event anEvent) throws PushletException {  
        // TODO Auto-generated method stub  
		String subject=anEvent.getSubject();
		String userId = anEvent.getField("userId"); // 获取userId参数
		if(null==userId || userId.length()==0 )
		{
			return Session.create(createSessionId());
		}
		String sessionId = userId + "_" + createSessionId();    // 构造sessionId
        OnLineClient.addClient(userId+"_"+subject, sessionId);  // 记录在线用户
        return Session.create(sessionId);  
    }  
	
	@Override
	public Session removeSession(Session aSession) {
		if(null==aSession){
			return null;
		}
		OnLineClient.removeClient(aSession.getId());
		return super.removeSession(aSession);
	}

}