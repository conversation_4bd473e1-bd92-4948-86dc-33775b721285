package com.clou.esp.hes.app.web.core.tag.customui;

import java.io.IOException;

import javax.servlet.jsp.JspTagException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;

import com.clou.esp.hes.app.web.core.util.MutiLangUtil;
import com.power7000g.core.util.base.StringUtil;
import com.power7000g.core.util.uuid.UUIDGenerator;

/**
 * 选择器标签
 * 
 * <AUTHOR>
 * 
 */
public class ChooseTag extends TagSupport {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	protected String hiddenName;// 弹出的分页显示取值name
	protected String hiddenid;// 弹出的分页显示取值ID
	protected String textname;// 显示文本框字段
	protected String textid;// 显示文本框框ID字段
	protected String icon;// 显示图标,，暂时无用
	protected String width;// 弹出框宽度px
	protected String height;// 弹出框高度px
	protected String top;// 距离上边框px
	protected String left;// 距离左边款px
	protected String url;// 弹出框地址
	protected String title;// 弹出框标题
	protected String fun;// 自定义函数
	protected String inputId;// inputId，弹出页面的jqueryID，或者treeId
	protected String fatherId;//联动上级ID
	protected String fatherErrMsg; //上级联动提示信息

	protected String chooseStyle;// 选择按钮样式，pageQuery，form，默认是pageQuery
	protected String chooseType;// ，page，分页；tree树形结构；

	public int doStartTag() throws JspTagException {
		return EVAL_PAGE;
	}

	public int doEndTag() throws JspTagException {
		JspWriter out = null;
		title=MutiLangUtil.doMutiLang(title);
		fatherErrMsg=MutiLangUtil.doMutiLang(fatherErrMsg);
		try {
			out = this.pageContext.getOut();
			out.print(end().toString());
			out.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return EVAL_PAGE;
	}

	public StringBuffer end() {
		StringBuffer sb = new StringBuffer();
		// 先拼接html
		// chooseStyle;// 选择按钮样式，pageQuery，form，默认是pageQuery
		String methodname = UUIDGenerator.generate().replaceAll("-", "");
		if (StringUtil.isEmpty(chooseStyle)) {
			chooseStyle = "pageQuery";
		}
		if ("pageQuery".equals(chooseStyle)) {
			// 等下拼接
			sb.append("<span class=\"input-group-btn\">");
			sb.append("<button class=\"btn btn-default\" type=\"button\" onclick=\"choose_"
					+ methodname
					+ "()\"><i class=\"fa fa-search\"></i></button>");
			sb.append("<button class=\"btn btn-default\" type=\"button\" onclick=\"clearAll_"
					+ methodname
					+ "()\"><i class=\"fa fa-close\"></i></button>");
			sb.append("</span>");

		} else {// form
			sb.append("<button type=\"button\" onclick=\"choose_" + methodname
					+ "()\" data-i18n=\"system.public.choose\" >选择</button>");
			sb.append("<button type=\"button\" onclick=\"clearAll_"
					+ methodname
					+ "()\" data-i18n=\"system.public.clear\" >清空</button>");
		}
		// 再拼接js
		sb.append("<script type=\"text/javascript\">");
		sb.append("function choose_" + methodname + "(){");

		sb.append("var title =i18n.t(\"" + title + "\");");
		sb.append("var confirm =i18n.t(\"system.determine\");");
		sb.append("var cancel =i18n.t(\"system.cancel\");");
		
		if(StringUtil.isNotEmpty(fatherId)){
			sb.append("var "+fatherId+" = $(\"#"+fatherId+"\").val();");
			if(StringUtil.isNotEmpty(fatherErrMsg)){
				sb.append("	if ("+fatherId+" == null || "+fatherId+" == '') {");
				sb.append("	window.parent.layer.msg(\""+fatherErrMsg+"\", {");
				sb.append("		icon : 2");
				sb.append("	});");
				sb.append("	return;");
				sb.append("}");
			}
		}
		
		sb.append("if(title==null||title==''){");
		sb.append("title=\"" + title + "\";");
		sb.append("}");
		sb.append("if(confirm==null||confirm==''){");
		sb.append("confirm=\"OK\";");
		sb.append("}");
		sb.append("if(cancel==null||cancel==''){");
		sb.append("cancel=\"Cancel\";");
		sb.append("}");

		sb.append("var close_link = window.parent.layer.open({");
		sb.append("type: 2,");
		sb.append("title: title,");
		sb.append("shadeClose: false,");
		sb.append("shade: 0.7,");
		sb.append("btn: [confirm, cancel],");
		sb.append("maxmin: true,");
		if (StringUtil.isNotEmpty(width) && StringUtil.isNotEmpty(height)) {
			sb.append("area: ['" + width + "', '" + height + "'],");
		}
		if (StringUtil.isNotEmpty(top) && StringUtil.isNotEmpty(left)) {
			sb.append("offset: ['" + top + "px', '" + left + "px'],");
		}
		if(StringUtil.isNotEmpty(fatherId)){
			if(url.contains("?")){
				sb.append("content: '" + url +"&"+fatherId+"='+"+fatherId+",");
			}else{
				sb.append("content: '" + url +"?"+fatherId+"='+"+fatherId+",");
			}
		}else{
			sb.append("content: '" + url +"',");
		}
		sb.append("success: function(layero, index){");
		sb.append("},");
		// 确定按钮回调
		sb.append("yes: function(index, layero){");
		if (StringUtil.isNotEmpty(fun)) {
			sb.append(fun + "(layero, index);");
		} else {
			sb.append("callback_" + methodname + "(layero, index);");
		}
		sb.append("window.parent.layer.close(close_link);");
		sb.append("}");

		sb.append("});");
		sb.append("$('.close-link').click(function() {");
		sb.append("window.parent.layer.close(close_link);");
		sb.append("});");
		sb.append("}");
		clearAll(sb, methodname);
		callback(sb, methodname);
		sb.append("</script>");
		return sb;
	}

	/**
	 * 清除
	 * 
	 * @param sb
	 */
	private void clearAll(StringBuffer sb, String methodname) {
		sb.append("function clearAll_" + methodname + "(){");
		sb.append("$(\'#" + textname + "\').val(\"\");");
		sb.append("$(\'#" + textid + "\').val(\"\");");
		sb.append("}");
	}

	/**
	 * 点击确定回填
	 * 
	 * @param sb
	 */
	private void callback(StringBuffer sb, String methodname) {
		// 分页callback函数
		sb.append("function chosePageCallback_" + methodname
				+ "(layero, index){");
		sb.append("var body = window.parent.layer.getChildFrame('body',index);");

		sb.append("var jqgrd = $(body.find('#" + inputId + "'));");

		sb.append("var ids = $(jqgrd).jqGrid(\"getGridParam\", \"selarrrow\");");
		sb.append("if (ids.length > 1) {");
		sb.append("var rowNames = \"\";");
		sb.append("var rowIds = \"\";");
		sb.append("for (var i = 1; i <= ids.length; i++) {");
		sb.append("var rowData = $(jqgrd).jqGrid(\"getRowData\", ids[i-1]);");
		sb.append("var rowName = rowData." + hiddenName + ";");
		sb.append("var rowId = rowData." + hiddenid + ";");
		sb.append("rowNames += (rowName + \",\");");
		sb.append("rowIds += (rowId + \",\");");
		sb.append("}");
		sb.append("rowNames = rowNames.substring(0, rowNames.length - 1);");
		sb.append("rowIds = rowIds.substring(0, rowIds.length - 1);");
		sb.append("$(\"#" + textname + "\").val(rowNames);");
		sb.append("$(\"#" + textid + "\").val(rowIds);");
		sb.append("} else {");
		sb.append("var rowid = $(jqgrd).jqGrid(\"getGridParam\", \"selrow\");");
		sb.append("var rowData = $(jqgrd).jqGrid(\"getRowData\", rowid);");
		sb.append("$(\"#" + textname + "\").val(rowData." + hiddenName + ");");
		sb.append("$(\"#" + textid + "\").val(rowData." + hiddenid + ");");
		sb.append("}");
		sb.append("}");
		// Tree callback函数
		sb.append("function choseTreeCallback_" + methodname
				+ "(layero, index){");
		sb.append("var iframe = $(layero).find('iframe')[0].contentWindow;");
		sb.append("var treeObj =iframe.$.fn.zTree.getZTreeObj(\"" + inputId
				+ "\");");
		sb.append("var nodes = treeObj.getCheckedNodes(true);");
		sb.append("if (nodes.length > 0) {");
		sb.append("var ids = '', names = '';");
		sb.append("for (i = 0; i < nodes.length; i++) {");
		sb.append("var node = nodes[i];");
		sb.append("ids += node." + hiddenid + " + ',';");
		sb.append("names += node." + hiddenName + " + ',';");
		sb.append("}");
		sb.append("}");
		sb.append("ids = ids.substring(0, ids.length - 1);");
		sb.append("names = names.substring(0, names.length - 1);");
		sb.append("$(\"#" + textid + "\").val(ids);");
		sb.append("$(\"#" + textname + "\").val(names);");

		sb.append("}");
		sb.append("function callback_" + methodname + "(layero, index){");
		if (StringUtil.isEmpty(chooseType)) {
			chooseType = "page";
		}
		if ("page".equals(chooseType)) {
			sb.append("chosePageCallback_" + methodname + "(layero, index);");
		} else {// tree
			sb.append("choseTreeCallback_" + methodname + "(layero, index);");
		}
		sb.append("}");
	}

	public void setHiddenName(String hiddenName) {
		this.hiddenName = hiddenName;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public void setTextname(String textname) {
		this.textname = textname;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public void setTop(String top) {
		this.top = top;
	}

	public void setLeft(String left) {
		this.left = left;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public void setHiddenid(String hiddenid) {
		this.hiddenid = hiddenid;
	}

	public void setFun(String fun) {
		this.fun = fun;
	}

	public String getTextid() {
		return textid;
	}

	public void setTextid(String textid) {
		this.textid = textid;
	}

	public String getHiddenName() {
		return hiddenName;
	}

	public String getHiddenid() {
		return hiddenid;
	}

	public String getTextname() {
		return textname;
	}

	public String getIcon() {
		return icon;
	}

	public String getChooseStyle() {
		return chooseStyle;
	}

	public String getChooseType() {
		return chooseType;
	}

	public String getTitle() {
		return title;
	}

	public String getUrl() {
		return url;
	}

	public String getTop() {
		return top;
	}

	public String getLeft() {
		return left;
	}

	public String getWidth() {
		return width;
	}

	public String getHeight() {
		return height;
	}

	public String getFun() {
		return fun;
	}

	public String getInputId() {
		return inputId;
	}

	public void setChooseStyle(String chooseStyle) {
		this.chooseStyle = chooseStyle;
	}

	public void setChooseType(String chooseType) {
		this.chooseType = chooseType;
	}

	public void setInputId(String inputId) {
		this.inputId = inputId;
	}

	public String getFatherId() {
		return fatherId;
	}

	public void setFatherId(String fatherId) {
		this.fatherId = fatherId;
	}

	public String getFatherErrMsg() {
		return fatherErrMsg;
	}

	public void setFatherErrMsg(String fatherErrMsg) {
		this.fatherErrMsg = fatherErrMsg;
	}

	
	
	

}
