package com.clou.esp.hes.app.web.core.tag.vo;

/**
 * 文件名：FindListExp.java
 * 版权：Copyright by Power7000g Team
 * 描述：字段过滤按钮实体
 * 修改人：严浪
 * 修改时间：2017年3月23日
 * 跟踪单号：
 * 修改单号：
 * 修改内容：
 */
public class FindListExp {
    //字段名称
    private String field;
    //表达式
    private String expression;
    //对比值
    private String value;
    
    
    
    public FindListExp(String field, String expression, String value) {
        super();
        this.field = field;
        this.expression = expression;
        this.value = value;
    }
    public FindListExp() {
        super();
        // TODO Auto-generated constructor stub
    }
    public String getField() {
        return field;
    }
    public String getExpression() {
        switch (expression) {
            case "eq":
                return "==";
            case "ne":
                return "!=";
            case "empty":
                setValue("");
                return "==null";
            default:
                return "==";
        }
    }
    public String getValue() {
        return value;
    }
    public void setField(String field) {
        this.field = field;
    }
    public void setExpression(String expression) {
        this.expression = expression;
    }
    public void setValue(String value) {
        this.value = value;
    }
    @Override
    public String toString() {
        return "FindListExp [field=" + field + ", expression=" + expression + ", value=" + value
               + "]";
    }
    
    

}
